"""
动作奖励评分函数

该模块提供了一个用于从预先计算的动作奖励中提取分数的奖励函数。
它能够从模型回答中提取导航动作，并查询预先计算好的奖励字典，获取对应的奖励值。
适用于与process_numeric_dataset_with_rewards.py脚本生成的数据集配合使用。

使用方法:
1. 在Swift框架中作为奖励函数使用:
   ```python
   from action_reward_score import ActionRewardScore

   # 创建实例
   action_reward = ActionRewardScore()

   # 计算奖励
   rewards = action_reward(model_outputs, correct_answers)
   ```

2. 在训练脚本中指定为外部插件:
   ```bash
   swift rlhf \
   --rlhf_type grpo \
   --external_plugins /path/to/action_reward_score.py \
   # 其他参数...
   ```

"""

import os
import re
import json
from typing import Dict, List, Union, Optional, Tuple, Any

from swift.plugin.orm import ORM

# 注册奖励函数的全局字典
orms = {}


class ActionRewardScore(ORM):
    """
    动作奖励评分函数，用于从预先计算的奖励字典中提取动作得分。

    从模型回答中提取导航动作，并查询预先计算好的奖励字典，获取对应的奖励值。
    适用于与process_numeric_dataset_with_rewards.py脚本生成的数据集配合使用。

    特点:
    1. 支持从<answer></answer>标签中提取动作
    2. 支持从整个内容中提取动作，使用多种策略
    3. 直接使用预先计算好的奖励值，避免重复计算
    4. 完整的日志记录，方便调试

    支持的动作:
    - Move forward
    - Move backward
    - Move left
    - Move right
    - Move up
    - Move down
    - Rotate left
    - Rotate right
    - Look up
    - Look down
    """

    # 定义导航动作列表
    NAVIGATION_ACTIONS = [
        "Move forward",
        "Move backward",
        "Move left",
        "Move right",
        "Move up",
        "Move down",
        "Rotate left",
        "Rotate right",
        "Look up",
        "Look down"
    ]

    # 定义动作的简写或变体映射
    ACTION_VARIANTS = {
        # 英文完整匹配（优先级最高）
        "move forward": "Move forward",
        "move backward": "Move backward",
        "move left": "Move left",
        "move right": "Move right",
        "move up": "Move up",
        "move down": "Move down",
        "rotate left": "Rotate left",
        "rotate right": "Rotate right",
        "look up": "Look up",
        "look down": "Look down",

        # 英文简写匹配（优先级次之）
        "forward": "Move forward",
        "backward": "Move backward",
        "left": "Move left",
        "right": "Move right",
        "up": "Move up",
        "down": "Move down",

        # 其他英文变体
        "tilt up": "Look up",
        "tilt down": "Look down",
        "go forward": "Move forward",
        "go backward": "Move backward",
        "go left": "Move left",
        "go right": "Move right",
        "go up": "Move up",
        "go down": "Move down",
        "turn left": "Rotate left",
        "turn right": "Rotate right",

        # 中文匹配
        "向前": "Move forward",
        "向后": "Move backward",
        "向左": "Move left",
        "向右": "Move right",
        "向上": "Move up",
        "向下": "Move down",
        "左转": "Rotate left",
        "右转": "Rotate right",
        "向上看": "Look up",
        "向下看": "Look down",
        "向前运动": "Move forward",
        "向后运动": "Move backward",
        "向左移动": "Move left",
        "向右移动": "Move right",
        "向上运动": "Move up",
        "向下运动": "Move down",
        "向左旋转": "Rotate left",
        "向右旋转": "Rotate right"
    }

    # 定义用于从回答中提取动作的正则表达式模式
    ANSWER_TAG_PATTERN = r"<answer>(.*?)</answer>"

    # 定义用于从文本中提取动作的正则表达式模式
    ACTION_PATTERNS = [
        r"I choose\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"My choice is\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"I select\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"The best action is\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"I would\s+(?:choose\s+)?(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"我选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"最佳动作是[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"我会选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"应该[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?"
    ]

    # 定义用于从文本中提取最后一行或最后一句话的正则表达式模式
    LAST_LINE_PATTERN = r"(?:^|\n)([^\n]+)$"
    LAST_SENTENCE_PATTERN = r"[.!?。！？]\s*([^.!?。！？]+)[.!?。！？]?$"

    def __init__(self):
        """
        初始化动作奖励评分函数
        """
        pass

    def extract_action_from_answer_tag(self, content: str) -> str:
        """
        从<answer></answer>标签中提取动作

        Args:
            content: 包含答案的文本

        Returns:
            提取的动作文本
        """
        # 查找<answer>标签中的内容
        answer_matches = re.findall(self.ANSWER_TAG_PATTERN, content, re.DOTALL | re.IGNORECASE)
        if answer_matches:
            # 返回第一个匹配的答案内容，去除首尾空白
            answer_content = answer_matches[0].strip()

            # 检查<answer>标签中是否直接包含标准动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in answer_content.lower():
                    # 如果包含标准动作，直接返回该动作
                    return action

            # 如果没有找到标准动作，返回原始内容
            return answer_content
        return ""

    def extract_action_from_content(self, content: str) -> Tuple[str, str]:
        """
        从整个内容中提取动作，使用多种策略

        Args:
            content: 包含答案的文本

        Returns:
            提取的动作文本和提取方法描述
        """
        # 策略1: 使用预定义的模式直接匹配
        for pattern in self.ACTION_PATTERNS:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[-1], "预定义模式匹配"

        # 策略2: 检查最后一行是否包含动作
        last_line_match = re.search(self.LAST_LINE_PATTERN, content)
        if last_line_match:
            last_line = last_line_match.group(1).strip()
            # 检查最后一行是否只包含一个动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in last_line.lower() and len(last_line) < len(action) + 10:
                    return last_line, "最后一行匹配"

        # 策略3: 检查最后一句话是否包含动作
        last_sentence_match = re.search(self.LAST_SENTENCE_PATTERN, content)
        if last_sentence_match:
            last_sentence = last_sentence_match.group(1).strip()
            # 检查最后一句话是否只包含一个动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in last_sentence.lower():
                    return last_sentence, "最后一句话匹配"

        # 策略4: 直接在整个内容中查找标准动作
        for action in self.NAVIGATION_ACTIONS:
            if action.lower() in content.lower():
                # 找到动作周围的上下文
                action_index = content.lower().find(action.lower())
                start = max(0, action_index - 20)
                end = min(len(content), action_index + len(action) + 20)
                context = content[start:end]
                return context, "整个内容匹配"

        # 策略5: 在整个内容中查找动作变体
        for variant, standard_action in self.ACTION_VARIANTS.items():
            if variant.lower() in content.lower():
                # 找到变体周围的上下文
                variant_index = content.lower().find(variant.lower())
                start = max(0, variant_index - 20)
                end = min(len(content), variant_index + len(variant) + 20)
                context = content[start:end]
                return context, "变体匹配"

        # 如果所有策略都失败，返回整个内容
        return content, "无匹配，使用整个内容"

    def normalize_action(self, action) -> str:
        """
        标准化动作格式，匹配到预定义的导航动作
        
        Args:
            action: 包含动作的文本，可以是字符串或其他类型

        Returns:
            标准化后的动作文本
        """
        # 处理非字符串类型
        if not action:
            return ""

        # 如果是列表，递归处理第一个元素
        if isinstance(action, list):
            if len(action) > 0:
                return self.normalize_action(action[0])
            else:
                return ""

        # 如果不是字符串，尝试转换为字符串
        if not isinstance(action, str):
            try:
                action = str(action)
            except:
                return ""

        # 去除首尾空白并转为小写以便比较
        action_lower = action.strip().lower()

        # 1. 直接检查完整匹配（最高优先级）
        for nav_action in self.NAVIGATION_ACTIONS:
            if nav_action.lower() == action_lower:
                return nav_action

        # 2. 检查是否包含完整的动作短语（次高优先级）
        # 例如："I should look up" 应该匹配 "Look up" 而不是 "Move up"
        exact_matches = []
        for nav_action in self.NAVIGATION_ACTIONS:
            if nav_action.lower() in action_lower:
                # 记录匹配的动作和它在文本中的位置
                exact_matches.append((nav_action, action_lower.find(nav_action.lower())))

        # 如果找到完整匹配，返回最前面出现的那个
        if exact_matches:
            # 按照出现位置排序，返回最前面出现的
            exact_matches.sort(key=lambda x: x[1])
            return exact_matches[0][0]

        # 3. 检查动作变体映射（第三优先级）
        # 先检查较长的变体，避免错误匹配
        # 例如："look up" 应该匹配 "Look up" 而不是 "Move up"
        variant_matches = []
        for variant, standard_action in sorted(self.ACTION_VARIANTS.items(), key=lambda x: -len(x[0])):
            if variant.lower() in action_lower:
                # 记录匹配的变体、对应的标准动作和它在文本中的位置
                variant_matches.append((variant, standard_action, action_lower.find(variant.lower())))

        # 如果找到变体匹配，返回最长的那个变体对应的标准动作
        if variant_matches:
            # 按照变体长度和出现位置排序，优先选择较长的变体和较后出现的
            variant_matches.sort(key=lambda x: (len(x[0]), x[2]))
            return variant_matches[-1][1]

        # 如果没有找到匹配项，返回原始文本
        return action

    def __call__(self, completions, solution, **kwargs) -> List[float]:
        """
        评估模型回答并从预先计算的奖励字典中提取奖励值

        Args:
            completions: 模型生成的回答列表
            solution: 正确答案列表
            kwargs: 其他参数，包括action_reward字典

        Returns:
            奖励列表
        """
        rewards = []
        debug = kwargs.get('debug', False)  # 是否打印调试信息

        # 确保 completions 和 solution 都是列表
        if not isinstance(completions, list):
            completions = [completions]

        if not isinstance(solution, list):
            solution = [solution] * len(completions)

        # 如果debug模式，打印可用的字段
        if debug:
            print(f"可用字段: {list(kwargs.keys())}")

        # 从kwargs中提取action_reward字段
        action_reward_dict = {}
        
        if 'action_reward' in kwargs:
            action_reward_dict = kwargs['action_reward']
            if debug:
                print(f"从kwargs中提取到action_reward，类型: {type(action_reward_dict)}")
        elif 'sample' in kwargs and isinstance(kwargs['sample'], dict) and 'action_reward' in kwargs['sample']:
            action_reward_dict = kwargs['sample']['action_reward']
            if debug:
                print(f"从sample中提取到action_reward，类型: {type(action_reward_dict)}")
        elif 'message' in kwargs and isinstance(kwargs['message'], dict) and 'action_reward' in kwargs['message']:
            action_reward_dict = kwargs['message']['action_reward']
            if debug:
                print(f"从message中提取到action_reward，类型: {type(action_reward_dict)}")

        # 处理字符串格式的action_reward
        if isinstance(action_reward_dict, str):
            try:
                action_reward_dict = json.loads(action_reward_dict)
                if debug:
                    print(f"解析字符串格式的action_reward成功，类型: {type(action_reward_dict)}")
            except Exception as e:
                if debug:
                    print(f"解析字符串格式的action_reward失败: {e}")
                action_reward_dict = {}

        # 打印所有奖励字段，方便调试
        action_reward_dict = self.process_action_rewards(action_reward_dict, debug=debug)

        for content, sol in zip(completions, solution):
            try:
                # 确保内容是字符串
                if not isinstance(content, str):
                    content = str(content)
                if not isinstance(sol, str):
                    sol = str(sol)

                # 首先尝试从<answer>标签中提取动作
                extracted_action = self.extract_action_from_answer_tag(content)
                extraction_method = "<answer>标签提取"

                # 如果没有从<answer>标签中提取到动作，则尝试从整个内容中提取
                if not extracted_action:
                    extracted_action, extraction_method = self.extract_action_from_content(content)

                # 标准化提取的动作和正确答案
                normalized_content = self.normalize_action(extracted_action)
                normalized_solution = self.normalize_action(sol)

                # 默认奖励值（如果在action_reward中找不到）
                reward = 0.0

                # 检查动作是否在奖励字典中
                if normalized_content in action_reward_dict:
                    reward = action_reward_dict[normalized_content]
                    if debug:
                        print(f"从action_reward字典中找到动作 '{normalized_content}' 的奖励值: {reward}")
                else:
                    if debug:
                        print(f"动作 '{normalized_content}' 不在action_reward字典中")
                        # 输出字典的所有键，帮助调试
                        if action_reward_dict:
                            print(f"action_reward字典中的键: {list(action_reward_dict.keys())}")
                    
                    # 如果动作正确，给予满分奖励
                    if normalized_content == normalized_solution:
                        reward = 1.0
                        if debug:
                            print(f"动作正确匹配，给予满分奖励: {reward}")
                    else:
                        reward = 0.0
                        if debug:
                            print(f"动作不匹配，给予零分: {reward}")

                # 打印调试信息
                if debug:
                    print(f"提取方法: {extraction_method}")
                    print(f"原始回答: {extracted_action}")
                    print(f"标准化后模型答案: {normalized_content}")
                    print(f"正确答案: {normalized_solution}")
                    print(f"奖励分数: {reward}")
                    print("-" * 50)

            except Exception as e:
                print(f"处理答案时发生错误: {e}")
                import traceback
                traceback.print_exc()
                reward = 0.0  # 发生错误时返回0分

            rewards.append(reward)

        return rewards

    def process_action_rewards(self, action_reward_data, debug=True) -> Dict[str, float]:
        """
        处理action_reward数据，确保返回单个字典格式
        
        Args:
            action_reward_data: 动作奖励数据，可能是字典、列表或其他格式
            debug: 是否打印调试信息
            
        Returns:
            处理后的动作奖励字典
        """
        if not action_reward_data:
            if debug:
                print("没有找到action_reward数据或数据为空")
            return {}
            
        if debug:
            print("\n" + "="*50)
            print(f"处理ACTION_REWARD数据，类型: {type(action_reward_data)}")
            print("="*50)
        
        result_dict = {}
        
        # 处理列表类型的action_reward_data
        if isinstance(action_reward_data, list):
            if debug:
                print(f"发现列表类型的action_reward，共有{len(action_reward_data)}个元素")
                
            # 使用第一个非空字典元素
            for i, item in enumerate(action_reward_data):
                if isinstance(item, dict) and item:
                    if debug:
                        print(f"使用第{i+1}个元素作为奖励字典")
                        for action, reward in item.items():
                            print(f"动作: {action:<15} | 奖励值: {reward}")
                    result_dict = item
                    break
            
            # 如果没有找到非空字典，尝试合并所有字典
            if not result_dict:
                if debug:
                    print("未找到非空字典元素，尝试合并所有字典")
                for i, item in enumerate(action_reward_data):
                    if isinstance(item, dict):
                        result_dict.update(item)
        
        # 处理字典类型的action_reward_data
        elif isinstance(action_reward_data, dict):
            result_dict = action_reward_data
            if debug:
                print("使用字典类型的action_reward")
                for action, reward in result_dict.items():
                    print(f"动作: {action:<15} | 奖励值: {reward}")
        
        # 其他类型，尝试转换为字典
        else:
            if debug:
                print(f"未知类型的action_reward: {type(action_reward_data)}")
                print(f"内容: {action_reward_data}")
            try:
                if hasattr(action_reward_data, '__dict__'):
                    result_dict = action_reward_data.__dict__
                elif hasattr(action_reward_data, 'items'):
                    result_dict = dict(action_reward_data.items())
            except Exception as e:
                if debug:
                    print(f"转换为字典失败: {e}")
        
        if debug:
            print(f"最终处理结果: 包含 {len(result_dict)} 个动作奖励")
            print("="*50 + "\n")
        
        return result_dict

    def print_action_rewards(self, action_reward_dict, debug=True):
        """
        打印action_reward字典中的所有内容
        
        Args:
            action_reward_dict: 动作奖励字典或列表
            debug: 是否打印调试信息
        """
        if not action_reward_dict:
            print("没有找到action_reward字典或字典为空")
            return
            
        print("\n" + "="*50)
        print("ACTION_REWARD字段内容:")
        print("="*50)
        
        # 处理列表类型的action_reward_dict
        if isinstance(action_reward_dict, list):
            print(f"发现列表类型的action_reward，共有{len(action_reward_dict)}个元素")
            for i, reward_dict in enumerate(action_reward_dict):
                if isinstance(reward_dict, dict):
                    print(f"\n--- 第{i+1}个奖励字典 ---")
                    for action, reward in reward_dict.items():
                        print(f"动作: {action:<15} | 奖励值: {reward}")
                else:
                    print(f"\n--- 第{i+1}个元素(非字典类型) ---")
                    print(f"类型: {type(reward_dict)}")
                    print(f"内容: {reward_dict}")
        # 处理字典类型的action_reward_dict
        elif isinstance(action_reward_dict, dict):
            # 格式化打印每个动作及其奖励值
            for action, reward in action_reward_dict.items():
                print(f"动作: {action:<15} | 奖励值: {reward}")
        else:
            print(f"未知类型的action_reward: {type(action_reward_dict)}")
            print(f"内容: {action_reward_dict}")
            
        print("="*50 + "\n")


# 注册奖励函数
orms['action_reward_score'] = ActionRewardScore

# 为了兼容性，也注册到swift的orms中
try:
    from swift.plugin.orm import orms as swift_orms
    swift_orms['action_reward_score'] = ActionRewardScore
except ImportError:
    pass 