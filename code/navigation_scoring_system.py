#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
无人机导航评分系统

该模块提供了一个用于评估无人机导航轨迹质量的评分系统。
它基于三个维度对模型生成的导航轨迹进行评分：
1. 终点关系评分：使用奖励2和奖励3机制评估当前位置执行模型选择的动作对到达终点的理论影响
2. 轨迹关系评分：找到基准轨迹中与当前位置最近的点，取连续5个点的平均位置作为"轨迹参考点"，
   使用与终点关系评分相同的奖励2和奖励3机制，但将终点替换为轨迹参考点
3. 导航效率评分：评估导航成功率和步数效率

使用方法:
```bash
python navigation_scoring_system.py --task_id 331 --output_file scores.json
```

作者: AI助手
日期: 2023-11-20
"""

import os
import json
import math
import argparse
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import csv
import glob

# 动作编号对照表
COMMANDS_MAP = {
    6: ('向前运动', (10, 0, 0)),
    7: ('向后运动', (-10, 0, 0)),
    8: ('向左运动', (0, -10, 0)),
    9: ('向右运动', (0, 10, 0)),
    10: ('向上运动', (0, 0, -10)),
    11: ('向下运动', (0, 0, 10)),
    2: ('向左旋转', -22.5),
    3: ('向右旋转', 22.5),
    4: ('向上看', 45),
    5: ('向下看', -45)
}

# 对立动作映射
OPPOSITE_ACTIONS = {
    "向前运动": "向后运动",
    "向后运动": "向前运动",
    "向左运动": "向右运动",
    "向右运动": "向左运动",
    "向上运动": "向下运动",
    "向下运动": "向上运动",
    "向左旋转": "向右旋转",
    "向右旋转": "向左旋转",
    "向上看": "向下看",
    "向下看": "向上看"
}


def load_coordinates(file_path: str) -> List[np.ndarray]:
    """
    加载坐标文件

    Args:
        file_path: 坐标文件路径

    Returns:
        坐标列表，每个元素是一个numpy数组
    """
    coordinates = []
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) >= 2:
                # 解析坐标字符串为numpy数组
                coord_str = row[1].strip()
                # 移除开头的 "[" 和结尾的 "]"
                coord_str = coord_str.strip('[]')
                # 分割并转换为浮点数
                coord_values = [float(x.strip()) for x in coord_str.split()]
                coordinates.append(np.array(coord_values))
    return coordinates


def load_actions(file_path: str) -> List[str]:
    """
    加载动作文件

    Args:
        file_path: 动作文件路径

    Returns:
        动作列表
    """
    actions = []
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) >= 2:
                # 第二列是动作名称或编号
                action = row[1].strip()
                # 如果是数字，转换为动作名称
                if action.isdigit() and int(action) in COMMANDS_MAP:
                    action = COMMANDS_MAP[int(action)][0]
                actions.append(action)
    return actions


def calculate_reward_2(current_loc: np.ndarray, action_loc: np.ndarray, target_loc: np.ndarray) -> float:
    """
    计算奖励2：评估动作对接近目标的贡献

    Args:
        current_loc: 当前位置坐标
        action_loc: 执行动作后的坐标
        target_loc: 目标位置坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标（前三维）
    pos_current = current_loc[:3]
    pos_action = action_loc[:3]
    pos_target = target_loc[:3]

    # 计算当前位置到目标的距离
    dist_current_to_target = np.linalg.norm(pos_target - pos_current)

    # 计算执行动作后位置到目标的距离
    dist_action_to_target = np.linalg.norm(pos_target - pos_action)

    # 计算距离变化
    dist_change = dist_current_to_target - dist_action_to_target

    # 归一化为[-1,1]区间
    if dist_change > 0:  # 距离减小，接近目标
        reward = min(dist_change / 10.0, 1.0)  # 归一化，最大值为1
    else:  # 距离增加，远离目标
        reward = max(dist_change / 10.0, -1.0)  # 归一化，最小值为-1

    return reward


def calculate_reward_3(current_loc: np.ndarray, action_loc: np.ndarray, target_loc: np.ndarray) -> float:
    """
    计算奖励3：评估动作朝向与目标方向的一致性

    Args:
        current_loc: 当前位置坐标
        action_loc: 执行动作后的坐标
        target_loc: 目标位置坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标（前三维）
    pos_current = current_loc[:3]
    pos_action = action_loc[:3]
    pos_target = target_loc[:3]

    # 计算B：当前朝向向量（由偏航角和俯仰角共同确定）
    yaw = current_loc[5]    # 偏航角（水平面内的旋转，弧度制）
    pitch_deg = current_loc[6]  # 俯仰角（垂直方向的旋转，度数制）

    # 将俯仰角从度数转换为弧度
    pitch_rad = (pitch_deg / 180.0) * math.pi

    # 使用球坐标系计算三维朝向向量
    # 注意：我们反转Z轴分量的符号，使其与位置坐标系统一致（Z轴向下为正）
    current_direction = np.array([
        math.cos(pitch_rad) * math.cos(yaw),
        math.cos(pitch_rad) * math.sin(yaw),
        -math.sin(pitch_rad)  # 反转Z轴方向
    ])  # B

    # 计算E：动作后朝向向量（由动作后的偏航角和俯仰角共同确定）
    action_yaw = action_loc[5]    # 动作后的偏航角（弧度制）
    action_pitch_deg = action_loc[6]  # 动作后的俯仰角（度数制）

    # 将动作后的俯仰角从度数转换为弧度
    action_pitch_rad = (action_pitch_deg / 180.0) * math.pi

    action_direction = np.array([
        math.cos(action_pitch_rad) * math.cos(action_yaw),
        math.cos(action_pitch_rad) * math.sin(action_yaw),
        -math.sin(action_pitch_rad)  # 反转Z轴方向
    ])  # E

    # 计算C：当前位置指向目标的向量
    to_target_vector = pos_target - pos_current
    to_target_vector_norm = np.linalg.norm(to_target_vector)
    if to_target_vector_norm < 1e-6:
        return 0.0  # 已非常接近目标
    to_target_direction = to_target_vector / to_target_vector_norm  # C

    # 计算D：动作后位置指向目标的向量
    to_target_vector_after_action = pos_target - pos_action
    to_target_vector_after_action_norm = np.linalg.norm(to_target_vector_after_action)
    if to_target_vector_after_action_norm < 1e-6:
        return 1.0  # 动作后已非常接近目标
    to_target_direction_after_action = to_target_vector_after_action / to_target_vector_after_action_norm  # D

    # 计算B和C的夹角（当前朝向与目标方向的夹角）
    cos_angle_BC = np.dot(current_direction, to_target_direction)
    angle_BC = math.acos(max(min(cos_angle_BC, 1.0), -1.0)) * 180 / math.pi

    # 计算E和D的夹角（动作后朝向与目标方向的夹角）
    cos_angle_ED = np.dot(action_direction, to_target_direction_after_action)
    angle_ED = math.acos(max(min(cos_angle_ED, 1.0), -1.0)) * 180 / math.pi

    # 根据夹角变化计算奖励
    if angle_ED < angle_BC:
        return min(1.0, (angle_BC - angle_ED) / 45.0)
    else:
        return max(-1.0, -(angle_ED - angle_BC) / 45.0)


def calculate_action_score(action: str, reference_action: str) -> float:
    """
    计算动作评分：匹配动作得+1，相反动作得-1，其他得0

    Args:
        action: 待评估动作
        reference_action: 参考动作

    Returns:
        评分值，范围[-1, 1]
    """
    # 如果动作不在动作编号对照表中，直接返回-1
    if action not in [cmd[0] for cmd in COMMANDS_MAP.values()]:
        return -1.0

    # 如果动作与参考动作匹配
    if action == reference_action:
        return 1.0

    # 如果动作是参考动作的对立动作
    if OPPOSITE_ACTIONS.get(reference_action) == action:
        return -1.0

    # 其他情况
    return 0.0


def find_nearest_point(current_loc: np.ndarray, reference_coords: List[np.ndarray]) -> int:
    """
    在参考轨迹中找到与当前位置最近的点的索引

    Args:
        current_loc: 当前位置坐标
        reference_coords: 参考轨迹坐标列表

    Returns:
        最近点的索引
    """
    min_dist = float('inf')
    nearest_idx = 0

    for i, ref_loc in enumerate(reference_coords):
        # 只考虑位置坐标（前三维）
        dist = np.linalg.norm(current_loc[:3] - ref_loc[:3])
        if dist < min_dist:
            min_dist = dist
            nearest_idx = i

    return nearest_idx


def calculate_trajectory_reference_point(reference_coords: List[np.ndarray], nearest_idx: int) -> np.ndarray:
    """
    计算轨迹参考点（最近点及其后4个点的平均位置）

    Args:
        reference_coords: 参考轨迹坐标列表
        nearest_idx: 最近点的索引

    Returns:
        轨迹参考点坐标
    """
    # 确定要取的点的范围（最近点及其后4个点）
    end_idx = min(nearest_idx + 5, len(reference_coords))
    points = reference_coords[nearest_idx:end_idx]

    # 如果点不足5个，使用所有可用点
    if len(points) == 0:
        return reference_coords[-1]  # 如果没有可用点，返回最后一个点

    # 计算平均位置
    avg_point = np.zeros_like(points[0])
    for point in points:
        avg_point += point
    avg_point /= len(points)

    return avg_point


def calculate_endpoint_score(model_coords: List[np.ndarray], model_actions: List[str],
                            final_loc: np.ndarray) -> List[float]:
    """
    计算终点关系评分

    Args:
        model_coords: 模型轨迹坐标列表
        model_actions: 模型动作列表
        final_loc: 终点坐标

    Returns:
        每个步骤的评分列表
    """
    scores = []

    for i in range(len(model_actions)):
        # 当前位置和执行动作后的位置
        current_loc = model_coords[i]
        action_loc = model_coords[i+1] if i+1 < len(model_coords) else model_coords[i]

        # 计算奖励2（距离变化）
        reward2 = calculate_reward_2(current_loc, action_loc, final_loc)

        # 计算奖励3（方向对齐）
        reward3 = calculate_reward_3(current_loc, action_loc, final_loc)

        # 综合评分（权重1:1）
        score = (reward2 + reward3) / 2.0
        scores.append(score)

    return scores


def calculate_trajectory_score(model_coords: List[np.ndarray], model_actions: List[str],
                              reference_coords: List[np.ndarray]) -> List[float]:
    """
    计算轨迹关系评分

    Args:
        model_coords: 模型轨迹坐标列表
        model_actions: 模型动作列表
        reference_coords: 参考轨迹坐标列表

    Returns:
        每个步骤的评分列表
    """
    scores = []

    for i in range(len(model_actions)):
        # 当前位置和执行动作后的位置
        current_loc = model_coords[i]
        action_loc = model_coords[i+1] if i+1 < len(model_coords) else model_coords[i]

        # 找到参考轨迹中最近的点
        nearest_idx = find_nearest_point(current_loc, reference_coords)

        # 计算轨迹参考点
        trajectory_ref_point = calculate_trajectory_reference_point(reference_coords, nearest_idx)

        # 计算奖励2（距离变化）
        reward2 = calculate_reward_2(current_loc, action_loc, trajectory_ref_point)

        # 计算奖励3（方向对齐）
        reward3 = calculate_reward_3(current_loc, action_loc, trajectory_ref_point)

        # 综合评分（权重1:1）
        score = (reward2 + reward3) / 2.0
        scores.append(score)

    return scores


def calculate_efficiency_score(model_coords: List[np.ndarray], reference_coords: List[np.ndarray]) -> float:
    """
    计算导航效率评分

    Args:
        model_coords: 模型轨迹坐标列表
        reference_coords: 参考轨迹坐标列表

    Returns:
        效率评分，范围[0, 1.0]
    """
    # 获取终点坐标
    final_loc = reference_coords[-1]

    # 获取模型最终位置
    model_final_loc = model_coords[-1]

    # 计算模型最终位置到终点的距离
    dist_to_final = np.linalg.norm(model_final_loc[:3] - final_loc[:3])

    # 判断是否成功导航（距离终点不超过15米）
    is_success = dist_to_final <= 15.0

    # 如果导航失败，返回0分
    if not is_success:
        return 0.0

    # 如果导航成功，计算步数效率
    reference_steps = len(reference_coords) - 1  # 减去初始位置
    model_steps = len(model_coords) - 1  # 减去初始位置

    # 计算步数比例，避免除以零
    if model_steps == 0:
        steps_ratio = 1.0
    else:
        steps_ratio = reference_steps / model_steps

    # 根据需求计算效率分数：0.5 × (N/M)
    # 当M>N时，分数低于0.5；当M<N时，分数高于0.5但不超过1.0
    score = 0.5 * steps_ratio
    
    # 限制最大值为1.0
    score = min(score, 1.0)

    return score


def evaluate_navigation(task_id: str, attempt_id: str = "0", base_dir: str = "data") -> Dict[str, Any]:
    """
    评估指定任务ID的导航轨迹

    Args:
        task_id: 任务ID
        attempt_id: 尝试次数ID（默认为"0"）
        base_dir: 数据目录

    Returns:
        评估结果字典
    """
    # 构建文件路径
    reference_dir = os.path.join(base_dir, "dataset_video_instruction", task_id)
    model_dir = os.path.join(base_dir, "dataset_rl", task_id, attempt_id)
    
    # 构建完整的任务标识符
    full_task_id = f"{task_id}_{attempt_id}"
    
    # 详细检查目录和文件是否存在
    error_details = []
    
    # 检查基准数据集目录
    if not os.path.exists(reference_dir):
        error_details.append(f"基准数据集目录不存在: {reference_dir}")
    
    # 检查模型数据集目录
    if not os.path.exists(model_dir):
        error_details.append(f"模型数据集目录不存在: {model_dir}")
    
    # 如果目录都不存在，直接返回错误
    if error_details:
        return {
            "error": "目录不存在",
            "error_details": error_details,
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }
    
    # 检查必需的文件
    required_files = {
        "reference_loc": os.path.join(reference_dir, "loc.csv"),
        "reference_path": os.path.join(reference_dir, "path.csv"),
        "model_loc": os.path.join(model_dir, "loc.csv"),
        "model_path": os.path.join(model_dir, "path.csv")
    }
    
    missing_files = []
    for file_type, file_path in required_files.items():
        if not os.path.exists(file_path):
            missing_files.append(f"{file_type}: {file_path}")
    
    if missing_files:
        return {
            "error": "必需文件缺失",
            "error_details": missing_files,
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 加载坐标和动作
    try:
        reference_coords = load_coordinates(required_files["reference_loc"])
        if not reference_coords:
            return {
                "error": "基准坐标数据为空",
                "error_details": [f"文件 {required_files['reference_loc']} 没有有效坐标数据"],
                "task_id": full_task_id,
                "original_task_id": task_id,
                "attempt_id": attempt_id
            }
            
        reference_actions = load_actions(required_files["reference_path"])
        model_coords = load_coordinates(required_files["model_loc"])
        
        if not model_coords:
            return {
                "error": "模型坐标数据为空",
                "error_details": [f"文件 {required_files['model_loc']} 没有有效坐标数据"],
                "task_id": full_task_id,
                "original_task_id": task_id,
                "attempt_id": attempt_id
            }
            
        model_actions = load_actions(required_files["model_path"])
        
        if not model_actions:
            return {
                "error": "模型动作数据为空",
                "error_details": [f"文件 {required_files['model_path']} 没有有效动作数据"],
                "task_id": full_task_id,
                "original_task_id": task_id,
                "attempt_id": attempt_id
            }
            
    except Exception as e:
        return {
            "error": "数据加载失败",
            "error_details": [f"加载数据时发生异常: {str(e)}"],
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 验证数据一致性
    validation_errors = []
    
    # 检查坐标和动作数量的一致性
    if len(model_coords) != len(model_actions) + 1:
        validation_errors.append(f"模型坐标数量({len(model_coords)})与动作数量({len(model_actions)})不匹配")
    
    if len(reference_coords) != len(reference_actions) + 1:
        validation_errors.append(f"基准坐标数量({len(reference_coords)})与动作数量({len(reference_actions)})不匹配")
    
    # 检查坐标维度
    if reference_coords and len(reference_coords[0]) < 7:
        validation_errors.append(f"基准坐标维度不足: {len(reference_coords[0])}, 需要至少7维 [x,y,z,rx,ry,yaw,pitch]")
    
    if model_coords and len(model_coords[0]) < 7:
        validation_errors.append(f"模型坐标维度不足: {len(model_coords[0])}, 需要至少7维 [x,y,z,rx,ry,yaw,pitch]")
    
    if validation_errors:
        return {
            "error": "数据验证失败",
            "error_details": validation_errors,
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 计算终点关系评分
    try:
        endpoint_scores = calculate_endpoint_score(model_coords, model_actions, reference_coords[-1])
    except Exception as e:
        return {
            "error": "终点关系评分计算失败",
            "error_details": [f"计算终点关系评分时发生异常: {str(e)}"],
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 计算轨迹关系评分
    try:
        trajectory_scores = calculate_trajectory_score(model_coords, model_actions, reference_coords)
    except Exception as e:
        return {
            "error": "轨迹关系评分计算失败",
            "error_details": [f"计算轨迹关系评分时发生异常: {str(e)}"],
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 计算导航效率评分
    try:
        efficiency_score = calculate_efficiency_score(model_coords, reference_coords)
    except Exception as e:
        return {
            "error": "导航效率评分计算失败",
            "error_details": [f"计算导航效率评分时发生异常: {str(e)}"],
            "task_id": full_task_id,
            "original_task_id": task_id,
            "attempt_id": attempt_id
        }

    # 加载模型动作编号（用于输出）
    model_action_ids = []
    try:
        with open(required_files["model_path"], 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if len(row) >= 2:
                    action_id = row[1].strip()
                    model_action_ids.append(action_id)
    except Exception as e:
        # 动作编号加载失败不是致命错误，使用默认值
        model_action_ids = ["unknown"] * len(model_actions)

    # 计算每个步骤的详细评分信息
    step_details = []
    step_scores = []
    for i in range(len(model_actions)):
        # 检查动作是否有效
        if model_actions[i] not in [cmd[0] for cmd in COMMANDS_MAP.values()]:
            # 如果动作不在动作编号对照表中，直接给-1分
            step_score = -1.0
            endpoint_score = -1.0
            trajectory_score = -1.0
            efficiency_contribution = 0.0
        else:
            endpoint_score = endpoint_scores[i]
            trajectory_score = trajectory_scores[i]
            # 效率评分平摊到每个步骤
            efficiency_contribution = efficiency_score / len(model_actions) if len(model_actions) > 0 else 0.0
            
            step_score = endpoint_score + trajectory_score + efficiency_contribution
        
        # 收集步骤详细信息
        step_detail = {
            "step": i,
            "action_id": model_action_ids[i] if i < len(model_action_ids) else "unknown",
            "action_name": model_actions[i],
            "endpoint_score": endpoint_score,
            "trajectory_score": trajectory_score,
            "efficiency_score": efficiency_contribution,
            "total_score": step_score
        }
        step_details.append(step_detail)
        step_scores.append(step_score)

    # 计算总评分（所有步骤的平均分）
    avg_step_score = sum(step_scores) / len(step_scores) if step_scores else 0.0
    
    # 任务总分就是步骤平均分
    total_score = avg_step_score

    # 返回评估结果
    return {
        "task_id": full_task_id,
        "original_task_id": task_id,
        "attempt_id": attempt_id,
        "step_details": step_details,
        "step_scores": step_scores,
        "endpoint_scores": endpoint_scores,
        "trajectory_scores": trajectory_scores,
        "efficiency_score": efficiency_score,
        "avg_step_score": avg_step_score,
        "total_score": total_score,
        "model_steps": len(model_actions),
        "reference_steps": len(reference_actions)
    }


def batch_evaluate_navigation(task_ids: List[str], attempt_id: str = "0", base_dir: str = "data", output_file: str = None) -> Dict[str, Any]:
    """
    批量评估多个导航任务

    Args:
        task_ids: 任务ID列表
        attempt_id: 尝试次数ID（默认为"0"）
        base_dir: 数据目录
        output_file: 输出文件路径

    Returns:
        评估结果字典
    """
    results = {}
    failed_tasks = {}  # 记录失败的任务详情

    for task_id in task_ids:
        print(f"评估任务 {task_id}_{attempt_id}...")
        result = evaluate_navigation(task_id, attempt_id, base_dir)
        
        if "error" in result:
            # 记录失败任务
            failed_tasks[f"{task_id}_{attempt_id}"] = result
            print(f"  失败: {result['error']}")
            if "error_details" in result:
                for detail in result["error_details"]:
                    print(f"    - {detail}")
        else:
            print(f"  成功: 总分 {result['total_score']:.4f}")
        
        results[f"{task_id}_{attempt_id}"] = result

    # 计算平均分数（只计算成功的任务）
    successful_results = [(task_id, result) for task_id, result in results.items() 
                         if "error" not in result]
    
    total_scores = [result["total_score"] for task_id, result in successful_results]
    avg_total_score = sum(total_scores) / len(total_scores) if total_scores else 0.0

    # 统计错误类型
    error_statistics = {}
    for task_id, failed_result in failed_tasks.items():
        error_type = failed_result["error"]
        if error_type not in error_statistics:
            error_statistics[error_type] = {
                "count": 0,
                "tasks": []
            }
        error_statistics[error_type]["count"] += 1
        error_statistics[error_type]["tasks"].append(task_id)

    # 添加汇总信息
    summary = {
        "task_count": len(task_ids),
        "success_count": len(successful_results),
        "failed_count": len(failed_tasks),
        "avg_total_score": avg_total_score,
        "attempt_id": attempt_id,
        "error_statistics": error_statistics
    }
    results["summary"] = summary
    
    # 单独保存失败任务详情
    if failed_tasks:
        results["failed_tasks"] = failed_tasks

    # 保存结果到文件
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到 {output_file}")
        
        # 如果有失败任务，单独保存失败任务详情
        if failed_tasks:
            failed_file = output_file.replace('.json', '_failed.json')
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(failed_tasks, f, ensure_ascii=False, indent=2)
            print(f"失败任务详情已保存到 {failed_file}")

    return results


def find_all_task_attempts(base_dir: str = "data") -> List[Tuple[str, str]]:
    """
    查找所有可用的任务ID和尝试次数组合

    Args:
        base_dir: 数据目录

    Returns:
        (任务ID, 尝试次数)的列表
    """
    # 查找基准数据集中的所有任务ID
    reference_dirs = glob.glob(os.path.join(base_dir, "dataset_video_instruction", "*"))
    reference_task_ids = [os.path.basename(d) for d in reference_dirs if os.path.isdir(d)]

    # 查找模型数据集中的所有任务ID和尝试次数
    task_attempts = []
    for task_id in reference_task_ids:
        model_task_dir = os.path.join(base_dir, "dataset_rl", task_id)
        if os.path.exists(model_task_dir):
            # 查找该任务下的所有尝试次数
            attempt_dirs = glob.glob(os.path.join(model_task_dir, "*"))
            for attempt_dir in attempt_dirs:
                if os.path.isdir(attempt_dir):
                    attempt_id = os.path.basename(attempt_dir)
                    # 验证尝试目录是否包含必需文件
                    if (os.path.exists(os.path.join(attempt_dir, "loc.csv")) and 
                        os.path.exists(os.path.join(attempt_dir, "path.csv"))):
                        task_attempts.append((task_id, attempt_id))

    # 排序
    task_attempts.sort()
    return task_attempts


def find_all_task_ids(base_dir: str = "data") -> List[str]:
    """
    查找所有可用的任务ID（为了向后兼容保留）

    Args:
        base_dir: 数据目录

    Returns:
        任务ID列表
    """
    # 查找基准数据集中的所有任务ID
    reference_dirs = glob.glob(os.path.join(base_dir, "dataset_video_instruction", "*"))
    reference_task_ids = [os.path.basename(d) for d in reference_dirs if os.path.isdir(d)]

    # 查找模型数据集中的所有任务ID（只检查第0次尝试）
    model_dirs = glob.glob(os.path.join(base_dir, "dataset_rl", "*", "0"))
    model_task_ids = [os.path.basename(os.path.dirname(d)) for d in model_dirs if os.path.isdir(d)]

    # 取交集，确保任务在两个数据集中都存在
    task_ids = list(set(reference_task_ids) & set(model_task_ids))
    task_ids.sort()

    return task_ids


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="无人机导航评分系统")
    parser.add_argument("--task_id", help="要评估的任务ID，不指定则评估所有任务")
    parser.add_argument("--attempt_id", default="0", help="尝试次数ID（默认为0）")
    parser.add_argument("--all_attempts", action="store_true", help="评估所有尝试次数")
    parser.add_argument("--base_dir", default="data", help="数据目录")
    parser.add_argument("--output_file", default="navigation_scores.json", help="输出文件路径")
    args = parser.parse_args()

    # 确定要评估的任务ID和尝试次数
    if args.task_id:
        if args.all_attempts:
            # 评估指定任务的所有尝试次数
            task_attempts = find_all_task_attempts(args.base_dir)
            task_attempts = [(tid, aid) for tid, aid in task_attempts if tid == args.task_id]
            if not task_attempts:
                print(f"未找到任务 {args.task_id} 的任何有效尝试")
                return
            
            results = {}
            for task_id, attempt_id in task_attempts:
                print(f"评估任务 {task_id}_{attempt_id}...")
                result = evaluate_navigation(task_id, attempt_id, args.base_dir)
                results[f"{task_id}_{attempt_id}"] = result
            
            # 保存结果
            if args.output_file:
                with open(args.output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"结果已保存到 {args.output_file}")
                
            # 打印汇总信息
            successful_results = [r for r in results.values() if "error" not in r]
            print(f"\n评估汇总:")
            print(f"总尝试数: {len(task_attempts)}")
            print(f"成功评估数: {len(successful_results)}")
            if successful_results:
                avg_score = sum(r["total_score"] for r in successful_results) / len(successful_results)
                print(f"平均总分: {avg_score:.4f}")
        else:
            # 评估指定任务的指定尝试次数
            task_ids = [args.task_id]
            results = batch_evaluate_navigation(task_ids, args.attempt_id, args.base_dir, args.output_file)
            
            # 打印汇总信息
            summary = results["summary"]
            print("\n评估汇总:")
            print(f"任务: {args.task_id}_{args.attempt_id}")
            print(f"成功评估任务数: {summary['success_count']}")
            print(f"失败评估任务数: {summary['failed_count']}")
            if summary['success_count'] > 0:
                print(f"平均总分: {summary['avg_total_score']:.4f}")
    else:
        if args.all_attempts:
            # 评估所有任务的所有尝试次数
            print("未指定任务ID，将评估所有任务的所有尝试次数...")
            task_attempts = find_all_task_attempts(args.base_dir)
            print(f"找到 {len(task_attempts)} 个任务尝试")
            
            results = {}
            failed_tasks = {}
            
            for task_id, attempt_id in task_attempts:
                print(f"评估任务 {task_id}_{attempt_id}...")
                result = evaluate_navigation(task_id, attempt_id, args.base_dir)
                
                if "error" in result:
                    failed_tasks[f"{task_id}_{attempt_id}"] = result
                    print(f"  失败: {result['error']}")
                else:
                    print(f"  成功: 总分 {result['total_score']:.4f}")
                
                results[f"{task_id}_{attempt_id}"] = result
            
            # 计算汇总信息
            successful_results = [r for r in results.values() if "error" not in r]
            avg_total_score = sum(r["total_score"] for r in successful_results) / len(successful_results) if successful_results else 0.0
            
            summary = {
                "total_attempts": len(task_attempts),
                "success_count": len(successful_results),
                "failed_count": len(failed_tasks),
                "avg_total_score": avg_total_score
            }
            results["summary"] = summary
            
            if failed_tasks:
                results["failed_tasks"] = failed_tasks
            
            # 保存结果
            if args.output_file:
                with open(args.output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"结果已保存到 {args.output_file}")
                
                if failed_tasks:
                    failed_file = args.output_file.replace('.json', '_failed.json')
                    with open(failed_file, 'w', encoding='utf-8') as f:
                        json.dump(failed_tasks, f, ensure_ascii=False, indent=2)
                    print(f"失败任务详情已保存到 {failed_file}")
            
            # 打印汇总信息
            print("\n评估汇总:")
            print(f"总尝试数: {summary['total_attempts']}")
            print(f"成功评估数: {summary['success_count']}")
            print(f"失败评估数: {summary['failed_count']}")
            if summary['success_count'] > 0:
                print(f"平均总分: {summary['avg_total_score']:.4f}")
        else:
            # 评估所有任务的第0次尝试（默认行为）
            print("未指定任务ID，将评估所有任务的第0次尝试...")
            task_ids = find_all_task_ids(args.base_dir)
            print(f"找到 {len(task_ids)} 个任务")

            # 批量评估
            results = batch_evaluate_navigation(task_ids, args.attempt_id, args.base_dir, args.output_file)

            # 打印汇总信息
            summary = results["summary"]
            print("\n评估汇总:")
            print(f"总任务数: {summary['task_count']}")
            print(f"成功评估任务数: {summary['success_count']}")
            print(f"失败评估任务数: {summary['failed_count']}")
            if summary['success_count'] > 0:
                print(f"平均总分: {summary['avg_total_score']:.4f}")
                
            # 打印错误统计
            if summary.get('error_statistics'):
                print("\n错误类型统计:")
                for error_type, error_info in summary['error_statistics'].items():
                    print(f"  {error_type}: {error_info['count']} 个任务")
                    if len(error_info['tasks']) <= 5:
                        print(f"    任务: {', '.join(error_info['tasks'])}")
                    else:
                        print(f"    任务: {', '.join(error_info['tasks'][:5])} ... (共{len(error_info['tasks'])}个)")


if __name__ == "__main__":
    main()
