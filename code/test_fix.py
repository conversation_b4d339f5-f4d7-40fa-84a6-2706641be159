#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的 normalize_action 方法

该脚本用于测试修复后的 normalize_action 方法是否能够正确处理列表和其他非字符串类型的输入。
"""

from navigation_accuracy_reward import NavigationAccuracy

def main():
    # 创建奖励函数实例
    reward_func = NavigationAccuracy()
    
    # 测试字符串输入
    print("\n测试字符串输入:")
    action_str = "Move forward"
    normalized = reward_func.normalize_action(action_str)
    print(f"输入: {action_str}, 标准化后: {normalized}")
    
    # 测试列表输入
    print("\n测试列表输入:")
    action_list = ["Move forward", "Move right"]
    normalized = reward_func.normalize_action(action_list)
    print(f"输入: {action_list}, 标准化后: {normalized}")
    
    # 测试嵌套列表输入
    print("\n测试嵌套列表输入:")
    action_nested_list = [["Move forward"], ["Move right"]]
    normalized = reward_func.normalize_action(action_nested_list)
    print(f"输入: {action_nested_list}, 标准化后: {normalized}")
    
    # 测试数字输入
    print("\n测试数字输入:")
    action_num = 123
    normalized = reward_func.normalize_action(action_num)
    print(f"输入: {action_num}, 标准化后: {normalized}")
    
    # 测试空输入
    print("\n测试空输入:")
    action_empty = ""
    normalized = reward_func.normalize_action(action_empty)
    print(f"输入: {action_empty}, 标准化后: {normalized}")
    
    # 测试None输入
    print("\n测试None输入:")
    action_none = None
    normalized = reward_func.normalize_action(action_none)
    print(f"输入: {action_none}, 标准化后: {normalized}")
    
    # 测试完整的奖励函数
    print("\n测试完整的奖励函数:")
    completions = ["<answer>Move forward</answer>"]
    solution = ["Move forward"]
    
    # 测试正常的history_actions
    print("\n1. 测试正常的history_actions:")
    rewards = reward_func(completions, solution, 
                         history_actions=["Move backward", "Move left"], 
                         debug=True)
    print(f"奖励: {rewards}")
    
    # 测试嵌套的history_actions
    print("\n2. 测试嵌套的history_actions:")
    rewards = reward_func(completions, solution, 
                         history_actions=[["Move backward"], ["Move left"]], 
                         debug=True)
    print(f"奖励: {rewards}")

if __name__ == "__main__":
    main()
