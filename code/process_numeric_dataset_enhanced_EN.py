#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data processing script: Convert images and path data from E:/NIPS/video_frame to ms-swift compatible GRPO multimodal dataset
Specifically processes numerically named folders and sequentially arranged navigation targets
Enhanced version: Merges consecutive movements in the same direction, modifies system prompts, and converts to English
"""

import os
import json
import logging
import argparse
import pandas as pd
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 中英文动作映射表
ZH_TO_EN_ACTION_MAPPING = {
    # 详细动作映射
    "向前移动10m": "Move forward 10m",
    "向后移动10m": "Move backward 10m",
    "向左移动10m": "Move left 10m",
    "向右移动10m": "Move right 10m",
    "向上移动10m": "Move up 10m",
    "向下移动10m": "Move down 10m",
    "向左旋转22.5度": "Rotate left 22.5 degrees",
    "向右旋转22.5度": "Rotate right 22.5 degrees",
    "摄像头云台向上转动45度": "Tilt camera up 45 degrees",
    "摄像头云台向下转动45度": "Tilt camera down 45 degrees",

    # 简化动作映射
    "向前运动": "Move forward",
    "向后运动": "Move backward",
    "向左移动": "Move left",
    "向右移动": "Move right",
    "向上运动": "Move up",
    "向下运动": "Move down",
    "向左旋转": "Rotate left",
    "向右旋转": "Rotate right",
    "向上看": "Look up",
    "向下看": "Look down",

    # 其他可能的表述
    "向右运动": "Move right",
    "向左运动": "Move left",
    "未知动作": "Unknown action"
}

# 动作映射表：详细动作 -> 简化动作（中文）
ACTION_MAPPING = {
    # 详细动作映射
    "向前移动10m": "向前运动",
    "向后移动10m": "向后运动",
    "向左移动10m": "向左移动",
    "向右移动10m": "向右移动",
    "向上移动10m": "向上运动",
    "向下移动10m": "向下运动",
    "向左旋转22.5度": "向左旋转",
    "向右旋转22.5度": "向右旋转",
    "摄像头云台向上转动45度": "向上看",
    "摄像头云台向下转动45度": "向下看",

    # 简化动作映射（path.csv中可能直接使用简化表述）
    "向前运动": "向前运动",
    "向后运动": "向后运动",
    "向左移动": "向左移动",
    "向右移动": "向右移动",
    "向上运动": "向上运动",
    "向下运动": "向下运动",
    "向左旋转": "向左旋转",
    "向右旋转": "向右旋转",
    "向上看": "向上看",
    "向下看": "向下看",

    # 数字映射（处理可能的数字索引）
    "0": "向前运动",
    "1": "向后运动",
    "2": "向左移动",
    "3": "向右移动",
    "4": "向上运动",
    "5": "向下运动",
    "6": "向左旋转",
    "7": "向右旋转",
    "8": "向上看",
    "9": "向下看",

    # 其他可能的表述
    "向右运动": "向右移动",
    "向左运动": "向左移动"
}

# 可合并的运动动作集合（中文）
MERGEABLE_MOVEMENTS_ZH = {
    "向前运动", "向后运动", "向上运动", "向下运动"
}

# 可合并的运动动作集合（英文）
MERGEABLE_MOVEMENTS_EN = {
    "Move forward", "Move backward", "Move up", "Move down"
}

# 动作空间描述（中文，用于用户提示）
ACTION_SPACE_DESC_ZH = """- 向前运动
- 向后运动
- 向左移动
- 向右移动
- 向上运动
- 向下运动
- 向左旋转
- 向右旋转
- 向上看
- 向下看"""

# 动作空间描述（英文，用于用户提示）
ACTION_SPACE_DESC_EN = """- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down"""

# 系统提示内容（中文）
SYSTEM_CONTENT_ZH = """你是一个视觉导航决策系统，无人机可以进行的动作集合有：
- 向前运动
- 向后运动
- 向左移动
- 向右移动
- 向上运动
- 向下运动
- 向左旋转22.5度
- 向右旋转22.5度
- 摄像头云台向上转动45度
- 摄像头云台向下转动45度"""

# 系统提示内容（英文）
SYSTEM_CONTENT_EN = """A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> \n<answer> answer here </answer>. Ensure that your answer is consistent with and directly derived from your thinking process, maintaining logical coherence between the two sections. User: . Assistant:\nYou are a visual navigation decision system. The drone can perform the following actions:
- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left 22.5 degrees
- Rotate right 22.5 degrees
- Tilt camera up 45 degrees
- Tilt camera down 45 degrees"""

# 默认使用英文
MERGEABLE_MOVEMENTS = MERGEABLE_MOVEMENTS_EN
ACTION_SPACE_DESC = ACTION_SPACE_DESC_EN
SYSTEM_CONTENT = SYSTEM_CONTENT_EN


def read_goal_from_txt(goal_path: str) -> str:
    """
    从goal.txt文件中读取导航目标

    Args:
        goal_path: goal.txt文件路径

    Returns:
        导航目标文本，如果文件不存在或读取失败则返回空字符串
    """
    try:
        with open(goal_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if content:
                logger.info(f"从goal.txt成功读取导航目标: {content}")
                return content
            else:
                logger.warning(f"goal.txt文件为空: {goal_path}")
                return ""
    except FileNotFoundError:
        logger.debug(f"goal.txt文件不存在: {goal_path}")
        return ""
    except Exception as e:
        logger.warning(f"读取goal.txt文件时出错: {e}")
        return ""


def read_navigation_targets(label_path: str) -> List[str]:
    """
    读取导航目标文件

    Args:
        label_path: 导航目标文件路径

    Returns:
        导航目标列表，按照文件中的顺序
    """
    navigation_targets = []
    try:
        with open(label_path, 'r', encoding='utf-8') as f:
            for line in f:
                if ';' in line:
                    _, target = line.strip().split(';', 1)
                    navigation_targets.append(target)
                else:
                    logger.warning(f"跳过格式不正确的行: {line.strip()}")
    except FileNotFoundError:
        logger.error(f"导航目标文件不存在: {label_path}")
        raise
    except Exception as e:
        logger.error(f"读取导航目标文件时出错: {e}")
        raise

    logger.info(f"成功读取 {len(navigation_targets)} 个导航目标")
    return navigation_targets


def get_numeric_folders(base_path: str) -> List[str]:
    """
    获取所有数字命名的文件夹，并按数字顺序排序

    Args:
        base_path: 基础路径

    Returns:
        数字文件夹路径列表，按数字顺序排序
    """
    try:
        # 列出所有文件夹
        all_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]

        # 筛选出数字命名的文件夹
        numeric_folders = []
        for folder in all_folders:
            if folder.isdigit():
                numeric_folders.append(folder)

        # 按数字顺序排序
        numeric_folders.sort(key=int)

        # 转换为完整路径
        folder_paths = [os.path.join(base_path, folder) for folder in numeric_folders]

        logger.info(f"找到 {len(folder_paths)} 个数字文件夹")
        return folder_paths
    except Exception as e:
        logger.error(f"获取数字文件夹时出错: {e}")
        return []


def read_actions_from_csv(csv_path: str) -> List[str]:
    """
    从CSV文件中读取动作序列

    Args:
        csv_path: CSV文件路径

    Returns:
        动作列表
    """
    try:
        # 首先尝试检查文件格式
        with open(csv_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            # 检查是否是TSV格式（制表符分隔）
            if '\t' in first_line:
                logger.info(f"检测到TSV格式: {csv_path}")
                # 明确指定header=None，确保第一行不被视为列名
                df = pd.read_csv(csv_path, sep='\t', header=None)
            else:
                # 明确指定header=None，确保第一行不被视为列名
                df = pd.read_csv(csv_path, header=None)

        # 检查列数
        if len(df.columns) == 1:
            # 只有一列，可能是没有标题的CSV
            logger.info(f"CSV文件只有一列: {csv_path}")
            actions = df.iloc[:, 0].astype(str).tolist()
            return actions

        # 如果有多列，尝试找到动作列
        if len(df.columns) >= 2:
            # 检查第一列是否是索引（数字递增）
            first_col = df.iloc[:, 0]
            if first_col.dtype.kind in 'iuf' and first_col.is_monotonic_increasing:
                logger.info(f"CSV文件第一列似乎是索引，使用第二列作为动作: {csv_path}")
                actions = df.iloc[:, 1].astype(str).tolist()

                # 打印前几个动作，帮助调试
                if actions:
                    logger.info(f"CSV文件 {csv_path} 中的前3个动作: {actions[:3]}")

                return actions

        # 由于我们指定了header=None，列名现在是数字索引
        # 直接检查是否有第二列，如果有就使用第二列（动作列）
        if len(df.columns) >= 2:
            logger.info(f"CSV文件有多列，使用第二列作为动作列: {csv_path}")
            actions = df[1].astype(str).tolist()
        else:
            logger.info(f"CSV文件只有一列，使用第一列作为动作列: {csv_path}")
            actions = df[0].astype(str).tolist()

        # 打印前几个动作，帮助调试
        if actions:
            logger.info(f"CSV文件 {csv_path} 中的前3个动作: {actions[:3]}")

        return actions
    except FileNotFoundError:
        logger.error(f"CSV文件不存在: {csv_path}")
        raise
    except Exception as e:
        logger.error(f"读取CSV文件时出错: {e}")
        # 尝试更简单的方法读取
        try:
            logger.info(f"尝试使用更简单的方法读取CSV: {csv_path}")
            actions = []
            with open(csv_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    # 不跳过第一行，直接处理
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        actions.append(parts[1])
                    elif len(parts) == 1:
                        actions.append(parts[0])

                    # 打印前几行的处理结果，帮助调试
                    if line_num < 3:
                        logger.info(f"第{line_num+1}行: {line.strip()} -> 动作: {actions[-1]}")
            if actions:
                logger.info(f"成功读取 {len(actions)} 个动作")
                return actions
        except Exception as inner_e:
            logger.error(f"简单读取CSV也失败: {inner_e}")
        raise


def check_image_exists(image_path: str) -> bool:
    """
    检查图像文件是否存在

    Args:
        image_path: 图像文件路径

    Returns:
        文件是否存在
    """
    return os.path.exists(image_path)


def merge_consecutive_movements(actions: List[str], step_images: List[str], mergeable_movements: set) -> Tuple[List[str], List[str], List[int]]:
    """
    Merge consecutive movements in the same direction and skip intermediate frames

    Args:
        actions: Action list
        step_images: Step image path list
        mergeable_movements: Set of actions that can be merged

    Returns:
        Merged action list, image path list, and a list of counts for each merged action
    """
    if not actions or len(actions) <= 1:
        return actions, step_images, [1] * len(actions)

    merged_actions = []
    merged_images = []
    merged_counts = []  # 记录每个动作的合并次数

    i = 0
    while i < len(actions):
        current_action = actions[i]
        count = 1  # 默认合并次数为1（单个动作）

        # If the current action is a mergeable movement, check if subsequent actions are the same
        if current_action in mergeable_movements and i < len(actions) - 1:
            # Count consecutive identical actions
            while i + 1 < len(actions) and actions[i + 1] == current_action:
                i += 1
                count += 1

            # Add the image corresponding to the last identical action
            if i < len(step_images):
                merged_images.append(step_images[i])
        else:
            # For non-mergeable actions, directly add the corresponding image
            if i < len(step_images):
                merged_images.append(step_images[i])

        merged_actions.append(current_action)
        merged_counts.append(count)  # 记录当前动作的合并次数
        i += 1

    logger.info(f"Actions before merging: {len(actions)}, after merging: {len(merged_actions)}")
    logger.info(f"Images before merging: {len(step_images)}, after merging: {len(merged_images)}")
    logger.info(f"Merged counts: {merged_counts}")

    return merged_actions, merged_images, merged_counts


def create_no_history_case(folder_id: str, navigation_target: str,
                          initial_img: str, solution: str,
                          all_actions: List[str] = None, use_english: bool = True) -> Dict:
    """
    Create a case with no history

    Args:
        folder_id: Folder ID
        navigation_target: Navigation target
        initial_img: Initial image path
        solution: Correct action
        all_actions: Complete list of actions for the navigation path (optional)
        use_english: Whether to use English (default: True)

    Returns:
        Case data with no history
    """
    # 转换正确动作为英文（如果使用英文）
    if use_english and solution in ZH_TO_EN_ACTION_MAPPING:
        solution_output = ZH_TO_EN_ACTION_MAPPING[solution]
    else:
        solution_output = solution

    # 转换所有动作为英文（如果使用英文且提供了动作列表）
    full_action_sequence = []
    if all_actions:
        if use_english:
            for action in all_actions:
                if action in ZH_TO_EN_ACTION_MAPPING:
                    full_action_sequence.append(ZH_TO_EN_ACTION_MAPPING[action])
                else:
                    full_action_sequence.append(action)
        else:
            full_action_sequence = all_actions.copy()

    if use_english:
        return {
            "messages": [
                {"role": "system", "content": SYSTEM_CONTENT_EN},
                {"role": "user", "content": f"Navigation target: {navigation_target}\nCurrent position image:\n<image>\n\nPlease select the best next action from the following action space:\n{ACTION_SPACE_DESC_EN}"}
            ],
            "images": [initial_img],
            "solution": solution_output,
            "folder_id": folder_id,  # Add folder ID for tracking
            "current_index": 0,  # Current index in the action sequence (first action)
            "action_sequence": full_action_sequence  # Add full action sequence at the top level
        }
    else:
        return {
            "messages": [
                {"role": "system", "content": SYSTEM_CONTENT_ZH},
                {"role": "user", "content": f"导航目标：{navigation_target}\n当前位置图像如下：\n<image>\n\n请从以下动作空间中选择最佳的下一步动作：\n{ACTION_SPACE_DESC_ZH}"}
            ],
            "images": [initial_img],
            "solution": solution,
            "folder_id": folder_id,  # 添加文件夹ID以便追踪
            "current_index": 0,  # 当前在动作序列中的索引（第一个动作）
            "action_sequence": full_action_sequence  # 添加完整动作序列到顶层
        }


def detect_consecutive_actions(actions: List[str], start_idx: int) -> Tuple[str, int]:
    """
    Detect consecutive identical actions starting from a given index

    Args:
        actions: List of actions
        start_idx: Starting index to check for consecutive actions

    Returns:
        Tuple of (action, count) where count is the number of consecutive occurrences
    """
    if start_idx >= len(actions):
        return "", 0

    current_action = actions[start_idx]
    count = 1

    # Count consecutive identical actions
    for i in range(start_idx + 1, len(actions)):
        if actions[i] == current_action:
            count += 1
        else:
            break

    return current_action, count

def create_history_case(folder_id: str, navigation_target: str,
                       initial_img: str, step_images: List[str],
                       actions: List[str], simplified_actions: List[str],
                       history_index: int, use_english: bool = True,
                       merged_counts: List[int] = None) -> Dict:
    """
    Create a case with history

    Args:
        folder_id: Folder ID
        navigation_target: Navigation target
        initial_img: Initial image path
        step_images: Step image path list
        actions: Detailed action list (not used directly)
        simplified_actions: Simplified action list
        history_index: History index
        use_english: Whether to use English (default: True)
        merged_counts: List of counts for each merged action (default: None)

    Returns:
        Case data with history
    """
    history_images = [initial_img] + step_images[:history_index]

    # 转换历史动作和正确动作为英文（如果使用英文）
    if use_english:
        history_actions_output = []
        for action in simplified_actions[:history_index]:
            if action in ZH_TO_EN_ACTION_MAPPING:
                history_actions_output.append(ZH_TO_EN_ACTION_MAPPING[action])
            else:
                history_actions_output.append(action)

        if simplified_actions[history_index] in ZH_TO_EN_ACTION_MAPPING:
            solution_output = ZH_TO_EN_ACTION_MAPPING[simplified_actions[history_index]]
        else:
            solution_output = simplified_actions[history_index]

        # 英文提示内容
        history_content = f"Navigation target: {navigation_target}\nInitial position image:\n<image>\n\n"

        # Add historical steps with action merging (English)
        j = 0
        step_num = 1
        while j < history_index:
            action_text = history_actions_output[j]

            # 获取当前动作的合并次数
            action_count = 1
            if merged_counts and j < len(merged_counts):
                action_count = merged_counts[j]

            # 根据合并次数显示动作
            if action_count > 1 and action_text in MERGEABLE_MOVEMENTS_EN:
                # 使用合并后的动作描述
                history_content += f'Step {step_num}: After executing {action_count} times action "{action_text}", the position is:\n<image>\n\n'
            else:
                # 常规单个动作
                history_content += f'Step {step_num}: After executing action "{action_text}", the position is:\n<image>\n\n'

            j += 1
            step_num += 1

        history_content += f"Please continue to the navigation target {navigation_target} given at the initial position. Please select the best next action from the following action space:\n{ACTION_SPACE_DESC_EN}"

        # Create complete action sequence for the entire navigation path
        full_action_sequence = []
        for action in simplified_actions:
            if action in ZH_TO_EN_ACTION_MAPPING:
                full_action_sequence.append(ZH_TO_EN_ACTION_MAPPING[action])
            else:
                full_action_sequence.append(action)

        return {
            "messages": [
                {"role": "system", "content": SYSTEM_CONTENT_EN},
                {"role": "user", "content": history_content}
            ],
            "images": history_images,
            "solution": solution_output,
            "history_actions": history_actions_output,
            "folder_id": folder_id,  # Add folder ID for tracking
            "current_index": history_index,  # Add current index in the action sequence
            "action_sequence": full_action_sequence  # Add full action sequence at the top level
        }
    else:
        # 中文提示内容
        history_content = f"导航目标：{navigation_target}\n初始位置图像如下：\n<image>\n\n"

        # 添加历史步骤，支持动作合并（中文）
        j = 0
        step_num = 1
        while j < history_index:
            action_text = simplified_actions[j]

            # 获取当前动作的合并次数
            action_count = 1
            if merged_counts and j < len(merged_counts):
                action_count = merged_counts[j]

            # 根据合并次数显示动作
            if action_count > 1 and action_text in MERGEABLE_MOVEMENTS_ZH:
                # 使用合并后的动作描述
                history_content += f'Step {step_num}: 执行了 {action_count} 次"{action_text}"后的位置：\n<image>\n\n'
            else:
                # 常规单个动作
                history_content += f'Step {step_num}: 执行动作"{action_text}"后的位置：\n<image>\n\n'

            j += 1
            step_num += 1

        history_content += f"请继续前往在初始位置给定的导航目标:{navigation_target}。请从以下动作空间中选择最佳的下一步动作：\n{ACTION_SPACE_DESC_ZH}"

        return {
            "messages": [
                {"role": "system", "content": SYSTEM_CONTENT_ZH},
                {"role": "user", "content": history_content}
            ],
            "images": history_images,
            "solution": simplified_actions[history_index],
            "history_actions": simplified_actions[:history_index],
            "folder_id": folder_id,  # 添加文件夹ID以便追踪
            "current_index": history_index,  # 添加当前在动作序列中的索引
            "action_sequence": simplified_actions  # 添加完整动作序列到顶层
        }


def process_folder(folder_path: str, folder_id: str, navigation_target: str, max_actions: int = 32, use_english: bool = True) -> List[Dict]:
    """
    Process a single folder

    Args:
        folder_path: Folder path
        folder_id: Folder ID
        navigation_target: Navigation target
        max_actions: Maximum number of actions, folders exceeding this limit will be skipped
        use_english: Whether to use English (default: True)

    Returns:
        List of cases generated from this folder
    """
    logger.info(f"Processing folder {folder_id}, navigation target: {navigation_target}")

    # Check if folder exists
    if not os.path.exists(folder_path):
        logger.warning(f"Folder does not exist: {folder_path}")
        return []

    # Read path.csv to get correct actions
    path_csv = os.path.join(folder_path, "path.csv")
    if not os.path.exists(path_csv):
        logger.warning(f"path.csv does not exist: {path_csv}")
        return []

    try:
        actions = read_actions_from_csv(path_csv)
        # Map detailed actions to simplified actions
        simplified_actions = []
        for action in actions:
            if action in ACTION_MAPPING:
                simplified_actions.append(ACTION_MAPPING[action])
            else:
                logger.warning(f"Unknown action: {action}, skipping")
                simplified_actions.append("未知动作")
    except Exception as e:
        logger.error(f"Error processing path.csv: {e}")
        return []

    # Get image paths
    initial_img = os.path.join(folder_path, "initial.png")
    if not check_image_exists(initial_img):
        logger.warning(f"Initial image does not exist: {initial_img}")
        return []

    step_images = []
    for i in range(len(actions)):
        step_img = os.path.join(folder_path, f"{i}.png")
        if check_image_exists(step_img):
            step_images.append(step_img)
        else:
            logger.warning(f"Step image does not exist: {step_img}")
            # If an image is missing, we can choose to stop processing or continue
            break

    # Ensure we have enough images
    if len(step_images) < len(actions):
        logger.warning(f"Not enough images: {len(step_images)} < {len(actions)}")
        # Adjust actions and simplified_actions length to match image count
        actions = actions[:len(step_images)]
        simplified_actions = simplified_actions[:len(step_images)]

    # Merge consecutive movements in the same direction
    if use_english:
        # For English version, first map Chinese actions to English actions, then merge
        simplified_actions_en = []
        for action in simplified_actions:
            if action in ZH_TO_EN_ACTION_MAPPING:
                simplified_actions_en.append(ZH_TO_EN_ACTION_MAPPING[action])
            else:
                simplified_actions_en.append(action)

        # Use English actions and English mergeable movements set for merging
        merged_actions_en, merged_images, merged_counts = merge_consecutive_movements(simplified_actions_en, step_images, MERGEABLE_MOVEMENTS_EN)

        # Map merged English actions back to Chinese actions for case creation
        action_en_to_zh_map = {}
        for i, zh_action in enumerate(simplified_actions):
            if i < len(simplified_actions_en):
                en_action = simplified_actions_en[i]
                if en_action not in action_en_to_zh_map:
                    action_en_to_zh_map[en_action] = []
                action_en_to_zh_map[en_action].append(zh_action)

        # Convert merged English actions back to Chinese actions
        merged_actions = []
        for en_action in merged_actions_en:
            if en_action in action_en_to_zh_map and action_en_to_zh_map[en_action]:
                # Use the first matching Chinese action
                merged_actions.append(action_en_to_zh_map[en_action][0])
            else:
                # If no matching Chinese action is found, use a placeholder
                merged_actions.append("未知动作")
    else:
        # For Chinese version, directly use Chinese actions and Chinese mergeable movements set
        merged_actions, merged_images, merged_counts = merge_consecutive_movements(simplified_actions, step_images, MERGEABLE_MOVEMENTS_ZH)

    # Check if the number of actions after merging exceeds the maximum limit
    if len(merged_actions) > max_actions:
        logger.warning(f"Folder {folder_id} has too many actions after merging ({len(merged_actions)}) exceeding the limit ({max_actions}), skipping")
        return []

    # Generate cases
    cases = []

    # Case with no history
    if len(merged_actions) > 0:
        no_history_case = create_no_history_case(
            folder_id, navigation_target,
            initial_img, merged_actions[0],
            merged_actions, use_english  # Pass the complete action sequence
        )
        cases.append(no_history_case)

    # Cases with history
    for i in range(1, len(merged_actions)):
        history_case = create_history_case(
            folder_id, navigation_target,
            initial_img, merged_images, actions,
            merged_actions, i, use_english,
            merged_counts  # Pass the merged counts
        )
        cases.append(history_case)

    logger.info(f"Folder {folder_id} generated {len(cases)} cases")
    return cases


def process_all_folders(base_path: str, label_path: str, output_path: str, max_actions: int = 32, use_english: bool = True, use_goal_txt: bool = True) -> None:
    """
    Process all folders

    Args:
        base_path: Base path
        label_path: Navigation target file path, if None will use goal.txt in each folder
        output_path: Output file path
        max_actions: Maximum number of actions, folders exceeding this limit will be skipped
        use_english: Whether to use English (default: True)
        use_goal_txt: Whether to use goal.txt in each folder for navigation targets (default: True)
    """
    # Get numeric folders
    folder_paths = get_numeric_folders(base_path)

    # Read navigation targets from label_path if provided
    navigation_targets = []
    if label_path is not None:
        navigation_targets = read_navigation_targets(label_path)

        # Check if navigation targets and folder count match
        if len(navigation_targets) != len(folder_paths):
            logger.warning(f"Navigation target count ({len(navigation_targets)}) does not match folder count ({len(folder_paths)})")
            logger.warning("Will use the shorter list length for matching")

    all_cases = []
    processed_folders = 0
    skipped_folders = 0

    # Process each folder
    for i, folder_path in enumerate(folder_paths):
        # 如果有label_path且已经处理完所有导航目标，则跳过剩余文件夹
        if label_path is not None and i >= len(navigation_targets):
            logger.warning(f"No more navigation targets, skipping remaining folders")
            break

        folder_id = os.path.basename(folder_path)

        # 从goal.txt文件中读取导航目标
        goal_txt_path = os.path.join(folder_path, "goal.txt")
        goal_from_txt = read_goal_from_txt(goal_txt_path)

        # 确定导航目标
        if label_path is None or use_goal_txt:
            # 如果label_path为None或use_goal_txt为True，优先使用goal.txt
            if goal_from_txt:
                # 如果goal.txt存在且有内容，使用其中的目标
                navigation_target = goal_from_txt
                logger.info(f"使用goal.txt中的导航目标: {navigation_target}")
            elif label_path is not None and i < len(navigation_targets):
                # 如果goal.txt不存在或为空，且有label_path，使用label.txt中的目标
                navigation_target = navigation_targets[i]
                logger.info(f"使用label.txt中的导航目标: {navigation_target}")
            else:
                # 如果goal.txt不存在或为空，且没有label_path，跳过此文件夹
                logger.warning(f"文件夹 {folder_id} 没有导航目标，跳过")
                skipped_folders += 1
                continue
        else:
            # 直接使用label.txt中的目标
            navigation_target = navigation_targets[i]
            logger.info(f"使用label.txt中的导航目标: {navigation_target}")

        try:
            cases = process_folder(folder_path, folder_id, navigation_target, max_actions, use_english)
            if cases:
                all_cases.extend(cases)
                processed_folders += 1
            else:
                # If an empty list is returned, it may be because the action count exceeds the limit
                skipped_folders += 1
        except Exception as e:
            logger.error(f"Error processing folder {folder_id}: {e}")
            skipped_folders += 1
            continue

    logger.info(f"Successfully processed {processed_folders}/{len(folder_paths)} folders")
    logger.info(f"Skipped {skipped_folders} folders (possibly due to action count exceeding limit or other errors)")
    logger.info(f"Generated a total of {len(all_cases)} cases")

    # Save as JSONL file
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in all_cases:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        logger.info(f"Dataset saved to: {output_path}")
    except Exception as e:
        logger.error(f"Error saving dataset: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description='Process navigation dataset')
    parser.add_argument('--base_path', type=str, default='/home/<USER>/wzy/NIPS/data/test_video',
                        help='Base path containing image folders')
    parser.add_argument('--label_path', type=str, default=None,
                        help='Navigation target file path, if None will use goal.txt in each folder')
    parser.add_argument('--output_path', type=str, default='/home/<USER>/wzy/NIPS/data/json/navigation_testset.jsonl',
                        help='Output file path')
    parser.add_argument('--list_folders', action='store_true',
                        help='List all folders in the base path')
    parser.add_argument('--show_actions', action='store_true',
                        help='Show action mapping table')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug mode, show more log information')
    parser.add_argument('--no_merge', action='store_true',
                        help='Do not merge consecutive movements in the same direction')
    parser.add_argument('--max_actions', type=int, default=31,
                        help='Maximum number of actions, folders exceeding this limit will be skipped (default: 32)')
    parser.add_argument('--use_chinese', action='store_true',
                        help='Use Chinese instead of English (default: use English)')
    parser.add_argument('--no_goal_txt', action='store_true',
                        help='Do not use goal.txt in each folder for navigation targets (default: use goal.txt if available)')
    args = parser.parse_args()

    # Set language
    use_english = not args.use_chinese

    # If debug mode is enabled, set log level to DEBUG
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug mode enabled")

    # If --show_actions is specified, show action mapping table
    if args.show_actions:
        if use_english:
            print("Action Mapping Table:")
            print("-" * 40)
            print("Chinese Action -> English Action")
            print("-" * 40)
            for zh, en in sorted(ZH_TO_EN_ACTION_MAPPING.items()):
                print(f"{zh:20} -> {en}")

            print("\nMergeable Movements:")
            print("-" * 40)
            for movement in sorted(MERGEABLE_MOVEMENTS_EN):
                print(movement)
        else:
            print("动作映射表:")
            print("-" * 40)
            print("详细动作 -> 简化动作")
            print("-" * 40)
            for detailed, simplified in sorted(ACTION_MAPPING.items()):
                print(f"{detailed:20} -> {simplified}")

            print("\n可合并的运动动作:")
            print("-" * 40)
            for movement in sorted(MERGEABLE_MOVEMENTS_ZH):
                print(movement)

        print(f"\nMaximum actions: {args.max_actions}")
        print(f"Language: {'English' if use_english else 'Chinese'}")
        return

    # If --list_folders is specified, list all folders
    if args.list_folders:
        try:
            folders = [f for f in os.listdir(args.base_path) if os.path.isdir(os.path.join(args.base_path, f))]
            numeric_folders = [f for f in folders if f.isdigit()]
            numeric_folders.sort(key=int)

            print(f"Found {len(numeric_folders)} numeric folders in {args.base_path}:")
            for i, folder in enumerate(numeric_folders):
                print(f"{i}: {folder}")

            print(f"\nOther folders ({len(folders) - len(numeric_folders)}):")
            other_folders = [f for f in folders if not f.isdigit()]
            for folder in other_folders[:10]:  # Only show the first 10
                print(folder)
            if len(other_folders) > 10:
                print(f"... and {len(other_folders) - 10} more")

            return
        except Exception as e:
            logger.error(f"Error listing folders: {e}")
            return

    # If --no_merge is specified, disable action merging
    if args.no_merge:
        global merge_consecutive_movements
        # Rewrite merge function to not perform any merging
        def no_merge(actions, step_images, mergeable_movements):
            return actions, step_images
        merge_consecutive_movements = no_merge
        logger.info("Action merging disabled")

    logger.info(f"Setting maximum action count to: {args.max_actions}")
    logger.info(f"Using language: {'English' if use_english else 'Chinese'}")

    if args.label_path is None:
        logger.info("No label_path provided, will use goal.txt in each folder for navigation targets")
    else:
        logger.info(f"Using label_path: {args.label_path}")
        logger.info(f"Using goal.txt for navigation targets when available: {not args.no_goal_txt}")

    # Process dataset
    process_all_folders(args.base_path, args.label_path, args.output_path, args.max_actions, use_english, not args.no_goal_txt)


if __name__ == '__main__':
    main()
