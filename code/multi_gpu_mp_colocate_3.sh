setup_env() {
    export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,garbage_collection_threshold:0.90
    export TORCH_DISTRIBUTED_AUTO_DESTROY_PROCESS_GROUP=1
    export CUDA_CACHE_DISABLE=1

    # # 限制CUDA预分配内存
    # export PYTORCH_NO_CUDA_MEMORY_CACHING=1

    # # 启用cudnn确定性模式，可能会减少一些内存使用
    # export CUBLAS_WORKSPACE_CONFIG=:4096:8

    # # DeepSpeed优化
    # # 禁用DeepSpeed的激活检查点缓存
    # export DS_DISABLE_ACTIVATION_CACHE=1

    # # 减少DeepSpeed的通信缓冲区大小
    # export DS_COMM_BUFFERS_SIZE=1000000

    # # 启用DeepSpeed的内存高效的梯度累积
    # export DS_USE_MEMORY_EFFICIENT_GRADIENT_ACCUMULATION=1

    # # 启用DeepSpeed的CPU优化器
    # export DS_ENABLE_CPU_OPTIMIZER=1

    # # 设置较低的NCCL缓冲区大小
    # export NCCL_SOCKET_NTHREADS=1
    # export NCCL_NSOCKS_PERTHREAD=1
    # export NCCL_BUFFSIZE=4194304

    # # 设置较低的通信缓冲区大小
    # export NCCL_IB_SL=0
    # export NCCL_IB_TC=0

    # VLLM优化
    # # 禁用VLLM的KV缓存预分配
    # export VLLM_DISABLE_KV_CACHE_PREALLOCATION=1

    # # 启用VLLM的渐进式创建张量并行组
    # export VLLM_ENABLE_PROGRESSIVE_TP_GROUP_CREATION=1

    # # 减少VLLM的PagedAttention缓冲区大小
    # export VLLM_PAGED_ATTENTION_BUFFER_SIZE=512

    # # 启用VLLM的内存高效模式
    # export VLLM_ENABLE_MEMORY_EFFICIENT_MODE=1
}
# 清理GPU显存
clean_gpu_memory() {
    echo "清理GPU显存..."
    # 强制终止所有可能占用GPU的Python进程
    pkill -9 python || true

    # 等待进程完全终止
    sleep 2

    # 使用nvidia-smi重置所有GPU设备
    nvidia-smi --gpu-reset 2>/dev/null || true

    # 等待GPU重置完成
    sleep 3

    # 显示清理后的GPU状态
    echo "清理后的GPU状态:"
    nvidia-smi
}

# 检查文件和目录
check_paths() {
    echo "检查路径..."
    MODEL_PATH="/home/<USER>/wzy/NIPS/checkpoint-2200"
    DATA_PATH="/home/<USER>/wzy/NIPS/data/navigation_qa_instruction_5_26.jsonl"

    if [ ! -d "$MODEL_PATH" ]; then
        echo "错误: 模型目录不存在: $MODEL_PATH"
        exit 1
    fi

    if [ ! -f "$DATA_PATH" ]; then
        echo "错误: 数据集文件不存在: $DATA_PATH"
        exit 1
    fi

    # 创建输出目录
    mkdir -p output_video_grpo
}

# 获取可用的GPU数量
get_gpu_info() {
    NUM_GPUS=$(nvidia-smi --query-gpu=gpu_name --format=csv,noheader | wc -l)
    echo "检测到 $NUM_GPUS 个可用GPU"
    nvidia-smi

    # 检查每个GPU的显存
    for i in $(seq 0 $((NUM_GPUS-1))); do
        FREE_MEM=$(nvidia-smi --query-gpu=memory.free --format=csv -i $i | grep -v memory.free)
        echo "GPU $i 可用显存: $FREE_MEM"
    done
}


# 启动一致性检验模型服务
start_consistency_service() {
    echo "启动一致性检验模型服务..."
    # 给脚本执行权限
    chmod +x /root/autodl-tmp/GRPO_word/start_consistency_service.sh
    # 启动服务
    /root/autodl-tmp/GRPO_word/start_consistency_service.sh
    # 等待服务完全启动
    sleep 15
}

# 主函数
main() {
    # 设置环境变量以优化显存使用
    setup_env

    # 清理GPU显存，确保训练前显存状态干净

    # check_paths
    get_gpu_info

    # # 启动一致性检验模型服务在GPU 4上
    # start_consistency_service

    echo "开始全量微调训练..."

    # 确保在正确的工作目录下运行
    cd /home/<USER>/wzy


    # 定义system提示词
    # SYSTEM_PROMPT="A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> \n<answer> answer here </answer>. Ensure that your answer is consistent with and directly derived from your thinking process, maintaining logical coherence between the two sections. User: . Assistant:"
        # --system "${SYSTEM_PROMPT}" \

    CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 \
    NPROC_PER_NODE=8 \
    swift rlhf \
    --rlhf_type grpo \
    --model "/home/<USER>/wzy/NIPS/checkpoint-2200" \
    --train_type full \
    --model_type qwen2_5_vl \
    --dataset "/home/<USER>/wzy/NIPS/data/navigation_qa_instruction_5_26.jsonl" \
    --torch_dtype bfloat16 \
    --num_train_epochs 2 \
    --reward_weights 0.1 0.9 \
    --external_plugins /home/<USER>/wzy/NIPS/code/action_reward_score.py /home/<USER>/wzy/NIPS/code/format_tag_reward.py \
    --max_length 8192 \
    --eval_steps 200000 \
    --save_steps 200 \
    --use_vllm true \
    --vllm_gpu_memory_utilization 0.6 \
    --vllm_limit_mm_per_prompt '{"image": 9}' \
    --save_only_model true \
    --target_modules all-linear \
    --save_total_limit 200 \
    --per_device_train_batch_size 1 \
    --per_device_eval_batch_size 1 \
    --learning_rate 5e-7 \
    --logging_steps 1 \
    --output_dir /home/<USER>/wzy/NIPS/port \
    --warmup_ratio 0.05 \
    --dataloader_num_workers 8 \
    --max_completion_length 2048 \
    --reward_funcs format_tag_score action_reward_score \
    --gradient_accumulation_steps 4 \
    --deepspeed zero3 \
    --num_generations 8 \
    --num_infer_workers 8 \
    --num_iterations 1 \
    --temperature 1.0 \
    --beta 0.001 \
    --gradient_checkpointing \
    --report_to wandb \
    --log_completions true \
    --log_level debug \
    --log_level_replica warning \
    --gradient_checkpointing \
    2>&1 | tee /home/<USER>/wzy/NIPS/code/grpo.log
}
# 运行主函数
main
