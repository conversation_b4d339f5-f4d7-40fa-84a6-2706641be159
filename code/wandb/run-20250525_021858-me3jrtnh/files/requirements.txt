colorama==0.4.6
psutil==6.1.0
setproctitle==1.2.2
psutil==7.0.0
protobuf==4.25.7
pillow==11.2.1
orjson==3.10.18
wheel==0.45.1
pip==25.1
sortedcontainers==2.4.0
sentencepiece==0.2.0
pytz==2025.2
pydub==0.25.1
nvidia-cusparselt-cu12==0.6.2
mpmath==1.3.0
jieba==0.42.1
crcmod==1.7
cpm-kernels==1.0.11
addict==2.4.0
zstandard==0.23.0
zipp==3.21.0
xxhash==3.5.0
websockets==15.0.1
urllib3==2.4.0
tzdata==2025.2
typing_extensions==4.13.2
tqdm==4.67.1
tomlkit==0.13.2
tensorboard-data-server==0.7.2
sniffio==1.3.1
six==1.17.0
simplejson==3.20.1
shellingham==1.5.4
propcache==0.3.1
packaging==25.0
setuptools==69.5.1
semantic-version==2.10.0
safetensors==0.5.3
ruff==0.11.8
regex==2024.11.6
PyYAML==6.0.2
python-multipart==0.0.20
pyparsing==3.2.3
Pygments==2.19.1
pycryptodome==3.22.0
pycparser==2.22
pyarrow==20.0.0
nvidia-nvtx-cu12==12.4.127
nvidia-nvjitlink-cu12==12.4.127
nvidia-nccl-cu12==2.21.5
nvidia-curand-cu12==**********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cublas-cu12==********
numpy==1.26.4
networkx==3.4.2
mdurl==0.1.2
MarkupSafe==3.0.2
Markdown==3.8
kiwisolver==1.4.8
joblib==1.5.0
jmespath==0.10.0
jiter==0.9.0
idna==3.10
hf-xet==1.1.0
h11==0.16.0
grpcio==1.71.0
groovy==0.1.2
future==1.0.0
fsspec==2025.3.0
frozenlist==1.6.0
fonttools==4.57.0
filelock==3.18.0
ffmpy==0.5.0
exceptiongroup==1.2.2
einops==0.8.1
distro==1.9.0
dill==0.3.8
dacite==1.9.2
cycler==0.12.1
click==8.1.8
charset-normalizer==3.4.2
certifi==2025.4.26
attrs==25.3.0
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
aiofiles==24.1.0
absl-py==2.2.2
Werkzeug==3.1.3
uvicorn==0.34.2
typing-inspection==0.4.0
fastrlock==0.8.3
scipy==1.15.2
rouge==1.0.1
requests==2.32.3
python-dateutil==2.9.0.post0
pydantic_core==2.33.2
nvidia-cusparse-cu12==**********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
nltk==3.9.1
multiprocess==0.70.16
multidict==6.4.3
markdown-it-py==3.0.0
Jinja2==3.1.6
httptools==0.6.4
httpcore==1.0.9
contourpy==1.3.2
cffi==1.17.1
binpacking==1.5.2
attrdict==2.0.1
anyio==4.9.0
aiosignal==1.3.2
yarl==1.20.0
tiktoken==0.9.0
tensorboard==2.19.0
starlette==0.46.2
rich==14.0.0
pydantic==2.11.4
pandas==2.2.3
nvidia-cusolver-cu12==********
modelscope==1.19.1
matplotlib==3.10.1
huggingface-hub==0.31.1
httpx==0.28.1
cryptography==44.0.3
typer==0.15.3
vllm==0.7.3
tokenizers==0.21.1
safehttpx==0.1.6
openai==1.77.0
gradio_client==1.10.0
fastapi==0.115.12
aliyun-python-sdk-core==2.16.0
aiohttp==3.11.18
transformers==4.51.3
gradio==5.29.0
aliyun-python-sdk-kms==2.16.5
accelerate==1.6.0
transformers-stream-generator==0.0.5
peft==0.15.2
oss2==2.19.1
datasets==3.6.0
trl==0.17.0
ms_swift==3.4.0
watchdog==6.0.0
webencodings==0.5.1
wcwidth==0.2.13
pure_eval==0.2.3
ptyprocess==0.7.0
fastjsonschema==2.21.1
websocket-client==1.8.0
webcolors==24.11.1
uri-template==1.3.0
types-python-dateutil==2.9.0.20241206
traitlets==5.14.3
tornado==6.4.2
tomli==2.2.1
tinycss2==1.4.0
soupsieve==2.7
Send2Trash==1.8.3
rpds-py==0.24.0
rfc3986-validator==0.1.1
rfc3339-validator==0.1.4
pyzmq==26.4.0
python-json-logger==3.3.0
prompt_toolkit==3.0.51
prometheus_client==0.21.1
platformdirs==4.3.8
pexpect==4.9.0
parso==0.8.4
pandocfilters==1.5.1
overrides==7.7.0
nest-asyncio==1.6.0
mistune==3.1.3
jupyterlab_pygments==0.3.0
jsonpointer==3.0.0
json5==0.12.0
fqdn==1.5.1
executing==2.2.0
defusedxml==0.7.1
decorator==5.2.1
debugpy==1.8.14
bleach==6.2.0
babel==2.17.0
async-lru==2.0.5
asttokens==3.0.0
terminado==0.18.1
stack-data==0.6.3
referencing==0.36.2
matplotlib-inline==0.1.7
jupyter_core==5.7.2
jedi==0.19.2
comm==0.2.2
beautifulsoup4==4.13.4
arrow==1.3.0
argon2-cffi-bindings==21.2.0
jupyter_server_terminals==0.5.3
jupyter_client==8.6.3
jsonschema-specifications==2025.4.1
isoduration==20.11.0
ipython==8.36.0
argon2-cffi==23.1.0
jsonschema==4.23.0
ipykernel==6.29.5
nbformat==5.10.4
nbclient==0.10.2
jupyter-events==0.12.0
nbconvert==7.16.6
jupyter_server==2.15.0
notebook_shim==0.2.4
jupyterlab_server==2.27.3
jupyter-lsp==2.2.5
jupyterlab==4.4.2
smmap==5.0.2
setproctitle==1.3.6
sentry-sdk==2.27.0
docker-pycreds==0.4.0
gitdb==4.0.12
GitPython==3.1.44
wandb==0.19.11
py-cpuinfo==9.0.0
nvidia-ml-py==12.575.51
hjson==3.1.0
ninja==********
msgpack==1.1.0
pybind11==2.13.6
av==14.3.0
qwen-vl-utils==0.0.11
ray==2.40.0
torchaudio==2.5.1
llvmlite==0.43.0
blake3==1.0.4
wrapt==1.17.2
uvloop==0.21.0
sympy==1.13.1
python-dotenv==1.1.0
pycountry==24.6.1
partial-json-parser==*******.post5
opentelemetry-semantic-conventions-ai==0.4.7
opencv-python-headless==*********
msgspec==0.19.0
pytest==8.3.5
llguidance==0.7.19
lark==1.2.2
interegular==0.3.3
importlib_metadata==8.0.0
dnspython==2.7.0
diskcache==5.6.3
cupy-cuda12x==13.4.1
cloudpickle==3.1.1
cachetools==5.5.2
astor==0.8.1
airportsdata==20250224
watchfiles==1.0.5
opentelemetry-proto==1.26.0
googleapis-common-protos==1.70.0
email_validator==2.2.0
depyf==0.18.0
Deprecated==1.2.18
rich-toolkit==0.14.5
prometheus-fastapi-instrumentator==7.1.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-api==1.26.0
lm-format-enforcer==0.10.11
iniconfig==2.1.0
torch==2.5.1
outlines_core==0.1.26
opentelemetry-semantic-conventions==0.47b0
mistral_common==1.5.4
fastapi-cli==0.0.7
xgrammar==0.1.11
torchvision==0.20.1
outlines==0.1.11
opentelemetry-sdk==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp==1.26.0
numba==0.60.0
decord==0.6.0
triton==3.1.0
pluggy==1.5.0
gguf==0.10.0
xformers==0.0.28.post3
compressed-tensors==0.9.1
deepspeed==0.14.5
