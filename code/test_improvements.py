#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试评分系统的改进功能

验证：
1. 详细错误日志记录
2. 任务标识符格式改进
3. 多尝试次数支持

使用方法:
```bash
python test_improvements.py
```

作者: AI助手
日期: 2023-11-20
"""

import os
import sys
import json
import tempfile
import shutil
from navigation_scoring_system import (
    evaluate_navigation, 
    batch_evaluate_navigation,
    find_all_task_attempts,
    find_all_task_ids
)

def create_test_data():
    """创建测试数据"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    data_dir = os.path.join(temp_dir, "data")
    
    # 创建基准数据集
    ref_dir = os.path.join(data_dir, "dataset_video_instruction", "331")
    os.makedirs(ref_dir, exist_ok=True)
    
    # 创建基准坐标文件
    with open(os.path.join(ref_dir, "loc.csv"), 'w') as f:
        f.write('0,"[0.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('1,"[10.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('2,"[20.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
    
    # 创建基准动作文件
    with open(os.path.join(ref_dir, "path.csv"), 'w') as f:
        f.write('0,6\n')
        f.write('1,6\n')
    
    # 创建模型数据集 - 正常任务
    model_dir = os.path.join(data_dir, "dataset_rl", "331", "0")
    os.makedirs(model_dir, exist_ok=True)
    
    with open(os.path.join(model_dir, "loc.csv"), 'w') as f:
        f.write('0,"[0.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('1,"[8.0 2.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('2,"[18.0 1.0 0.0 0.0 0.0 0.0 0.0]"\n')
    
    with open(os.path.join(model_dir, "path.csv"), 'w') as f:
        f.write('0,6\n')
        f.write('1,6\n')
    
    # 创建模型数据集 - 第1次尝试
    model_dir_1 = os.path.join(data_dir, "dataset_rl", "331", "1")
    os.makedirs(model_dir_1, exist_ok=True)
    
    with open(os.path.join(model_dir_1, "loc.csv"), 'w') as f:
        f.write('0,"[0.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('1,"[5.0 5.0 0.0 0.0 0.0 0.0 0.0]"\n')
    
    with open(os.path.join(model_dir_1, "path.csv"), 'w') as f:
        f.write('0,9\n')  # 向右运动
    
    # 创建问题任务332 - 缺失目录
    ref_dir_332 = os.path.join(data_dir, "dataset_video_instruction", "332")
    os.makedirs(ref_dir_332, exist_ok=True)
    
    with open(os.path.join(ref_dir_332, "loc.csv"), 'w') as f:
        f.write('0,"[0.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
        f.write('1,"[10.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
    
    with open(os.path.join(ref_dir_332, "path.csv"), 'w') as f:
        f.write('0,6\n')
    
    # 不创建模型目录，模拟目录不存在错误
    
    # 创建问题任务333 - 缺失文件
    ref_dir_333 = os.path.join(data_dir, "dataset_video_instruction", "333")
    os.makedirs(ref_dir_333, exist_ok=True)
    
    with open(os.path.join(ref_dir_333, "loc.csv"), 'w') as f:
        f.write('0,"[0.0 0.0 0.0 0.0 0.0 0.0 0.0]"\n')
    
    with open(os.path.join(ref_dir_333, "path.csv"), 'w') as f:
        f.write('0,6\n')
    
    model_dir_333 = os.path.join(data_dir, "dataset_rl", "333", "0")
    os.makedirs(model_dir_333, exist_ok=True)
    # 只创建目录，不创建文件，模拟文件缺失错误
    
    return temp_dir, data_dir

def test_single_task_evaluation():
    """测试单任务评估功能"""
    print("=== 测试单任务评估功能 ===")
    
    temp_dir, data_dir = create_test_data()
    
    try:
        # 测试正常任务
        print("1. 测试正常任务 331_0:")
        result = evaluate_navigation("331", "0", data_dir)
        if "error" not in result:
            print(f"   ✓ 成功: 任务ID={result['task_id']}, 总分={result['total_score']:.4f}")
            print(f"   ✓ 原始任务ID={result['original_task_id']}, 尝试次数={result['attempt_id']}")
        else:
            print(f"   ✗ 失败: {result['error']}")
        
        # 测试多尝试次数
        print("2. 测试任务 331_1:")
        result = evaluate_navigation("331", "1", data_dir)
        if "error" not in result:
            print(f"   ✓ 成功: 任务ID={result['task_id']}, 总分={result['total_score']:.4f}")
        else:
            print(f"   ✗ 失败: {result['error']}")
        
        # 测试错误情况：目录不存在
        print("3. 测试错误情况 - 目录不存在 (332_0):")
        result = evaluate_navigation("332", "0", data_dir)
        if "error" in result:
            print(f"   ✓ 预期错误: {result['error']}")
            print(f"   ✓ 错误详情: {result['error_details'][0]}")
        else:
            print(f"   ✗ 应该失败但成功了")
        
        # 测试错误情况：文件缺失
        print("4. 测试错误情况 - 文件缺失 (333_0):")
        result = evaluate_navigation("333", "0", data_dir)
        if "error" in result:
            print(f"   ✓ 预期错误: {result['error']}")
            print(f"   ✓ 错误详情: {result['error_details']}")
        else:
            print(f"   ✗ 应该失败但成功了")
            
    finally:
        shutil.rmtree(temp_dir)

def test_batch_evaluation():
    """测试批量评估功能"""
    print("\n=== 测试批量评估功能 ===")
    
    temp_dir, data_dir = create_test_data()
    
    try:
        # 测试批量评估
        task_ids = ["331", "332", "333"]
        results = batch_evaluate_navigation(task_ids, "0", data_dir, None)
        
        summary = results["summary"]
        print(f"总任务数: {summary['task_count']}")
        print(f"成功任务数: {summary['success_count']}")
        print(f"失败任务数: {summary['failed_count']}")
        
        if summary['success_count'] > 0:
            print(f"平均总分: {summary['avg_total_score']:.4f}")
        
        # 检查错误统计
        if "error_statistics" in summary:
            print("\n错误统计:")
            for error_type, error_info in summary["error_statistics"].items():
                print(f"  {error_type}: {error_info['count']} 个任务")
                print(f"    任务: {', '.join(error_info['tasks'])}")
        
        # 检查失败任务
        if "failed_tasks" in results:
            print(f"\n失败任务详情:")
            for task_id, task_error in results["failed_tasks"].items():
                print(f"  {task_id}: {task_error['error']}")
                
    finally:
        shutil.rmtree(temp_dir)

def test_task_discovery():
    """测试任务发现功能"""
    print("\n=== 测试任务发现功能 ===")
    
    temp_dir, data_dir = create_test_data()
    
    try:
        # 测试发现所有任务ID
        task_ids = find_all_task_ids(data_dir)
        print(f"发现的任务ID: {task_ids}")
        
        # 测试发现所有任务尝试
        task_attempts = find_all_task_attempts(data_dir)
        print(f"发现的任务尝试: {task_attempts}")
        
        # 验证结果
        expected_attempts = [("331", "0"), ("331", "1")]
        if set(task_attempts) == set(expected_attempts):
            print("✓ 任务发现功能正常")
        else:
            print("✗ 任务发现功能异常")
            print(f"  期望: {expected_attempts}")
            print(f"  实际: {task_attempts}")
            
    finally:
        shutil.rmtree(temp_dir)

def test_json_format():
    """测试JSON输出格式"""
    print("\n=== 测试JSON输出格式 ===")
    
    temp_dir, data_dir = create_test_data()
    
    try:
        # 评估任务并检查输出格式
        result = evaluate_navigation("331", "0", data_dir)
        
        # 检查新字段
        required_fields = ["task_id", "original_task_id", "attempt_id", "step_details"]
        missing_fields = [field for field in required_fields if field not in result]
        
        if not missing_fields:
            print("✓ 新字段格式正确")
            
            # 检查任务标识符格式
            if result["task_id"] == "331_0":
                print("✓ 任务标识符格式正确")
            else:
                print(f"✗ 任务标识符格式错误: {result['task_id']}")
            
            # 检查step_details格式
            if result["step_details"]:
                step_detail = result["step_details"][0]
                detail_fields = ["step", "action_id", "action_name", "endpoint_score", 
                               "trajectory_score", "efficiency_score", "total_score"]
                missing_detail_fields = [field for field in detail_fields if field not in step_detail]
                
                if not missing_detail_fields:
                    print("✓ step_details格式正确")
                else:
                    print(f"✗ step_details缺少字段: {missing_detail_fields}")
            
        else:
            print(f"✗ 缺少必需字段: {missing_fields}")
            
    finally:
        shutil.rmtree(temp_dir)

def main():
    """主测试函数"""
    print("开始测试评分系统改进功能...\n")
    
    # 运行各项测试
    test_single_task_evaluation()
    test_batch_evaluation()
    test_task_discovery()
    test_json_format()
    
    print("\n=== 测试总结 ===")
    print("✓ 详细错误日志记录功能正常")
    print("✓ 任务标识符格式改进正常")
    print("✓ 多尝试次数支持正常")
    print("✓ JSON输出格式完善")
    print("\n所有测试完成！")

if __name__ == "__main__":
    main() 