#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据集格式和奖励函数

该脚本用于测试修改后的数据集格式和奖励函数是否能够正确处理场景ID和其他字段。
"""

import json
from navigation_accuracy_reward import NavigationAccuracy

def main():
    # 创建奖励函数实例
    reward_func = NavigationAccuracy()

    # 测试场景1：模拟数据集格式
    print("\n测试场景1：模拟数据集格式")

    # 模拟数据集样本
    sample = {
        "messages": [
            {"role": "system", "content": "You are a visual navigation decision system."},
            {"role": "user", "content": "Navigation target: Exit\nCurrent position image:\n<image>\n\nPlease select the best next action."}
        ],
        "images": ["path/to/image.png"],
        "solution": "Move forward",
        "folder_id": "94",  # 字符串格式的folder_id
        "current_index": 1,
        "action_sequence": ["Look down", "Move forward", "Move right"],  # 顶层的action_sequence
        "history_actions": ["Look down"]  # 顶层的history_actions
    }

    # 模拟模型输出
    completions = ["<answer>Move forward</answer>"]
    solution = ["Move forward"]

    # 调用奖励函数
    # 从sample中移除solution，避免参数重复
    sample_copy = sample.copy()
    if 'solution' in sample_copy:
        del sample_copy['solution']
    rewards = reward_func(completions, solution, **sample_copy, debug=True)
    print(f"奖励: {rewards}")

    # 测试场景2：folder_id是列表的情况
    print("\n测试场景2：folder_id是列表的情况")

    # 修改sample，使folder_id为列表
    sample["folder_id"] = ["94"]

    # 调用奖励函数
    # 从sample中移除solution，避免参数重复
    sample_copy = sample.copy()
    if 'solution' in sample_copy:
        del sample_copy['solution']
    rewards = reward_func(completions, solution, **sample_copy, debug=True)
    print(f"奖励: {rewards}")

    # 测试场景3：history_actions是嵌套列表的情况
    print("\n测试场景3：history_actions是嵌套列表的情况")

    # 修改sample，使history_actions为嵌套列表
    sample["history_actions"] = [["Look down"]]

    # 调用奖励函数
    rewards = reward_func(completions, solution, **sample, debug=True)
    print(f"奖励: {rewards}")

    # 测试场景4：正确答案与模型输出不匹配
    print("\n测试场景4：正确答案与模型输出不匹配")

    # 修改模型输出
    completions = ["<answer>Move right</answer>"]

    # 调用奖励函数
    rewards = reward_func(completions, solution, **sample, debug=True)
    print(f"奖励: {rewards}")

if __name__ == "__main__":
    main()
