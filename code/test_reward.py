#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试导航准确率奖励函数

该脚本用于测试 navigation_accuracy_reward.py 中的奖励函数，
特别是场景ID提取和软性奖励机制。
"""

import json
from navigation_accuracy_reward import NavigationAccuracy

def main():
    # 创建奖励函数实例
    reward_func = NavigationAccuracy()
    
    # 测试场景1：直接提供folder_id
    print("\n测试场景1：直接提供folder_id")
    completions = ["<answer>Move forward</answer>"]
    solution = ["Move forward"]
    rewards = reward_func(completions, solution, folder_id="test_scene_1", debug=True)
    print(f"奖励: {rewards}")
    
    # 测试场景2：从message字典中提取folder_id
    print("\n测试场景2：从message字典中提取folder_id")
    message = {
        "folder_id": "test_scene_2",
        "action_sequence": ["Move forward", "Move right", "Move up", "Move down"],
        "current_index": 1
    }
    completions = ["<answer>Move right</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, message=message, debug=True)
    print(f"奖励: {rewards}")
    
    # 测试场景3：从message.messages中提取folder_id
    print("\n测试场景3：从message.messages中提取folder_id")
    message = {
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "folder_id: \"test_scene_3\"", "action_sequence": ["Move forward", "Move right", "Move up"]}
        ]
    }
    completions = ["<answer>Move right</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, message=message, debug=True)
    print(f"奖励: {rewards}")
    
    # 测试场景4：从历史动作构建动作序列
    print("\n测试场景4：从历史动作构建动作序列")
    completions = ["<answer>Move up</answer>"]
    solution = ["Move up"]
    rewards = reward_func(completions, solution, folder_id="test_scene_4", history_actions=["Move forward", "Move right"], debug=True)
    print(f"奖励: {rewards}")
    
    # 测试场景5：软性奖励机制
    print("\n测试场景5：软性奖励机制")
    action_sequence = ["Move forward", "Move right", "Move up", "Move down", "Move left"]
    
    # 测试正确答案
    completions = ["<answer>Move right</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, folder_id="test_scene_5", action_sequence=action_sequence, current_index=1, debug=True)
    print(f"正确答案奖励: {rewards}")
    
    # 测试未来动作（预测往后1步）
    completions = ["<answer>Move up</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, folder_id="test_scene_5", action_sequence=action_sequence, current_index=1, debug=True)
    print(f"预测往后1步奖励: {rewards}")
    
    # 测试未来动作（预测往后2步）
    completions = ["<answer>Move down</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, folder_id="test_scene_5", action_sequence=action_sequence, current_index=1, debug=True)
    print(f"预测往后2步奖励: {rewards}")
    
    # 测试错误答案
    completions = ["<answer>Move backward</answer>"]
    solution = ["Move right"]
    rewards = reward_func(completions, solution, folder_id="test_scene_5", action_sequence=action_sequence, current_index=1, debug=True)
    print(f"错误答案奖励: {rewards}")
    
    # 测试场景6：动作合并检测
    print("\n测试场景6：动作合并检测")
    completions = ["<answer>Move down</answer>\nStep 2: After executing 3 times action \"Move down\", the position is:"]
    solution = ["Move down"]
    rewards = reward_func(completions, solution, folder_id="test_scene_6", debug=True)
    print(f"动作合并奖励: {rewards}")
    
    # 测试场景7：从数据集样本中提取字段
    print("\n测试场景7：从数据集样本中提取字段")
    sample = {
        "folder_id": "test_scene_7",
        "action_sequence": ["Move forward", "Move right", "Move up", "Move down"],
        "current_index": 2,
        "history_actions": ["Move forward", "Move right"]
    }
    completions = ["<answer>Move up</answer>"]
    solution = ["Move up"]
    rewards = reward_func(completions, solution, sample=sample, debug=True)
    print(f"从样本提取字段奖励: {rewards}")
    
    # 测试场景8：从solution中提取folder_id
    print("\n测试场景8：从solution中提取folder_id")
    completions = ["<answer>Move forward</answer>"]
    solution = ["folder_id: \"test_scene_8\", Move forward"]
    rewards = reward_func(completions, solution, debug=True)
    print(f"从solution提取folder_id奖励: {rewards}")

if __name__ == "__main__":
    main()
