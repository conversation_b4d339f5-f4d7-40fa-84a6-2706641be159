#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GRPO数据集构建器单元测试

该模块包含对GRPO数据集构建器的单元测试，验证其功能的正确性。

使用方法:
```bash
python test_grpo_dataset_builder.py
```

作者: AI助手
日期: 2023-11-20
"""

import os
import json
import tempfile
import unittest
import numpy as np
from unittest.mock import patch, mock_open

# 导入要测试的模块
from grpo_dataset_builder import (
    extract_samples_from_scores,
    apply_action_to_location,
    calculate_action_rewards,
    build_grpo_sample,
    generate_statistics_report
)


class TestGRPODatasetBuilder(unittest.TestCase):
    """GRPO数据集构建器测试类"""
    
    def setUp(self):
        """设置测试数据"""
        # 模拟评分结果数据
        self.mock_scores = {
            "331_0": {
                "task_id": "331_0",
                "original_task_id": "331",
                "attempt_id": "0",
                "step_details": [
                    {
                        "step": 0,
                        "action_id": "6",
                        "action_name": "向前运动",
                        "endpoint_score": 0.5,
                        "trajectory_score": 0.3,
                        "efficiency_score": 0.1,
                        "total_score": 0.9
                    },
                    {
                        "step": 1,
                        "action_id": "7",
                        "action_name": "向后运动",
                        "endpoint_score": -0.3,
                        "trajectory_score": -0.2,
                        "efficiency_score": 0.0,
                        "total_score": -0.5
                    }
                ]
            },
            "summary": {
                "task_count": 1,
                "success_count": 1
            }
        }
        
        # 模拟坐标数据
        self.mock_coords = [
            np.array([100.0, 200.0, 50.0, 0.0, 0.0, 0.0, 0.0]),
            np.array([110.0, 200.0, 50.0, 0.0, 0.0, 0.0, 0.0]),
            np.array([120.0, 200.0, 50.0, 0.0, 0.0, 0.0, 0.0])
        ]
    
    def test_extract_samples_from_scores(self):
        """测试样本提取功能"""
        negative_samples, positive_samples = extract_samples_from_scores(self.mock_scores, 0.0)
        
        # 验证负样本
        self.assertEqual(len(negative_samples), 1)
        self.assertEqual(negative_samples[0]["step"], 1)
        self.assertEqual(negative_samples[0]["total_score"], -0.5)
        
        # 验证正样本
        self.assertEqual(len(positive_samples), 1)
        self.assertEqual(positive_samples[0]["step"], 0)
        self.assertEqual(positive_samples[0]["total_score"], 0.9)
    
    def test_apply_action_to_location(self):
        """测试动作应用功能"""
        current_loc = np.array([100.0, 200.0, 50.0, 0.0, 0.0, 0.0, 0.0])
        
        # 测试向前运动
        new_loc = apply_action_to_location(current_loc, "向前运动")
        expected = np.array([110.0, 200.0, 50.0, 0.0, 0.0, 0.0, 0.0])
        np.testing.assert_array_equal(new_loc, expected)
        
        # 测试向左运动
        new_loc = apply_action_to_location(current_loc, "向左运动")
        expected = np.array([100.0, 190.0, 50.0, 0.0, 0.0, 0.0, 0.0])
        np.testing.assert_array_equal(new_loc, expected)
        
        # 测试向上运动
        new_loc = apply_action_to_location(current_loc, "向上运动")
        expected = np.array([100.0, 200.0, 40.0, 0.0, 0.0, 0.0, 0.0])
        np.testing.assert_array_equal(new_loc, expected)
        
        # 测试左旋转
        new_loc = apply_action_to_location(current_loc, "向左旋转")
        expected = np.array([100.0, 200.0, 50.0, 0.0, 0.0, -0.39269908169872414, 0.0])
        np.testing.assert_array_almost_equal(new_loc, expected)
        
        # 测试向上看
        new_loc = apply_action_to_location(current_loc, "向上看")
        expected = np.array([100.0, 200.0, 50.0, 0.0, 0.0, 0.0, 45.0])
        np.testing.assert_array_equal(new_loc, expected)
    
    def test_calculate_action_rewards(self):
        """测试动作奖励计算功能"""
        current_loc = self.mock_coords[0]
        reference_coords = self.mock_coords
        
        # 测试负样本奖励计算
        rewards = calculate_action_rewards(current_loc, reference_coords, is_positive_sample=False)
        
        # 验证返回的动作数量
        self.assertEqual(len(rewards), 10)
        
        # 验证每个动作都有完整的奖励分解
        for action, reward_dict in rewards.items():
            self.assertIn("endpoint_score", reward_dict)
            self.assertIn("trajectory_score", reward_dict)
            self.assertIn("efficiency_score", reward_dict)
            self.assertIn("total_reward", reward_dict)
            
            # 负样本的效率评分应该为0
            self.assertEqual(reward_dict["efficiency_score"], 0.0)
        
        # 测试正样本奖励计算
        rewards_positive = calculate_action_rewards(current_loc, reference_coords, is_positive_sample=True)
        
        # 正样本的效率评分应该大于0
        for action, reward_dict in rewards_positive.items():
            self.assertGreater(reward_dict["efficiency_score"], 0.0)
    
    def test_build_grpo_sample(self):
        """测试GRPO样本构建功能"""
        sample = {
            "task_id": "331_0",
            "original_task_id": "331",
            "attempt_id": "0",
            "step": 0,
            "action_id": "6",
            "action_name": "向前运动",
            "endpoint_score": 0.5,
            "trajectory_score": 0.3,
            "efficiency_score": 0.1,
            "total_score": 0.9
        }
        
        model_coords = self.mock_coords
        model_actions = ["向前运动", "向后运动"]
        reference_coords = self.mock_coords
        navigation_target = "测试导航目标"
        
        # 模拟图像文件存在
        with patch('os.path.exists', return_value=True):
            grpo_sample = build_grpo_sample(
                sample, model_coords, model_actions, reference_coords, navigation_target
            )
        
        # 验证GRPO样本结构
        self.assertIsNotNone(grpo_sample)
        self.assertEqual(grpo_sample["task_id"], "331_0")
        self.assertEqual(grpo_sample["original_task_id"], "331")
        self.assertEqual(grpo_sample["step"], 0)
        self.assertEqual(grpo_sample["sample_type"], "positive")
        self.assertEqual(grpo_sample["navigation_target"], "测试导航目标")
        
        # 验证动作奖励
        self.assertIn("action_rewards", grpo_sample)
        self.assertEqual(len(grpo_sample["action_rewards"]), 10)
        
        # 验证英文动作名称
        self.assertIn("Move forward", grpo_sample["action_rewards"])
        self.assertIn("Move backward", grpo_sample["action_rewards"])
        
        # 验证元数据
        self.assertIn("metadata", grpo_sample)
        self.assertEqual(grpo_sample["metadata"]["reference_steps"], 2)
        self.assertEqual(grpo_sample["metadata"]["model_steps"], 2)
    
    def test_generate_statistics_report(self):
        """测试统计报告生成功能"""
        # 构建模拟数据集
        mock_dataset = {
            "samples": [
                {
                    "original_task_id": "331",
                    "sample_type": "negative",
                    "model_action": "向前运动",
                    "action_rewards": {
                        "Move forward": {
                            "endpoint_score": 0.1,
                            "trajectory_score": -0.2,
                            "efficiency_score": 0.0,
                            "total_reward": -0.1
                        }
                    }
                },
                {
                    "original_task_id": "331",
                    "sample_type": "positive",
                    "model_action": "向后运动",
                    "action_rewards": {
                        "Move backward": {
                            "endpoint_score": 0.3,
                            "trajectory_score": 0.2,
                            "efficiency_score": 0.1,
                            "total_reward": 0.6
                        }
                    }
                }
            ],
            "statistics": {
                "total_samples": 2,
                "negative_samples": 1,
                "positive_samples": 1,
                "failed_samples": 0,
                "task_count": 1,
                "task_distribution": {
                    "331": {"negative": 1, "positive": 1}
                },
                "action_frequency": {
                    "向前运动": 1,
                    "向后运动": 1
                }
            }
        }
        
        # 生成统计报告
        report = generate_statistics_report(mock_dataset)
        
        # 验证报告结构
        self.assertIn("dataset_summary", report)
        self.assertIn("reward_distribution", report)
        self.assertIn("sample_type_distribution", report)
        self.assertIn("action_frequency", report)
        self.assertIn("task_contributions", report)
        
        # 验证数据集摘要
        summary = report["dataset_summary"]
        self.assertEqual(summary["total_samples"], 2)
        self.assertEqual(summary["negative_samples"], 1)
        self.assertEqual(summary["positive_samples"], 1)
        self.assertEqual(summary["negative_ratio"], 0.5)
        
        # 验证奖励分布
        reward_dist = report["reward_distribution"]
        self.assertIn("endpoint_scores", reward_dist)
        self.assertIn("trajectory_scores", reward_dist)
        self.assertIn("efficiency_scores", reward_dist)
        self.assertIn("total_rewards", reward_dist)
        
        # 验证任务贡献度
        task_contributions = report["task_contributions"]
        self.assertEqual(len(task_contributions), 1)
        self.assertEqual(task_contributions[0]["task_id"], "331")
        self.assertEqual(task_contributions[0]["total_samples"], 2)


def run_tests():
    """运行所有测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()
