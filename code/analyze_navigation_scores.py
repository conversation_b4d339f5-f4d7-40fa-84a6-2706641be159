#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析导航评分结果

该脚本用于分析导航评分系统生成的评分结果，找出表现不佳的决策点，
用于后续GRPO训练。

使用方法:
```bash
python analyze_navigation_scores.py --input_file navigation_scores.json --output_file bad_decisions.json --threshold 0.3
```

作者: AI助手
日期: 2023-11-20
"""

import os
import json
import argparse
from typing import Dict, List, Any


def load_scores(file_path: str) -> Dict[str, Any]:
    """
    加载评分结果

    Args:
        file_path: 评分结果文件路径

    Returns:
        评分结果字典
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def find_bad_decisions(scores: Dict[str, Any], threshold: float = -0.3) -> Dict[str, List[Dict[str, Any]]]:
    """
    找出表现不佳的决策点

    Args:
        scores: 评分结果字典
        threshold: 评分阈值，低于该值的决策点被认为是表现不佳的

    Returns:
        表现不佳的决策点字典，按任务ID组织
    """
    bad_decisions = {}

    # 遍历每个任务
    for task_key, task_data in scores.items():
        # 跳过汇总信息和失败任务汇总
        if task_key in ["summary", "failed_tasks"]:
            continue

        # 跳过出错的任务
        if "error" in task_data:
            continue

        # 优先使用step_details（新格式），否则回退到旧格式
        if "step_details" in task_data:
            # 使用新的详细格式
            step_details = task_data["step_details"]
            task_bad_decisions = []

            for detail in step_details:
                if detail["total_score"] < threshold:
                    # 收集决策点信息（新格式）
                    decision_info = {
                        "step": detail["step"],
                        "action_id": detail["action_id"],
                        "action_name": detail["action_name"],
                        "endpoint_score": detail["endpoint_score"],
                        "trajectory_score": detail["trajectory_score"],
                        "efficiency_score": detail["efficiency_score"],
                        "total_score": detail["total_score"]
                    }
                    task_bad_decisions.append(decision_info)
        else:
            # 回退到旧格式兼容性
            step_scores = task_data.get("step_scores", [])
            task_bad_decisions = []

            for i, score in enumerate(step_scores):
                if score < threshold:
                    # 收集决策点信息（旧格式）
                    decision_info = {
                        "step": i,
                        "score": score,
                        "endpoint_score": task_data["endpoint_scores"][i] if i < len(task_data.get("endpoint_scores", [])) else None,
                        "trajectory_score": task_data["trajectory_scores"][i] if i < len(task_data.get("trajectory_scores", [])) else None
                    }
                    task_bad_decisions.append(decision_info)

        # 如果有表现不佳的决策点，添加到结果中
        if task_bad_decisions:
            bad_decisions[task_key] = task_bad_decisions

    return bad_decisions


def analyze_scores(scores: Dict[str, Any]) -> Dict[str, Any]:
    """
    分析评分结果

    Args:
        scores: 评分结果字典

    Returns:
        分析结果字典
    """
    # 初始化分析结果
    analysis = {
        "task_count": 0,
        "total_steps": 0,
        "avg_score": 0.0,
        "score_distribution": {
            "excellent": 0,  # 0.5 ~ 1.0
            "good": 0,       # 0.0 ~ 0.5
            "fair": 0,       # -0.3 ~ 0.0
            "poor": 0,       # -0.7 ~ -0.3
            "very_poor": 0   # -1.0 ~ -0.7
        },
        "worst_tasks": [],
        "best_tasks": []
    }

    # 收集所有任务的评分
    task_scores = []
    all_step_scores = []

    # 遍历每个任务
    for task_key, task_data in scores.items():
        # 跳过汇总信息和失败任务汇总
        if task_key in ["summary", "failed_tasks"]:
            continue

        # 跳过出错的任务
        if "error" in task_data:
            continue

        # 更新任务计数
        analysis["task_count"] += 1

        # 获取任务总分
        total_score = task_data.get("total_score", 0.0)
        task_scores.append((task_key, total_score))

        # 获取步骤评分
        step_scores = task_data.get("step_scores", [])
        all_step_scores.extend(step_scores)

        # 更新总步数
        analysis["total_steps"] += len(step_scores)

        # 更新评分分布
        for score in step_scores:
            if score >= 0.5:
                analysis["score_distribution"]["excellent"] += 1
            elif score >= 0.0:
                analysis["score_distribution"]["good"] += 1
            elif score >= -0.3:
                analysis["score_distribution"]["fair"] += 1
            elif score >= -0.7:
                analysis["score_distribution"]["poor"] += 1
            else:
                analysis["score_distribution"]["very_poor"] += 1

    # 计算平均分数
    if all_step_scores:
        analysis["avg_score"] = sum(all_step_scores) / len(all_step_scores)

    # 找出最差的5个任务
    task_scores.sort(key=lambda x: x[1])
    analysis["worst_tasks"] = [{"task_id": task_id, "score": score}
                              for task_id, score in task_scores[:5]]

    # 找出最好的5个任务
    task_scores.sort(key=lambda x: x[1], reverse=True)
    analysis["best_tasks"] = [{"task_id": task_id, "score": score}
                             for task_id, score in task_scores[:5]]

    return analysis


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="分析导航评分结果")
    parser.add_argument("--input_file", required=True, help="评分结果文件路径")
    parser.add_argument("--output_file", default="bad_decisions.json", help="输出文件路径")
    parser.add_argument("--threshold", type=float, default=-0.3, help="评分阈值，低于该值的决策点被认为是表现不佳的")
    parser.add_argument("--analysis_file", default="score_analysis.json", help="分析结果文件路径")

    # GRPO数据集构建选项
    parser.add_argument("--build_grpo_dataset", action="store_true", help="构建GRPO训练数据集")
    parser.add_argument("--grpo_output_file", default="grpo_dataset.json", help="GRPO数据集输出文件路径")
    parser.add_argument("--grpo_statistics_file", default="grpo_statistics.json", help="GRPO统计报告文件路径")
    parser.add_argument("--grpo_output_file_swift", help="Swift RLHF兼容格式的输出文件路径（JSONL格式）")
    parser.add_argument("--base_dir", default="data", help="数据目录")
    parser.add_argument("--max_positive_samples", type=int, default=200, help="最大正样本数量")
    parser.add_argument("--negative_threshold", type=float, default=0.0, help="GRPO负样本阈值")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")

    args = parser.parse_args()

    # 加载评分结果
    print(f"加载评分结果: {args.input_file}")
    scores = load_scores(args.input_file)

    # 找出表现不佳的决策点
    print(f"查找表现不佳的决策点 (阈值: {args.threshold})...")
    bad_decisions = find_bad_decisions(scores, args.threshold)

    # 保存表现不佳的决策点
    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(bad_decisions, f, ensure_ascii=False, indent=2)
    print(f"表现不佳的决策点已保存到: {args.output_file}")

    # 分析评分结果
    print("分析评分结果...")
    analysis = analyze_scores(scores)

    # 保存分析结果
    with open(args.analysis_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    print(f"分析结果已保存到: {args.analysis_file}")

    # 打印分析摘要
    print("\n分析摘要:")
    print(f"总任务数: {analysis['task_count']}")
    print(f"总步数: {analysis['total_steps']}")
    print(f"平均分数: {analysis['avg_score']:.4f}")
    print("\n评分分布:")
    for category, count in analysis['score_distribution'].items():
        percentage = count / analysis['total_steps'] * 100 if analysis['total_steps'] > 0 else 0
        print(f"  {category}: {count} ({percentage:.2f}%)")

    # 打印表现不佳的决策点统计
    total_bad_decisions = sum(len(decisions) for decisions in bad_decisions.values())
    print(f"\n表现不佳的决策点: {total_bad_decisions}")
    print(f"涉及任务数: {len(bad_decisions)}")

    # 如果有表现不佳的决策点，打印一些示例
    if bad_decisions:
        print(f"\n示例表现不佳的决策点（前5个）:")
        count = 0
        for task_key, decisions in bad_decisions.items():
            for decision in decisions:
                if count >= 5:
                    break
                if "action_id" in decision:  # 新格式
                    print(f"  任务{task_key} 步骤{decision['step']}: 动作{decision['action_id']}({decision['action_name']}) 总分{decision['total_score']:.3f}")
                else:  # 旧格式
                    print(f"  任务{task_key} 步骤{decision['step']}: 总分{decision['score']:.3f}")
                count += 1
            if count >= 5:
                break

    # 如果存在失败任务详情，显示统计
    if "failed_tasks" in scores:
        failed_tasks = scores["failed_tasks"]
        print(f"\n失败任务详情:")
        print(f"总失败任务数: {len(failed_tasks)}")

        # 按错误类型分组显示
        error_groups = {}
        for task_key, task_error in failed_tasks.items():
            error_type = task_error.get("error", "未知错误")
            if error_type not in error_groups:
                error_groups[error_type] = []
            error_groups[error_type].append(task_key)

        for error_type, task_list in error_groups.items():
            print(f"  {error_type}: {len(task_list)} 个任务")
            if len(task_list) <= 3:
                print(f"    任务: {', '.join(task_list)}")
            else:
                print(f"    任务: {', '.join(task_list[:3])} ... (共{len(task_list)}个)")

    # 打印原始数据中的错误统计（如果存在）
    elif "summary" in scores and "error_statistics" in scores["summary"]:
        error_stats = scores["summary"]["error_statistics"]
        if error_stats:
            print(f"\n原始评估错误统计:")
            for error_type, error_info in error_stats.items():
                print(f"  {error_type}: {error_info['count']} 个任务")
                if len(error_info['tasks']) <= 3:
                    print(f"    任务: {', '.join(error_info['tasks'])}")
                else:
                    print(f"    任务: {', '.join(error_info['tasks'][:3])} ... (共{len(error_info['tasks'])}个)")

    # 如果需要构建GRPO数据集
    if args.build_grpo_dataset:
        print("\n" + "=" * 60)
        print("构建GRPO训练数据集")
        print("=" * 60)

        try:
            # 导入GRPO数据集构建器
            from grpo_dataset_builder import build_grpo_dataset, generate_statistics_report, build_swift_rlhf_dataset
            import random
            import numpy as np

            # 设置随机种子
            random.seed(args.seed)
            np.random.seed(args.seed)

            print(f"GRPO数据集参数:")
            print(f"  负样本阈值: {args.negative_threshold}")
            print(f"  最大正样本数: {args.max_positive_samples}")
            print(f"  随机种子: {args.seed}")
            print(f"  数据目录: {args.base_dir}")
            if args.grpo_output_file_swift:
                print(f"  Swift RLHF输出文件: {args.grpo_output_file_swift}")

            # 构建GRPO数据集
            dataset = build_grpo_dataset(
                args.input_file,
                args.base_dir,
                args.max_positive_samples,
                args.negative_threshold
            )

            # 保存数据集
            print(f"\n保存GRPO数据集到: {args.grpo_output_file}")
            with open(args.grpo_output_file, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, ensure_ascii=False, indent=2)

            # 生成统计报告
            print("生成GRPO统计报告...")
            report = generate_statistics_report(dataset)

            # 保存统计报告
            print(f"保存GRPO统计报告到: {args.grpo_statistics_file}")
            with open(args.grpo_statistics_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            # 打印GRPO数据集摘要
            print("\n" + "=" * 60)
            print("GRPO数据集构建完成")
            print("=" * 60)
            summary = report["dataset_summary"]
            print(f"总样本数: {summary['total_samples']}")
            print(f"负样本数: {summary['negative_samples']} ({summary['negative_ratio']:.2%})")
            print(f"正样本数: {summary['positive_samples']} ({1-summary['negative_ratio']:.2%})")
            print(f"失败样本数: {summary['failed_samples']}")
            print(f"涉及任务数: {summary['task_count']}")

            print(f"\n贡献最多的任务 (前5个):")
            for task in report["task_contributions"][:5]:
                print(f"  任务{task['task_id']}: {task['total_samples']}个样本 (负样本{task['negative_samples']}, 正样本{task['positive_samples']})")

            # 如果指定了Swift RLHF输出文件，生成Swift RLHF兼容格式
            if args.grpo_output_file_swift:
                print("\n" + "=" * 60)
                print("生成Swift RLHF兼容格式数据集")
                print("=" * 60)

                try:
                    swift_samples = build_swift_rlhf_dataset(dataset, args.base_dir)

                    # 保存为JSONL格式
                    print(f"保存Swift RLHF数据集到: {args.grpo_output_file_swift}")
                    with open(args.grpo_output_file_swift, 'w', encoding='utf-8') as f:
                        for sample in swift_samples:
                            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

                    print(f"Swift RLHF数据集已保存，包含 {len(swift_samples)} 个样本")

                except Exception as e:
                    print(f"生成Swift RLHF数据集时发生错误: {e}")
                    import traceback
                    traceback.print_exc()

            print("=" * 60)

        except ImportError as e:
            print(f"导入GRPO数据集构建器失败: {e}")
            print("请确保grpo_dataset_builder.py文件在同一目录下")
        except Exception as e:
            print(f"构建GRPO数据集时发生错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
