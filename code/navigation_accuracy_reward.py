"""
导航准确率判断函数

该模块提供了一个用于评估模型在导航任务中回答准确性的奖励函数。
它能够从模型回答中提取导航动作，并与正确答案进行比较，计算准确率奖励。
支持软性奖励机制，根据预测动作与正确答案的距离给予不同的奖励分数。
还支持动作合并的显示，例如"After executing 3 times action \"Move down\", the position is:"。

使用方法:
1. 在Swift框架中作为奖励函数使用:
   ```python
   from navigation_accuracy_reward import NavigationAccuracy

   # 创建实例
   nav_accuracy = NavigationAccuracy()

   # 计算奖励
   rewards = nav_accuracy(model_outputs, correct_answers)
   ```

2. 在训练脚本中指定为外部插件:
   ```bash
   swift rlhf \
   --rlhf_type grpo \
   --external_plugins /path/to/navigation_accuracy_reward.py \
   # 其他参数...
   ```

软性奖励机制:
- 直接答对给 1 分
- 预测往后 1 步的动作给 0.5 分
- 预测往后 2 步的动作给 0.25 分
- 预测往后 3 步的动作给 0.125 分
- 预测往后 4 步的动作给 0.0625 分
- 只往后看 4 步

作者: AI助手
日期: 2025-05-03
"""

import os
import re
import json
from typing import Dict, List, Union, Optional, Tuple

from swift.plugin.orm import ORM

# 注册奖励函数的全局字典
orms = {}


class NavigationAccuracy(ORM):
    """
    导航准确性奖励函数，用于评估模型的导航动作回答是否与正确答案一致。

    从模型回答中提取导航动作（Move forward, Move backward等），并与正确答案进行比较。
    支持软性奖励机制，根据预测动作与正确答案的距离给予不同的奖励分数。

    特点:
    1. 支持从<answer></answer>标签中提取动作
    2. 支持从整个内容中提取动作，使用多种策略
    3. 支持多种动作表达方式，包括英文和中文
    4. 智能匹配算法，优先选择最相关的动作
    5. 软性奖励机制，根据预测动作与正确答案的距离给予不同的奖励分数
    6. 支持动作合并的显示，例如"After executing 3 times action \"Move down\", the position is:"

    支持的动作:
    - Move forward
    - Move backward
    - Move left
    - Move right
    - Move up
    - Move down
    - Rotate left
    - Rotate right
    - Look up
    - Look down

    软性奖励机制:
    - 直接答对给 1 分
    - 预测往后 1 步的动作给 0.5 分
    - 预测往后 2 步的动作给 0.25 分
    - 预测往后 3 步的动作给 0.125 分
    - 预测往后 4 步的动作给 0.0625 分
    - 只往后看 4 步
    """

    # 定义导航动作列表
    NAVIGATION_ACTIONS = [
        "Move forward",
        "Move backward",
        "Move left",
        "Move right",
        "Move up",
        "Move down",
        "Rotate left",
        "Rotate right",
        "Look up",
        "Look down"
    ]

    # 定义动作的简写或变体映射
    ACTION_VARIANTS = {
        # 英文完整匹配（优先级最高）
        "move forward": "Move forward",
        "move backward": "Move backward",
        "move left": "Move left",
        "move right": "Move right",
        "move up": "Move up",
        "move down": "Move down",
        "rotate left": "Rotate left",
        "rotate right": "Rotate right",
        "look up": "Look up",
        "look down": "Look down",

        # 英文简写匹配（优先级次之）
        "forward": "Move forward",
        "backward": "Move backward",
        "left": "Move left",
        "right": "Move right",
        "up": "Move up",
        "down": "Move down",

        # 其他英文变体
        "tilt up": "Look up",
        "tilt down": "Look down",
        "go forward": "Move forward",
        "go backward": "Move backward",
        "go left": "Move left",
        "go right": "Move right",
        "go up": "Move up",
        "go down": "Move down",
        "turn left": "Rotate left",
        "turn right": "Rotate right",

        # 中文匹配
        "向前": "Move forward",
        "向后": "Move backward",
        "向左": "Move left",
        "向右": "Move right",
        "向上": "Move up",
        "向下": "Move down",
        "左转": "Rotate left",
        "右转": "Rotate right",
        "向上看": "Look up",
        "向下看": "Look down",
        "向前运动": "Move forward",
        "向后运动": "Move backward",
        "向左移动": "Move left",
        "向右移动": "Move right",
        "向上运动": "Move up",
        "向下运动": "Move down",
        "向左旋转": "Rotate left",
        "向右旋转": "Rotate right"
    }

    # 定义用于从回答中提取动作的正则表达式模式
    ANSWER_TAG_PATTERN = r"<answer>(.*?)</answer>"

    # 定义用于从文本中提取动作的正则表达式模式
    ACTION_PATTERNS = [
        r"I choose\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"My choice is\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"I select\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"The best action is\s+(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"I would\s+(?:choose\s+)?(?:to\s+)?(?:the\s+)?(?:action\s+)?[\"']?(Move forward|Move backward|Move left|Move right|Move up|Move down|Rotate left|Rotate right|Look up|Look down)[\"']?",
        r"我选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"最佳动作是[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"我会选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"应该[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?",
        r"选择[\"']?(向前运动|向后运动|向左移动|向右移动|向上运动|向下运动|向左旋转|向右旋转|向上看|向下看)[\"']?"
    ]

    # 定义用于从文本中提取最后一行或最后一句话的正则表达式模式
    LAST_LINE_PATTERN = r"(?:^|\n)([^\n]+)$"
    LAST_SENTENCE_PATTERN = r"[.!?。！？]\s*([^.!?。！？]+)[.!?。！？]?$"

    # 定义软性奖励的衰减系数
    SOFT_REWARD_DECAY = {
        0: 1.0,      # 直接答对给 1 分
        1: 0.5,      # 预测往后 1 步的动作给 0.5 分
        2: 0.25,     # 预测往后 2 步的动作给 0.25 分
        3: 0.125,    # 预测往后 3 步的动作给 0.125 分
        4: 0.0625    # 预测往后 4 步的动作给 0.0625 分
    }

    # 最大查找步数
    MAX_FUTURE_STEPS = 4

    def __init__(self):
        """
        初始化导航准确性奖励函数
        """
        # 存储动作序列的字典，键为场景ID，值为动作列表
        self.action_sequences = {}

        # 尝试从数据集文件中加载动作序列
        self.dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../data/json/navigation_dataset.jsonl")
        self.load_action_sequences_from_dataset()

    def load_action_sequences_from_dataset(self):
        """
        从数据集文件中加载动作序列
        """
        try:
            if os.path.exists(self.dataset_path):
                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            sample = json.loads(line.strip())
                            if 'folder_id' in sample and 'action_sequence' in sample:
                                folder_id = str(sample['folder_id'])

                                # 确保action_sequence是列表
                                if isinstance(sample['action_sequence'], list):
                                    action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                elif isinstance(sample['action_sequence'], str):
                                    # 尝试解析字符串为列表
                                    try:
                                        action_list = json.loads(sample['action_sequence'])
                                        if isinstance(action_list, list):
                                            action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                                        else:
                                            action_sequence = [self.normalize_action(sample['action_sequence'])]
                                    except:
                                        action_sequence = [self.normalize_action(sample['action_sequence'])]
                                else:
                                    action_sequence = [self.normalize_action(sample['action_sequence'])]

                                if action_sequence:
                                    self.action_sequences[folder_id] = action_sequence
                                    print(f"加载场景 {folder_id} 的动作序列: {action_sequence}")
                        except Exception as e:
                            print(f"解析数据集样本时出错: {e}")
                print(f"从数据集中加载了 {len(self.action_sequences)} 个场景的动作序列")
            else:
                print(f"数据集文件不存在: {self.dataset_path}")
        except Exception as e:
            print(f"加载数据集时出错: {e}")

    def extract_action_from_answer_tag(self, content: str) -> str:
        """
        从<answer></answer>标签中提取动作

        Args:
            content: 包含答案的文本

        Returns:
            提取的动作文本
        """
        # 查找<answer>标签中的内容
        answer_matches = re.findall(self.ANSWER_TAG_PATTERN, content, re.DOTALL | re.IGNORECASE)
        if answer_matches:
            # 返回第一个匹配的答案内容，去除首尾空白
            answer_content = answer_matches[0].strip()

            # 检查<answer>标签中是否直接包含标准动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in answer_content.lower():
                    # 如果包含标准动作，直接返回该动作
                    return action

            # 如果没有找到标准动作，返回原始内容
            return answer_content
        return ""

    def extract_action_from_content(self, content: str) -> Tuple[str, str]:
        """
        从整个内容中提取动作，使用多种策略

        Args:
            content: 包含答案的文本

        Returns:
            提取的动作文本和提取方法描述
        """
        # 策略1: 使用预定义的模式直接匹配
        for pattern in self.ACTION_PATTERNS:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[-1], "预定义模式匹配"

        # 策略2: 检查最后一行是否包含动作
        last_line_match = re.search(self.LAST_LINE_PATTERN, content)
        if last_line_match:
            last_line = last_line_match.group(1).strip()
            # 检查最后一行是否只包含一个动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in last_line.lower() and len(last_line) < len(action) + 10:
                    return last_line, "最后一行匹配"

        # 策略3: 检查最后一句话是否包含动作
        last_sentence_match = re.search(self.LAST_SENTENCE_PATTERN, content)
        if last_sentence_match:
            last_sentence = last_sentence_match.group(1).strip()
            # 检查最后一句话是否只包含一个动作
            for action in self.NAVIGATION_ACTIONS:
                if action.lower() in last_sentence.lower():
                    return last_sentence, "最后一句话匹配"

        # 策略4: 直接在整个内容中查找标准动作
        for action in self.NAVIGATION_ACTIONS:
            if action.lower() in content.lower():
                # 找到动作周围的上下文
                action_index = content.lower().find(action.lower())
                start = max(0, action_index - 20)
                end = min(len(content), action_index + len(action) + 20)
                context = content[start:end]
                return context, "整个内容匹配"

        # 策略5: 在整个内容中查找动作变体
        for variant, standard_action in self.ACTION_VARIANTS.items():
            if variant.lower() in content.lower():
                # 找到变体周围的上下文
                variant_index = content.lower().find(variant.lower())
                start = max(0, variant_index - 20)
                end = min(len(content), variant_index + len(variant) + 20)
                context = content[start:end]
                return context, "变体匹配"

        # 如果所有策略都失败，返回整个内容
        return content, "无匹配，使用整个内容"

    def normalize_action(self, action) -> str:
        """
        标准化动作格式，匹配到预定义的导航动作

        Args:
            action: 包含动作的文本，可以是字符串或其他类型

        Returns:
            标准化后的动作文本
        """
        # 处理非字符串类型
        if not action:
            return ""

        # 如果是列表，递归处理第一个元素
        if isinstance(action, list):
            if len(action) > 0:
                return self.normalize_action(action[0])
            else:
                return ""

        # 如果不是字符串，尝试转换为字符串
        if not isinstance(action, str):
            try:
                action = str(action)
            except:
                return ""

        # 去除首尾空白并转为小写以便比较
        action_lower = action.strip().lower()

        # 1. 直接检查完整匹配（最高优先级）
        for nav_action in self.NAVIGATION_ACTIONS:
            if nav_action.lower() == action_lower:
                return nav_action

        # 2. 检查是否包含完整的动作短语（次高优先级）
        # 例如："I should look up" 应该匹配 "Look up" 而不是 "Move up"
        exact_matches = []
        for nav_action in self.NAVIGATION_ACTIONS:
            if nav_action.lower() in action_lower:
                # 记录匹配的动作和它在文本中的位置
                exact_matches.append((nav_action, action_lower.find(nav_action.lower())))

        # 如果找到完整匹配，返回最后出现的那个（通常是结论）
        if exact_matches:
            # 按照出现位置排序，返回最后出现的
            exact_matches.sort(key=lambda x: x[1])
            return exact_matches[-1][0]

        # 3. 检查动作变体映射（第三优先级）
        # 先检查较长的变体，避免错误匹配
        # 例如："look up" 应该匹配 "Look up" 而不是 "Move up"
        variant_matches = []
        for variant, standard_action in sorted(self.ACTION_VARIANTS.items(), key=lambda x: -len(x[0])):
            if variant.lower() in action_lower:
                # 记录匹配的变体、对应的标准动作和它在文本中的位置
                variant_matches.append((variant, standard_action, action_lower.find(variant.lower())))

        # 如果找到变体匹配，返回最长的那个变体对应的标准动作
        if variant_matches:
            # 按照变体长度和出现位置排序，优先选择较长的变体和较后出现的
            variant_matches.sort(key=lambda x: (len(x[0]), x[2]))
            return variant_matches[-1][1]

        # 如果没有找到匹配项，返回原始文本
        return action

    def extract_scene_id(self, content: str, message_dict: Dict = None) -> str:
        """
        从内容中提取场景ID，用于关联动作序列

        Args:
            content: 包含场景信息的文本
            message_dict: 消息字典，可能包含folder_id字段

        Returns:
            场景ID，如果没有找到则返回空字符串
        """
        # 首先检查消息字典中是否直接包含folder_id字段
        if message_dict and isinstance(message_dict, dict):
            # 直接检查顶层的folder_id字段
            if "folder_id" in message_dict:
                return str(message_dict["folder_id"])

        # 尝试从内容中提取场景ID，这里假设场景ID在某个特定格式中
        # 例如: "Scene ID: scene_123" 或 "Navigation task in environment: env_456"
        scene_id_patterns = [
            r"Scene ID[:\s]+([a-zA-Z0-9_-]+)",
            r"Environment[:\s]+([a-zA-Z0-9_-]+)",
            r"Task ID[:\s]+([a-zA-Z0-9_-]+)",
            r"Navigation in[:\s]+([a-zA-Z0-9_-]+)",
            r"folder_id[\"']?\s*:\s*[\"']([a-zA-Z0-9_-]+)[\"']"
        ]

        for pattern in scene_id_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)

        # 如果没有找到场景ID，使用内容的哈希值作为ID
        import hashlib
        return hashlib.md5(content[:200].encode()).hexdigest()

    def extract_action_sequence(self, content: str, message_dict: Dict = None) -> List[str]:
        """
        从内容中提取动作序列

        Args:
            content: 包含动作序列的文本
            message_dict: 消息字典，可能包含action_sequence字段

        Returns:
            标准化后的动作序列列表
        """
        # 首先检查消息字典中是否直接包含action_sequence字段
        if message_dict and isinstance(message_dict, dict):
            # 直接检查顶层的action_sequence字段
            if "action_sequence" in message_dict:
                action_sequence = message_dict["action_sequence"]
                if isinstance(action_sequence, list):
                    # 标准化每个动作
                    return [self.normalize_action(action) for action in action_sequence if self.normalize_action(action)]
                elif isinstance(action_sequence, str):
                    # 尝试解析字符串为列表
                    try:
                        action_list = json.loads(action_sequence)
                        if isinstance(action_list, list):
                            return [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                    except:
                        # 尝试使用正则表达式提取
                        actions = re.findall(r'["\']([^"\']+)["\']', action_sequence)
                        if actions:
                            return [self.normalize_action(action) for action in actions if self.normalize_action(action)]

            # 检查sample字段中的action_sequence
            if "sample" in message_dict and isinstance(message_dict["sample"], dict) and "action_sequence" in message_dict["sample"]:
                action_sequence = message_dict["sample"]["action_sequence"]
                if isinstance(action_sequence, list):
                    return [self.normalize_action(action) for action in action_sequence if self.normalize_action(action)]

            # 检查messages字段中的action_sequence
            if "messages" in message_dict and isinstance(message_dict["messages"], list):
                for msg in message_dict["messages"]:
                    if isinstance(msg, dict) and "action_sequence" in msg:
                        action_sequence = msg["action_sequence"]
                        if isinstance(action_sequence, list):
                            # 标准化每个动作
                            return [self.normalize_action(action) for action in action_sequence if self.normalize_action(action)]

            # 如果有folder_id和solution，尝试从数据集中查找
            if "folder_id" in message_dict and "solution" in message_dict:
                folder_id = str(message_dict["folder_id"])
                solution = self.normalize_action(message_dict["solution"])

                # 检查是否已经加载了该场景的动作序列
                if folder_id in self.action_sequences:
                    return self.action_sequences[folder_id]

                # 尝试从数据集文件中加载
                if os.path.exists(self.dataset_path):
                    try:
                        with open(self.dataset_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                try:
                                    sample = json.loads(line.strip())
                                    if 'folder_id' in sample and str(sample['folder_id']) == folder_id and 'action_sequence' in sample:
                                        action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                        if action_sequence:
                                            self.action_sequences[folder_id] = action_sequence
                                            return action_sequence
                                except:
                                    continue
                    except:
                        pass

        # 如果消息字典中没有action_sequence字段，尝试从内容中提取
        # 尝试解析JSON格式的内容
        try:
            # 尝试将内容解析为JSON
            json_data = json.loads(content)
            if "action_sequence" in json_data and isinstance(json_data["action_sequence"], list):
                return [self.normalize_action(action) for action in json_data["action_sequence"] if self.normalize_action(action)]
        except:
            pass

        # 尝试使用正则表达式提取
        action_sequence_pattern = r"action_sequence[\"']?\s*:\s*\[(.*?)\]"
        match = re.search(action_sequence_pattern, content, re.DOTALL | re.IGNORECASE)

        if match:
            # 提取动作序列字符串并分割成列表
            action_sequence_str = match.group(1)
            # 移除引号和空格，分割成列表
            actions = re.findall(r'["\']([^"\']+)["\']', action_sequence_str)
            # 标准化每个动作
            return [self.normalize_action(action) for action in actions if self.normalize_action(action)]

        return []

    def calculate_soft_reward(self, predicted_action: str, correct_action: str, action_sequence: List[str], current_index: int) -> Tuple[float, int]:
        """
        计算软性奖励，根据预测动作与正确答案的距离给予不同的奖励分数

        Args:
            predicted_action: 模型预测的动作
            correct_action: 正确的动作
            action_sequence: 完整的动作序列
            current_index: 当前动作在序列中的索引

        Returns:
            (奖励分数, 预测动作在未来的步数)
        """
        # 如果预测动作与正确动作一致，直接返回1.0
        if predicted_action == correct_action:
            return 1.0, 0

        # 如果动作序列为空或当前索引无效，返回0.0
        if not action_sequence or current_index < 0 or current_index >= len(action_sequence):
            return 0.0, -1

        # 检查预测动作是否在未来的动作序列中
        future_steps = -1
        for i in range(1, min(self.MAX_FUTURE_STEPS + 1, len(action_sequence) - current_index)):
            future_index = current_index + i
            if future_index < len(action_sequence) and predicted_action == action_sequence[future_index]:
                future_steps = i
                break

        # 如果预测动作在未来的动作序列中，根据距离给予奖励
        if future_steps > 0:
            return self.SOFT_REWARD_DECAY.get(future_steps, 0.0), future_steps

        return 0.0, -1

    def detect_action_merging(self, content: str) -> Tuple[str, int]:
        """
        检测内容中是否有动作合并的描述

        Args:
            content: 包含可能的动作合并描述的文本

        Returns:
            (合并的动作, 合并次数)，如果没有检测到则返回("", 0)
        """
        # 检测动作合并的模式
        # 例如: "After executing 3 times action \"Move down\", the position is:"
        merge_patterns = [
            r"(?:After|after) executing (\d+) times (?:action|the action) [\"']([^\"']+)[\"']",
            r"(?:执行|重复执行|连续执行)了 (\d+) 次[\"']([^\"']+)[\"']",
            r"(?:执行|重复执行|连续执行)(\d+)次[\"']([^\"']+)[\"']"
        ]

        for pattern in merge_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                count = int(match.group(1))
                action = match.group(2)
                return self.normalize_action(action), count

        return "", 0

    def __call__(self, completions, solution, **kwargs) -> List[float]:
        """
        评估模型回答与正确答案的一致性，支持软性奖励机制

        Args:
            completions: 模型生成的回答列表
            solution: 正确答案列表
            kwargs: 其他参数

        Returns:
            准确性奖励列表，根据软性奖励机制计算
        """
        rewards = []
        debug = kwargs.get('debug', True)  # 是否打印调试信息
        message = kwargs.get('message', {})  # 获取完整的消息内容，用于提取动作序列

        # 确保 completions 和 solution 都是列表
        if not isinstance(completions, list):
            completions = [completions]

        if not isinstance(solution, list):
            solution = [solution] * len(completions)

        # 如果debug模式，打印所有可用的字段，帮助调试
        if debug:
            print(f"可用字段: {list(kwargs.keys())}")

        # 提取场景ID和动作序列
        scene_id = ""
        action_sequence = []
        action_sequence_source = "未知来源"  # 记录action_sequence的来源
        current_index = -1
        history_actions = []

        # 1. 直接从kwargs中提取字段
        if 'folder_id' in kwargs:
            # 处理folder_id可能是列表的情况
            if isinstance(kwargs['folder_id'], list) and len(kwargs['folder_id']) > 0:
                scene_id = str(kwargs['folder_id'][0])
            else:
                scene_id = str(kwargs['folder_id'])
            if debug:
                print(f"从kwargs中提取到folder_id: {scene_id}")

        if 'current_index' in kwargs:
            # 处理current_index可能是列表的情况
            if isinstance(kwargs['current_index'], list) and len(kwargs['current_index']) > 0:
                current_index = int(kwargs['current_index'][0])
            else:
                current_index = int(kwargs['current_index'])
            if debug:
                print(f"从kwargs中提取到current_index: {current_index}")

        # 用于存储从数据集文件中加载的动作序列
        dataset_action_sequence = None

        if 'folder_id' in kwargs:
            folder_id = str(kwargs['folder_id']) if not isinstance(kwargs['folder_id'], list) else str(kwargs['folder_id'][0])
            # 优先从数据集文件中加载动作序列
            if os.path.exists(self.dataset_path):
                try:
                    with open(self.dataset_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                sample = json.loads(line.strip())
                                if 'folder_id' in sample and str(sample['folder_id']) == folder_id and 'action_sequence' in sample:
                                    if isinstance(sample['action_sequence'], list):
                                        dataset_action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                        if dataset_action_sequence:
                                            self.action_sequences[folder_id] = dataset_action_sequence
                                            action_sequence = dataset_action_sequence
                                            action_sequence_source = "数据集文件(kwargs)"
                                            if debug:
                                                print(f"从数据集文件中加载场景 {folder_id} 的动作序列: {action_sequence}")
                                            break
                            except Exception as e:
                                if debug:
                                    print(f"解析数据集样本时出错: {e}")
                except Exception as e:
                    if debug:
                        print(f"加载数据集时出错: {e}")

        # 如果从数据集文件中没有加载到动作序列，则尝试从kwargs中提取
        if 'action_sequence' in kwargs and not dataset_action_sequence:
            if isinstance(kwargs['action_sequence'], list):
                action_sequence = [self.normalize_action(action) for action in kwargs['action_sequence'] if self.normalize_action(action)]
                action_sequence_source = "kwargs列表"
                if debug:
                    print(f"从kwargs中提取到action_sequence: {action_sequence}")
            elif isinstance(kwargs['action_sequence'], str):
                # 尝试解析字符串为列表
                try:
                    action_list = json.loads(kwargs['action_sequence'])
                    if isinstance(action_list, list):
                        action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                        action_sequence_source = "kwargs字符串解析"
                        if debug:
                            print(f"从kwargs字符串中解析到action_sequence: {action_sequence}")
                except:
                    # 如果解析失败，尝试使用正则表达式提取
                    actions = re.findall(r'["\']([^"\']+)["\']', kwargs['action_sequence'])
                    if actions:
                        action_sequence = [self.normalize_action(action) for action in actions if self.normalize_action(action)]
                        action_sequence_source = "kwargs字符串正则提取"
                        if debug:
                            print(f"从kwargs字符串中正则提取到action_sequence: {action_sequence}")
        # 如果从kwargs中提取的动作序列与数据集中的不一致，使用数据集中的
        elif 'action_sequence' in kwargs and dataset_action_sequence:
            kwargs_action_sequence = None
            if isinstance(kwargs['action_sequence'], list):
                kwargs_action_sequence = [self.normalize_action(action) for action in kwargs['action_sequence'] if self.normalize_action(action)]
                if debug:
                    print(f"从kwargs中提取到action_sequence: {kwargs_action_sequence}")
            elif isinstance(kwargs['action_sequence'], str):
                # 尝试解析字符串为列表
                try:
                    action_list = json.loads(kwargs['action_sequence'])
                    if isinstance(action_list, list):
                        kwargs_action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                        if debug:
                            print(f"从kwargs字符串中解析到action_sequence: {kwargs_action_sequence}")
                except:
                    # 如果解析失败，尝试使用正则表达式提取
                    actions = re.findall(r'["\']([^"\']+)["\']', kwargs['action_sequence'])
                    if actions:
                        kwargs_action_sequence = [self.normalize_action(action) for action in actions if self.normalize_action(action)]
                        if debug:
                            print(f"从kwargs字符串中正则提取到action_sequence: {kwargs_action_sequence}")

            # 比较从kwargs中提取的动作序列与数据集中的是否一致
            if kwargs_action_sequence and kwargs_action_sequence != dataset_action_sequence:
                if debug:
                    print(f"警告：从kwargs中提取的action_sequence与数据集中的不一致")
                    print(f"  kwargs中的action_sequence: {kwargs_action_sequence}")
                    print(f"  数据集中的action_sequence: {dataset_action_sequence}")
                # 使用数据集中的动作序列
                action_sequence = dataset_action_sequence
                action_sequence_source = "数据集文件(不一致时优先)"
                if debug:
                    print(f"使用数据集中的动作序列: {action_sequence}")

        if 'history_actions' in kwargs and isinstance(kwargs['history_actions'], list):
            # 处理history_actions可能是嵌套列表的情况
            if len(kwargs['history_actions']) > 0 and isinstance(kwargs['history_actions'][0], list):
                # 展平嵌套列表
                flat_history_actions = []
                for action_list in kwargs['history_actions']:
                    if isinstance(action_list, list) and len(action_list) > 0:
                        flat_history_actions.append(action_list[0])
                    else:
                        flat_history_actions.append(action_list)
                history_actions = [self.normalize_action(action) for action in flat_history_actions if self.normalize_action(action)]
            else:
                history_actions = [self.normalize_action(action) for action in kwargs['history_actions'] if self.normalize_action(action)]
            if debug:
                print(f"从kwargs中提取到history_actions: {history_actions}")

        # 2. 从数据集样本中提取字段
        sample = None
        if 'sample' in kwargs:
            sample = kwargs['sample']
            if isinstance(sample, dict):
                # 提取场景ID
                if not scene_id and 'folder_id' in sample:
                    scene_id = str(sample['folder_id'])
                    if debug:
                        print(f"从sample中提取到folder_id: {scene_id}")

                # 提取当前索引
                if current_index < 0 and 'current_index' in sample:
                    # 处理current_index可能是列表的情况
                    if isinstance(sample['current_index'], list) and len(sample['current_index']) > 0:
                        current_index = int(sample['current_index'][0])
                    else:
                        current_index = int(sample['current_index'])
                    if debug:
                        print(f"从sample中提取到current_index: {current_index}")

                # 提取动作序列
                if not action_sequence and 'action_sequence' in sample:
                    # 优先从数据集文件中加载动作序列
                    dataset_action_sequence = None
                    if 'folder_id' in sample:
                        folder_id = str(sample['folder_id'])
                        # 尝试从数据集文件中加载动作序列
                        if os.path.exists(self.dataset_path):
                            try:
                                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                                    for line in f:
                                        try:
                                            dataset_sample = json.loads(line.strip())
                                            if 'folder_id' in dataset_sample and str(dataset_sample['folder_id']) == folder_id and 'action_sequence' in dataset_sample:
                                                if isinstance(dataset_sample['action_sequence'], list):
                                                    dataset_action_sequence = [self.normalize_action(action) for action in dataset_sample['action_sequence'] if self.normalize_action(action)]
                                                    if dataset_action_sequence:
                                                        self.action_sequences[folder_id] = dataset_action_sequence
                                                        action_sequence = dataset_action_sequence
                                                        action_sequence_source = "数据集文件(sample)"
                                                        if debug:
                                                            print(f"从数据集文件中加载场景 {folder_id} 的动作序列: {action_sequence}")
                                                        break
                                        except Exception as e:
                                            if debug:
                                                print(f"解析数据集样本时出错: {e}")
                            except Exception as e:
                                if debug:
                                    print(f"加载数据集时出错: {e}")

                    # 如果没有从数据集中获取到动作序列，则尝试从sample中提取
                    if not dataset_action_sequence:
                        sample_action_sequence = None
                        if isinstance(sample['action_sequence'], list):
                            sample_action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                            if debug:
                                print(f"从sample中提取到action_sequence: {sample_action_sequence}")
                        elif isinstance(sample['action_sequence'], str):
                            # 尝试解析字符串为列表
                            try:
                                action_list = json.loads(sample['action_sequence'])
                                if isinstance(action_list, list):
                                    sample_action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                                    if debug:
                                        print(f"从sample字符串中解析到action_sequence: {sample_action_sequence}")
                            except:
                                # 尝试使用正则表达式提取
                                actions = re.findall(r'["\']([^"\']+)["\']', sample['action_sequence'])
                                if actions:
                                    sample_action_sequence = [self.normalize_action(action) for action in actions if self.normalize_action(action)]
                                    if debug:
                                        print(f"从sample字符串中正则提取到action_sequence: {sample_action_sequence}")

                        # 如果从sample中提取到了动作序列，使用它
                        if sample_action_sequence:
                            action_sequence = sample_action_sequence
                            action_sequence_source = "sample提取"
                            if debug:
                                print(f"使用从sample中提取的action_sequence: {action_sequence}")

                # 提取历史动作
                if not history_actions and 'history_actions' in sample and isinstance(sample['history_actions'], list):
                    history_actions = [self.normalize_action(action) for action in sample['history_actions'] if self.normalize_action(action)]
                    if debug:
                        print(f"从sample中提取到history_actions: {history_actions}")

        # 3. 从message字段中提取
        if isinstance(message, dict):
            # 从消息字典中提取场景ID
            if not scene_id:
                scene_id = self.extract_scene_id("", message)
                if debug and scene_id:
                    print(f"从message字典中提取到folder_id: {scene_id}")

            # 从消息字典中提取动作序列
            if not action_sequence:
                # 优先从数据集文件中加载动作序列
                dataset_action_sequence = None

                # 获取场景ID
                if not scene_id and 'folder_id' in message:
                    scene_id = str(message['folder_id']) if not isinstance(message['folder_id'], list) else str(message['folder_id'][0])

                if scene_id:
                    # 尝试从数据集文件中加载动作序列
                    if os.path.exists(self.dataset_path):
                        try:
                            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                                for line in f:
                                    try:
                                        sample = json.loads(line.strip())
                                        if 'folder_id' in sample and str(sample['folder_id']) == scene_id and 'action_sequence' in sample:
                                            if isinstance(sample['action_sequence'], list):
                                                dataset_action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                                if dataset_action_sequence:
                                                    self.action_sequences[scene_id] = dataset_action_sequence
                                                    action_sequence = dataset_action_sequence
                                                    action_sequence_source = "数据集文件(message)"
                                                    if debug:
                                                        print(f"从数据集文件中加载场景 {scene_id} 的动作序列: {action_sequence}")
                                                    break
                                    except Exception as e:
                                        if debug:
                                            print(f"解析数据集样本时出错: {e}")
                        except Exception as e:
                            if debug:
                                print(f"加载数据集时出错: {e}")

                # 如果没有从数据集中获取到动作序列，则尝试从message中提取
                if not dataset_action_sequence:
                    message_action_sequence = self.extract_action_sequence("", message)
                    if message_action_sequence:
                        action_sequence = message_action_sequence
                        action_sequence_source = "message字典"
                        if debug:
                            print(f"从message字典中提取到action_sequence: {action_sequence}")

            # 从消息字典中提取当前索引
            if current_index < 0 and "current_index" in message:
                # 处理current_index可能是列表的情况
                if isinstance(message["current_index"], list) and len(message["current_index"]) > 0:
                    current_index = int(message["current_index"][0])
                else:
                    current_index = int(message["current_index"])
                if debug:
                    print(f"从message字典中提取到current_index: {current_index}")

            # 从消息字典中提取历史动作
            if not history_actions and "history_actions" in message and isinstance(message["history_actions"], list):
                history_actions = [self.normalize_action(action) for action in message["history_actions"] if self.normalize_action(action)]
                if debug and history_actions:
                    print(f"从message字典中提取到history_actions: {history_actions}")

            # 如果消息字典中包含messages字段，尝试从中提取
            if "messages" in message and isinstance(message["messages"], list):
                # 如果还没有提取到动作序列，尝试从messages中提取
                if not action_sequence:
                    for msg in message["messages"]:
                        if isinstance(msg, dict) and "role" in msg and msg["role"] == "user" and "action_sequence" in msg:
                            action_sequence = msg["action_sequence"]
                            if isinstance(action_sequence, list):
                                action_sequence = [self.normalize_action(action) for action in action_sequence if self.normalize_action(action)]
                                if debug:
                                    print(f"从message.messages中提取到action_sequence: {action_sequence}")
                                break

                # 如果还没有提取到场景ID，尝试从messages中提取
                if not scene_id:
                    for msg in message["messages"]:
                        if isinstance(msg, dict) and "content" in msg:
                            scene_id_from_content = self.extract_scene_id(msg["content"])
                            if scene_id_from_content:
                                scene_id = scene_id_from_content
                                if debug:
                                    print(f"从message.messages内容中提取到folder_id: {scene_id}")
                                break
        elif isinstance(message, str):
            # 从消息字符串中提取场景ID
            if not scene_id:
                scene_id = self.extract_scene_id(message)
                if debug and scene_id:
                    print(f"从message字符串中提取到folder_id: {scene_id}")

            # 从消息字符串中提取动作序列
            if not action_sequence:
                # 优先从数据集文件中加载动作序列
                dataset_action_sequence = None

                # 如果有场景ID，尝试从数据集文件中加载
                if scene_id and os.path.exists(self.dataset_path):
                    try:
                        with open(self.dataset_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                try:
                                    sample = json.loads(line.strip())
                                    if 'folder_id' in sample and str(sample['folder_id']) == scene_id and 'action_sequence' in sample:
                                        if isinstance(sample['action_sequence'], list):
                                            dataset_action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                            if dataset_action_sequence:
                                                self.action_sequences[scene_id] = dataset_action_sequence
                                                action_sequence = dataset_action_sequence
                                                action_sequence_source = "数据集文件(message字符串)"
                                                if debug:
                                                    print(f"从数据集文件中加载场景 {scene_id} 的动作序列: {action_sequence}")
                                                break
                                except Exception as e:
                                    if debug:
                                        print(f"解析数据集样本时出错: {e}")
                    except Exception as e:
                        if debug:
                            print(f"加载数据集时出错: {e}")

                # 如果没有从数据集中获取到动作序列，则尝试从message字符串中提取
                if not dataset_action_sequence:
                    message_action_sequence = self.extract_action_sequence(message)
                    if message_action_sequence:
                        action_sequence = message_action_sequence
                        action_sequence_source = "message字符串"
                        if debug:
                            print(f"从message字符串中提取到action_sequence: {action_sequence}")

        # 4. 如果仍然没有找到场景ID，尝试从solution中提取
        if not scene_id and isinstance(solution, list) and len(solution) > 0:
            solution_str = str(solution[0])
            folder_id_match = re.search(r'folder_id["\']?\s*:\s*["\']?([a-zA-Z0-9_-]+)["\']?', solution_str)
            if folder_id_match:
                scene_id = folder_id_match.group(1)
                if debug:
                    print(f"从solution中提取到folder_id: {scene_id}")

        # 5. 如果仍然没有找到场景ID，尝试从completions中提取
        if not scene_id and isinstance(completions, list) and len(completions) > 0:
            completion_str = str(completions[0])
            folder_id_match = re.search(r'folder_id["\']?\s*:\s*["\']?([a-zA-Z0-9_-]+)["\']?', completion_str)
            if folder_id_match:
                scene_id = folder_id_match.group(1)
                if debug:
                    print(f"从completions中提取到folder_id: {scene_id}")

        # 6. 如果仍然没有找到场景ID，使用默认值
        if not scene_id:
            scene_id = "unknown_scene"
            if debug:
                print(f"未找到场景ID，使用默认值: {scene_id}")

        # 7. 如果仍然没有找到动作序列，尝试从数据集文件中加载，如果失败则从kwargs中直接获取
        if not action_sequence:
            # 优先从数据集文件中加载
            dataset_action_sequence = None
            if scene_id and os.path.exists(self.dataset_path):
                try:
                    with open(self.dataset_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            try:
                                sample = json.loads(line.strip())
                                if 'folder_id' in sample and str(sample['folder_id']) == scene_id and 'action_sequence' in sample:
                                    if isinstance(sample['action_sequence'], list):
                                        dataset_action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                        if dataset_action_sequence:
                                            self.action_sequences[scene_id] = dataset_action_sequence
                                            action_sequence = dataset_action_sequence
                                            action_sequence_source = "数据集文件(最后尝试)"
                                            if debug:
                                                print(f"从数据集文件中加载场景 {scene_id} 的动作序列: {action_sequence}")
                                            break
                            except Exception as e:
                                if debug:
                                    print(f"解析数据集样本时出错: {e}")
                except Exception as e:
                    if debug:
                        print(f"加载数据集时出错: {e}")

            # 如果从数据集中没有获取到动作序列，则尝试从kwargs中直接获取
            if not dataset_action_sequence and 'action_sequence' in kwargs:
                if isinstance(kwargs['action_sequence'], list):
                    action_sequence = [self.normalize_action(action) for action in kwargs['action_sequence'] if self.normalize_action(action)]
                    action_sequence_source = "kwargs直接列表"
                    if debug:
                        print(f"从kwargs中直接提取到action_sequence: {action_sequence}")
                elif isinstance(kwargs['action_sequence'], str):
                    # 尝试解析字符串为列表
                    try:
                        action_list = json.loads(kwargs['action_sequence'])
                        if isinstance(action_list, list):
                            action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                            action_sequence_source = "kwargs直接字符串解析"
                            if debug:
                                print(f"从kwargs字符串中解析到action_sequence: {action_sequence}")
                    except:
                        pass

            # 如果仍然没有找到，使用默认值
            if not action_sequence:
                action_sequence = []
                if debug:
                    print(f"未找到动作序列，使用空列表")

        # 8. 如果有动作序列但没有当前索引，使用默认值
        if action_sequence and current_index < 0:
            current_index = 0
            if debug:
                print(f"未找到当前索引，使用默认值: {current_index}")

        # 9. 如果消息中包含动作序列，则存储到字典中
        if action_sequence:
            self.action_sequences[scene_id] = action_sequence

        # 10. 如果没有从当前消息中提取到动作序列，尝试从之前存储的序列中获取
        if not action_sequence and scene_id in self.action_sequences:
            action_sequence = self.action_sequences[scene_id]
            action_sequence_source = "预加载数据集"
            if debug:
                print(f"从预加载的数据集中获取动作序列: {action_sequence}")

        # 11. 如果仍然没有找到动作序列，尝试从数据集文件中重新加载
        if not action_sequence and os.path.exists(self.dataset_path):
            try:
                with open(self.dataset_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            sample = json.loads(line.strip())
                            if 'folder_id' in sample and str(sample['folder_id']) == scene_id and 'action_sequence' in sample:
                                # 确保action_sequence是列表
                                if isinstance(sample['action_sequence'], list):
                                    action_sequence = [self.normalize_action(action) for action in sample['action_sequence'] if self.normalize_action(action)]
                                elif isinstance(sample['action_sequence'], str):
                                    # 尝试解析字符串为列表
                                    try:
                                        action_list = json.loads(sample['action_sequence'])
                                        if isinstance(action_list, list):
                                            action_sequence = [self.normalize_action(action) for action in action_list if self.normalize_action(action)]
                                        else:
                                            action_sequence = [self.normalize_action(sample['action_sequence'])]
                                    except:
                                        action_sequence = [self.normalize_action(sample['action_sequence'])]
                                else:
                                    action_sequence = [self.normalize_action(sample['action_sequence'])]

                                if action_sequence:
                                    self.action_sequences[scene_id] = action_sequence
                                    action_sequence_source = "数据集文件实时加载"
                                    if debug:
                                        print(f"从数据集文件中实时加载动作序列: {action_sequence}")
                                    break
                        except Exception as e:
                            if debug:
                                print(f"解析数据集样本时出错: {e}")
                            continue
            except Exception as e:
                if debug:
                    print(f"从数据集文件中实时加载动作序列时出错: {e}")

        for content, sol in zip(completions, solution):
            try:
                # 确保内容是字符串
                if not isinstance(content, str):
                    content = str(content)
                if not isinstance(sol, str):
                    sol = str(sol)

                # 检测是否有动作合并
                merged_action, merge_count = self.detect_action_merging(content)

                # 首先尝试从<answer>标签中提取动作
                extracted_action = self.extract_action_from_answer_tag(content)
                extraction_method = "<answer>标签提取"

                # 如果没有从<answer>标签中提取到动作，则尝试从整个内容中提取
                if not extracted_action:
                    extracted_action, extraction_method = self.extract_action_from_content(content)

                # 标准化提取的动作和正确答案
                normalized_content = self.normalize_action(extracted_action)
                normalized_solution = self.normalize_action(sol)

                # 查找当前动作在序列中的索引
                if action_sequence:
                    # 如果已经有当前索引，检查它是否有效
                    if current_index >= 0 and current_index < len(action_sequence):
                        # 验证当前索引是否指向正确的动作
                        if action_sequence[current_index] != normalized_solution:
                            # 如果不匹配，重新查找正确的索引
                            current_index = -1

                    # 如果没有有效的当前索引，尝试查找
                    if current_index < 0:
                        try:
                            current_index = action_sequence.index(normalized_solution)
                        except ValueError:
                            # 如果正确答案不在序列中，尝试查找类似的动作
                            for i, action in enumerate(action_sequence):
                                if normalized_solution.lower() in action.lower() or action.lower() in normalized_solution.lower():
                                    current_index = i
                                    break

                # 计算软性奖励
                if action_sequence and current_index >= 0:
                    reward, future_steps = self.calculate_soft_reward(
                        normalized_content, normalized_solution, action_sequence, current_index
                    )
                else:
                    # 如果没有动作序列或找不到当前索引，使用传统的精确匹配
                    reward = float(normalized_content == normalized_solution)
                    future_steps = 0 if reward > 0 else -1

                # 打印调试信息
                if debug:
                    print(f"场景ID: {scene_id}")
                    if action_sequence:
                        print(f"动作序列来源: {action_sequence_source}")
                        print(f"动作序列: {action_sequence}")
                        print(f"当前动作索引: {current_index}")

                        # 打印当前位置往后4步的动作
                        if current_index >= 0 and current_index < len(action_sequence):
                            print(f"当前动作: {action_sequence[current_index]}")
                            future_actions = []
                            for i in range(1, min(self.MAX_FUTURE_STEPS + 1, len(action_sequence) - current_index)):
                                future_index = current_index + i
                                future_actions.append(f"未来第{i}步: {action_sequence[future_index]}")
                            if future_actions:
                                print(f"未来动作: {', '.join(future_actions)}")
                            else:
                                print("没有未来动作")

                    print(f"提取方法: {extraction_method}")
                    print(f"原始回答: {extracted_action}")
                    print(f"标准化后模型答案: {normalized_content}")
                    print(f"正确答案: {normalized_solution}")

                    if merged_action:
                        print(f"检测到动作合并: {merged_action} (重复 {merge_count} 次)")

                    if future_steps > 0:
                        print(f"预测的是未来第 {future_steps} 步的动作")

                    print(f"奖励分数: {reward}")
                    print("-" * 50)

            except Exception as e:
                print(f"处理答案时发生错误: {e}")
                import traceback
                traceback.print_exc()
                reward = 0.0  # 发生错误时返回0分

            rewards.append(reward)

        return rewards


# 注册奖励函数
orms['navigation_accuracy'] = NavigationAccuracy

# 为了兼容性，也注册到swift的orms中
from swift.plugin.orm import orms
orms['navigation_accuracy'] = NavigationAccuracy
