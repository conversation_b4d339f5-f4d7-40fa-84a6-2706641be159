#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Swift RLHF格式验证器

该脚本用于验证生成的Swift RLHF格式数据集是否与原始格式完全兼容。

使用方法:
```bash
python validate_swift_rlhf_format.py --input_file grpo_dataset_swift.jsonl --reference_file data/navigation_qa_instruction.jsonl
```

作者: AI助手
日期: 2023-11-20
"""

import json
import argparse
from typing import Dict, List, Any


def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSONL文件

    Args:
        file_path: JSONL文件路径

    Returns:
        样本列表
    """
    samples = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                samples.append(json.loads(line))
    return samples


def validate_sample_structure(sample: Dict[str, Any], sample_idx: int) -> List[str]:
    """
    验证单个样本的结构

    Args:
        sample: 样本字典
        sample_idx: 样本索引

    Returns:
        错误列表
    """
    errors = []

    # 检查必需字段
    required_fields = ["messages", "images", "solution", "folder_id", "current_index", "action_sequence", "action_reward"]
    for field in required_fields:
        if field not in sample:
            errors.append(f"样本 {sample_idx}: 缺少必需字段 '{field}'")

    # 检查messages结构
    if "messages" in sample:
        messages = sample["messages"]
        if not isinstance(messages, list) or len(messages) != 2:
            errors.append(f"样本 {sample_idx}: messages应为包含2个元素的列表")
        else:
            # 检查system message
            if messages[0].get("role") != "system":
                errors.append(f"样本 {sample_idx}: 第一个message的role应为'system'")
            if "content" not in messages[0]:
                errors.append(f"样本 {sample_idx}: 第一个message缺少content字段")

            # 检查user message
            if messages[1].get("role") != "user":
                errors.append(f"样本 {sample_idx}: 第二个message的role应为'user'")
            if "content" not in messages[1]:
                errors.append(f"样本 {sample_idx}: 第二个message缺少content字段")

    # 检查images字段
    if "images" in sample:
        images = sample["images"]
        if not isinstance(images, list):
            errors.append(f"样本 {sample_idx}: images应为列表")
        elif len(images) == 0:
            errors.append(f"样本 {sample_idx}: images列表不能为空")

    # 检查action_reward结构
    if "action_reward" in sample:
        action_reward = sample["action_reward"]
        if not isinstance(action_reward, dict):
            errors.append(f"样本 {sample_idx}: action_reward应为字典")
        else:
            expected_actions = [
                "Move forward", "Move backward", "Move left", "Move right",
                "Move up", "Move down", "Rotate left", "Rotate right",
                "Look up", "Look down"
            ]
            for action in expected_actions:
                if action not in action_reward:
                    errors.append(f"样本 {sample_idx}: action_reward缺少动作 '{action}'")
                elif not isinstance(action_reward[action], (int, float, dict)):
                    errors.append(f"样本 {sample_idx}: action_reward['{action}']应为数值或字典")

    # 检查其他字段类型
    if "folder_id" in sample and not isinstance(sample["folder_id"], str):
        errors.append(f"样本 {sample_idx}: folder_id应为字符串")

    if "current_index" in sample and not isinstance(sample["current_index"], int):
        errors.append(f"样本 {sample_idx}: current_index应为整数")

    if "action_sequence" in sample and not isinstance(sample["action_sequence"], list):
        errors.append(f"样本 {sample_idx}: action_sequence应为列表")

    if "solution" in sample and not isinstance(sample["solution"], str):
        errors.append(f"样本 {sample_idx}: solution应为字符串")

    return errors


def compare_with_reference(sample: Dict[str, Any], reference_samples: List[Dict[str, Any]], sample_idx: int) -> List[str]:
    """
    与参考样本比较结构一致性

    Args:
        sample: 待验证样本
        reference_samples: 参考样本列表
        sample_idx: 样本索引

    Returns:
        警告列表
    """
    warnings = []

    if not reference_samples:
        return warnings

    # 使用第一个参考样本作为结构模板
    ref_sample = reference_samples[0]

    # 检查字段一致性
    ref_fields = set(ref_sample.keys())
    sample_fields = set(sample.keys())

    # 检查缺少的字段
    missing_fields = ref_fields - sample_fields
    if missing_fields:
        warnings.append(f"样本 {sample_idx}: 与参考格式相比缺少字段: {missing_fields}")

    # 检查额外的字段
    extra_fields = sample_fields - ref_fields
    if extra_fields:
        warnings.append(f"样本 {sample_idx}: 与参考格式相比多出字段: {extra_fields}")

    # 检查messages结构一致性
    if "messages" in sample and "messages" in ref_sample:
        if len(sample["messages"]) != len(ref_sample["messages"]):
            warnings.append(f"样本 {sample_idx}: messages长度与参考格式不一致")

    return warnings


def validate_swift_rlhf_format(input_file: str, reference_file: str = None) -> Dict[str, Any]:
    """
    验证Swift RLHF格式数据集

    Args:
        input_file: 待验证的数据集文件路径
        reference_file: 参考数据集文件路径（可选）

    Returns:
        验证结果字典
    """
    print(f"加载数据集: {input_file}")
    samples = load_jsonl(input_file)

    reference_samples = []
    if reference_file:
        print(f"加载参考数据集: {reference_file}")
        try:
            reference_samples = load_jsonl(reference_file)
        except Exception as e:
            print(f"警告: 无法加载参考数据集: {e}")

    print(f"验证 {len(samples)} 个样本...")

    all_errors = []
    all_warnings = []

    for i, sample in enumerate(samples):
        # 验证样本结构
        errors = validate_sample_structure(sample, i)
        all_errors.extend(errors)

        # 与参考格式比较
        if reference_samples:
            warnings = compare_with_reference(sample, reference_samples, i)
            all_warnings.extend(warnings)

    # 统计信息
    stats = {
        "total_samples": len(samples),
        "error_count": len(all_errors),
        "warning_count": len(all_warnings),
        "validation_passed": len(all_errors) == 0
    }

    # 字段统计
    field_stats = {}
    for sample in samples:
        for field in sample.keys():
            field_stats[field] = field_stats.get(field, 0) + 1

    # 动作统计
    action_stats = {}
    for sample in samples:
        if "solution" in sample:
            action = sample["solution"]
            action_stats[action] = action_stats.get(action, 0) + 1

    return {
        "statistics": stats,
        "field_statistics": field_stats,
        "action_statistics": action_stats,
        "errors": all_errors,
        "warnings": all_warnings
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Swift RLHF格式验证器")
    parser.add_argument("--input_file", required=True, help="待验证的数据集文件路径")
    parser.add_argument("--reference_file", help="参考数据集文件路径")
    parser.add_argument("--output_file", default="validation_report.json", help="验证报告输出文件路径")
    args = parser.parse_args()

    print("=" * 60)
    print("Swift RLHF格式验证器")
    print("=" * 60)

    try:
        # 执行验证
        result = validate_swift_rlhf_format(args.input_file, args.reference_file)

        # 保存验证报告
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        # 打印结果
        stats = result["statistics"]
        print(f"\n验证结果:")
        print(f"总样本数: {stats['total_samples']}")
        print(f"错误数量: {stats['error_count']}")
        print(f"警告数量: {stats['warning_count']}")
        print(f"验证状态: {'通过' if stats['validation_passed'] else '失败'}")

        # 打印字段统计
        print(f"\n字段统计:")
        for field, count in sorted(result["field_statistics"].items()):
            coverage = count / stats['total_samples'] * 100
            print(f"  {field}: {count} ({coverage:.1f}%)")

        # 打印动作统计
        print(f"\n动作统计:")
        for action, count in sorted(result["action_statistics"].items()):
            print(f"  {action}: {count}")

        # 打印错误（如果有）
        if result["errors"]:
            print(f"\n错误详情 (前10个):")
            for error in result["errors"][:10]:
                print(f"  - {error}")
            if len(result["errors"]) > 10:
                print(f"  ... 还有 {len(result['errors']) - 10} 个错误")

        # 打印警告（如果有）
        if result["warnings"]:
            print(f"\n警告详情 (前10个):")
            for warning in result["warnings"][:10]:
                print(f"  - {warning}")
            if len(result["warnings"]) > 10:
                print(f"  ... 还有 {len(result['warnings']) - 10} 个警告")

        print(f"\n验证报告已保存到: {args.output_file}")
        print("=" * 60)

        # 返回适当的退出码
        return 0 if stats['validation_passed'] else 1

    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
