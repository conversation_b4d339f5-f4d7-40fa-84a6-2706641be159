nproc_per_node=8
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 \
NPROC_PER_NODE=$nproc_per_node \
swift sft \
    --model Qwen/Qwen2.5-VL-3B-Instruct \
    --train_type full \
    --model_type qwen2_5_vl \
    --dataset "/home/<USER>/wzy/NIPS/data/json/navigation_dataset_SFT.jsonl" \
    --torch_dtype bfloat16 \
    --num_train_epochs 4 \
    --per_device_train_batch_size 1 \
    --per_device_eval_batch_size 1 \
    --learning_rate 5e-7 \
    --target_modules all-linear \
    --gradient_accumulation_steps 4 \
    --eval_steps 20000 \
    --save_steps 100 \
    --save_total_limit 10 \
    --save_only_model true \
    --logging_steps 1 \
    --max_length 6144 \
    --output_dir /home/<USER>/wzy/NIPS/port \
    --warmup_ratio 0.05 \
    --dataloader_num_workers 8 \
    --deepspeed zero3
