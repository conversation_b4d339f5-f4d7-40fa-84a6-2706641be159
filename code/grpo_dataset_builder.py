#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GRPO训练数据集构建器

该模块用于从导航评分结果中构建GRPO训练数据集，包括：
1. 负样本提取：从step_score低于0.0的决策点提取负样本
2. 正样本噪声增强：随机抽取正样本进行平衡
3. 动作遍历评分：为每个样本的所有10个可能动作计算奖励值
4. 数据格式兼容性：确保与Swift RLHF训练流程兼容

使用方法:
```bash
python grpo_dataset_builder.py --scores_file navigation_scores.json --output_file grpo_dataset.json
```

作者: AI助手
日期: 2023-11-20
"""

import os
import json
import math
import random
import argparse
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import csv
from collections import defaultdict

# 导入现有评分系统的核心函数
from navigation_scoring_system import (
    COMMANDS_MAP,
    load_coordinates,
    load_actions,
    calculate_reward_2,
    calculate_reward_3,
    find_nearest_point,
    calculate_trajectory_reference_point,
    calculate_efficiency_score
)

# 导入历史图像采样函数
import sys
sys.path.append('data')
try:
    from process_numeric_dataset_with_rewards import sample_history_images
except ImportError:
    print("警告: 无法导入sample_history_images函数，将使用简化版本")
    def sample_history_images(initial_img: str, history_images: List[str], max_frames: int = 8) -> List[str]:
        """简化版本的历史图像采样函数"""
        if len(history_images) + 1 <= max_frames:
            return [initial_img] + history_images
        sample_count = max_frames - 2
        current_img = history_images[-1]
        if len(history_images) > 1:
            step = (len(history_images) - 1) / sample_count
            indices = [int(i * step) for i in range(sample_count)]
            indices = [min(i, len(history_images) - 2) for i in indices]
            sampled_history = [history_images[i] for i in indices]
        else:
            sampled_history = []
        return [initial_img] + sampled_history + [current_img]

# 所有可能的导航动作
ALL_ACTIONS = [
    "向前运动", "向后运动", "向左运动", "向右运动",
    "向上运动", "向下运动", "向左旋转", "向右旋转",
    "向上看", "向下看"
]

# 中英文动作映射
ZH_TO_EN_ACTION_MAPPING = {
    "向前运动": "Move forward",
    "向后运动": "Move backward",
    "向左运动": "Move left",
    "向右运动": "Move right",
    "向上运动": "Move up",
    "向下运动": "Move down",
    "向左旋转": "Rotate left",
    "向右旋转": "Rotate right",
    "向上看": "Look up",
    "向下看": "Look down"
}

# 动作参数映射
ACTION_PARAMS = {
    "向前运动": (10, 0, 0),
    "向后运动": (-10, 0, 0),
    "向左运动": (0, -10, 0),
    "向右运动": (0, 10, 0),
    "向上运动": (0, 0, -10),
    "向下运动": (0, 0, 10),
    "向左旋转": -22.5,  # 度
    "向右旋转": 22.5,   # 度
    "向上看": 45,       # 度
    "向下看": -45       # 度
}


def apply_action_to_location(current_loc: np.ndarray, action: str) -> np.ndarray:
    """
    将动作应用到当前位置，计算执行动作后的位置

    Args:
        current_loc: 当前位置坐标 [x, y, z, rx, ry, yaw, pitch]
        action: 动作名称

    Returns:
        执行动作后的位置坐标
    """
    new_loc = current_loc.copy()

    if action not in ACTION_PARAMS:
        return new_loc

    params = ACTION_PARAMS[action]

    if action in ["向前运动", "向后运动", "向左运动", "向右运动", "向上运动", "向下运动"]:
        # 位置移动
        dx, dy, dz = params
        new_loc[0] += dx  # x
        new_loc[1] += dy  # y
        new_loc[2] += dz  # z
    elif action in ["向左旋转", "向右旋转"]:
        # 偏航角旋转（弧度制）
        angle_deg = params
        angle_rad = math.radians(angle_deg)
        new_loc[5] += angle_rad  # yaw
    elif action in ["向上看", "向下看"]:
        # 俯仰角旋转（度数制）
        angle_deg = params
        new_loc[6] += angle_deg  # pitch

    return new_loc


def extract_samples_from_scores(scores: Dict[str, Any], negative_threshold: float = 0.0) -> Tuple[List[Dict], List[Dict]]:
    """
    从评分结果中提取负样本和正样本

    Args:
        scores: 评分结果字典
        negative_threshold: 负样本阈值，低于该值的决策点被认为是负样本

    Returns:
        (负样本列表, 正样本列表)
    """
    negative_samples = []
    positive_samples = []

    # 遍历每个任务
    for task_key, task_data in scores.items():
        # 跳过汇总信息和失败任务汇总
        if task_key in ["summary", "failed_tasks"]:
            continue

        # 跳过出错的任务
        if "error" in task_data:
            continue

        # 获取任务基本信息
        original_task_id = task_data.get("original_task_id", task_key.split("_")[0])
        attempt_id = task_data.get("attempt_id", "0")

        # 优先使用step_details（新格式）
        if "step_details" in task_data:
            step_details = task_data["step_details"]

            for detail in step_details:
                sample = {
                    "task_id": task_key,
                    "original_task_id": original_task_id,
                    "attempt_id": attempt_id,
                    "step": detail["step"],
                    "action_id": detail["action_id"],
                    "action_name": detail["action_name"],
                    "endpoint_score": detail["endpoint_score"],
                    "trajectory_score": detail["trajectory_score"],
                    "efficiency_score": detail["efficiency_score"],
                    "total_score": detail["total_score"]
                }

                # 根据总分分类
                if detail["total_score"] < negative_threshold:
                    negative_samples.append(sample)
                else:
                    positive_samples.append(sample)
        else:
            # 回退到旧格式兼容性
            step_scores = task_data.get("step_scores", [])
            endpoint_scores = task_data.get("endpoint_scores", [])
            trajectory_scores = task_data.get("trajectory_scores", [])
            efficiency_score = task_data.get("efficiency_score", 0.0)

            for i, score in enumerate(step_scores):
                # 效率评分平摊到每个步骤
                efficiency_contribution = efficiency_score / len(step_scores) if len(step_scores) > 0 else 0.0

                sample = {
                    "task_id": task_key,
                    "original_task_id": original_task_id,
                    "attempt_id": attempt_id,
                    "step": i,
                    "action_id": "unknown",
                    "action_name": "unknown",
                    "endpoint_score": endpoint_scores[i] if i < len(endpoint_scores) else 0.0,
                    "trajectory_score": trajectory_scores[i] if i < len(trajectory_scores) else 0.0,
                    "efficiency_score": efficiency_contribution,
                    "total_score": score
                }

                # 根据总分分类
                if score < negative_threshold:
                    negative_samples.append(sample)
                else:
                    positive_samples.append(sample)

    return negative_samples, positive_samples


def load_navigation_target(task_id: str, base_dir: str = "data") -> str:
    """
    从goal.txt文件加载导航目标

    Args:
        task_id: 任务ID
        base_dir: 数据目录

    Returns:
        导航目标字符串，如果文件不存在或读取失败则返回None
    """
    # 首先尝试从dataset_rl目录读取
    goal_file_rl = os.path.join(base_dir, "dataset_rl", task_id, "0", "goal.txt")
    if os.path.exists(goal_file_rl):
        try:
            with open(goal_file_rl, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    return content
        except Exception as e:
            print(f"警告: 读取goal.txt失败 {goal_file_rl}: {e}")

    # 回退到dataset_video_instruction目录
    goal_file_instruction = os.path.join(base_dir, "dataset_video_instruction", task_id, "goal.txt")
    if os.path.exists(goal_file_instruction):
        try:
            with open(goal_file_instruction, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    return content
        except Exception as e:
            print(f"警告: 读取goal.txt失败 {goal_file_instruction}: {e}")

    return None


def load_task_data(task_id: str, attempt_id: str, base_dir: str = "data") -> Tuple[List[np.ndarray], List[str], List[np.ndarray], str]:
    """
    加载任务的原始数据

    Args:
        task_id: 任务ID
        attempt_id: 尝试次数ID
        base_dir: 数据目录

    Returns:
        (模型坐标列表, 模型动作列表, 参考坐标列表, 导航目标)
    """
    # 构建文件路径
    reference_dir = os.path.join(base_dir, "dataset_video_instruction", task_id)
    model_dir = os.path.join(base_dir, "dataset_rl", task_id, attempt_id)

    # 加载坐标和动作
    model_coords = load_coordinates(os.path.join(model_dir, "loc.csv"))
    model_actions = load_actions(os.path.join(model_dir, "path.csv"))
    reference_coords = load_coordinates(os.path.join(reference_dir, "loc.csv"))

    # 加载导航目标
    navigation_target = load_navigation_target(task_id, base_dir)
    if not navigation_target:
        navigation_target = f"任务{task_id}的导航目标"

    return model_coords, model_actions, reference_coords, navigation_target


def calculate_action_rewards(current_loc: np.ndarray, reference_coords: List[np.ndarray],
                           is_positive_sample: bool = False) -> Dict[str, Dict[str, float]]:
    """
    计算所有可能动作的奖励值

    Args:
        current_loc: 当前位置坐标
        reference_coords: 参考轨迹坐标列表
        is_positive_sample: 是否为正样本（影响是否包含效率评分）

    Returns:
        动作奖励字典，键为动作名称，值为奖励分解字典
    """
    action_rewards = {}
    final_loc = reference_coords[-1]  # 终点坐标

    # 找到参考轨迹中最近的点
    nearest_idx = find_nearest_point(current_loc, reference_coords)
    trajectory_ref_point = calculate_trajectory_reference_point(reference_coords, nearest_idx)

    for action in ALL_ACTIONS:
        # 计算执行动作后的位置
        action_loc = apply_action_to_location(current_loc, action)

        # 计算终点关系评分（奖励2和奖励3）
        endpoint_reward2 = calculate_reward_2(current_loc, action_loc, final_loc)
        endpoint_reward3 = calculate_reward_3(current_loc, action_loc, final_loc)
        endpoint_score = (endpoint_reward2 + endpoint_reward3) / 2.0

        # 计算轨迹关系评分（奖励2和奖励3）
        trajectory_reward2 = calculate_reward_2(current_loc, action_loc, trajectory_ref_point)
        trajectory_reward3 = calculate_reward_3(current_loc, action_loc, trajectory_ref_point)
        trajectory_score = (trajectory_reward2 + trajectory_reward3) / 2.0

        # 计算效率评分（仅对正样本）
        efficiency_score = 0.0
        if is_positive_sample:
            # 对于正样本，计算效率评分
            # 这里简化处理，使用固定的效率评分
            efficiency_score = 0.1  # 可以根据实际需求调整

        # 计算总奖励
        if is_positive_sample:
            total_reward = endpoint_score + trajectory_score + efficiency_score
        else:
            total_reward = endpoint_score + trajectory_score

        # 存储奖励分解
        action_rewards[action] = {
            "endpoint_score": endpoint_score,
            "trajectory_score": trajectory_score,
            "efficiency_score": efficiency_score,
            "total_reward": total_reward
        }

    return action_rewards


def build_grpo_sample(sample: Dict, model_coords: List[np.ndarray], model_actions: List[str],
                     reference_coords: List[np.ndarray], navigation_target: str,
                     base_dir: str = "data") -> Dict[str, Any]:
    """
    构建单个GRPO训练样本

    Args:
        sample: 样本信息字典
        model_coords: 模型轨迹坐标列表
        model_actions: 模型动作列表
        reference_coords: 参考轨迹坐标列表
        navigation_target: 导航目标
        base_dir: 数据目录

    Returns:
        GRPO训练样本字典
    """
    step = sample["step"]
    task_id = sample["original_task_id"]
    attempt_id = sample["attempt_id"]

    # 获取当前位置
    if step < len(model_coords):
        current_loc = model_coords[step]
    else:
        return None

    # 构建图像路径
    model_dir = os.path.join(base_dir, "dataset_rl", task_id, attempt_id)
    current_image = os.path.join(model_dir, f"{step}.png")

    # 检查图像是否存在
    if not os.path.exists(current_image):
        return None

    # 判断是否为正样本
    is_positive_sample = sample["total_score"] >= 0.0

    # 计算所有动作的奖励值
    action_rewards = calculate_action_rewards(current_loc, reference_coords, is_positive_sample)

    # 转换为英文动作奖励
    action_rewards_en = {}
    for action_zh, rewards in action_rewards.items():
        action_en = ZH_TO_EN_ACTION_MAPPING.get(action_zh, action_zh)
        action_rewards_en[action_en] = rewards

    # 构建GRPO样本
    grpo_sample = {
        "task_id": sample["task_id"],
        "original_task_id": task_id,
        "attempt_id": attempt_id,
        "step": step,
        "navigation_target": navigation_target,
        "current_image": current_image,
        "current_location": current_loc.tolist(),
        "model_action": sample["action_name"],
        "model_action_id": sample["action_id"],
        "sample_type": "positive" if is_positive_sample else "negative",
        "original_scores": {
            "endpoint_score": sample["endpoint_score"],
            "trajectory_score": sample["trajectory_score"],
            "efficiency_score": sample["efficiency_score"],
            "total_score": sample["total_score"]
        },
        "action_rewards": action_rewards_en,
        "metadata": {
            "reference_steps": len(reference_coords) - 1,
            "model_steps": len(model_actions),
            "final_location": reference_coords[-1].tolist() if reference_coords else None
        }
    }

    return grpo_sample


def build_grpo_dataset(scores_file: str, base_dir: str = "data",
                      max_positive_samples: int = 200,
                      negative_threshold: float = 0.0) -> Dict[str, Any]:
    """
    构建GRPO训练数据集

    Args:
        scores_file: 评分结果文件路径
        base_dir: 数据目录
        max_positive_samples: 最大正样本数量
        negative_threshold: 负样本阈值

    Returns:
        GRPO数据集字典
    """
    print(f"加载评分结果: {scores_file}")
    with open(scores_file, 'r', encoding='utf-8') as f:
        scores = json.load(f)

    # 提取负样本和正样本
    print("提取负样本和正样本...")
    negative_samples, positive_samples = extract_samples_from_scores(scores, negative_threshold)

    print(f"找到 {len(negative_samples)} 个负样本")
    print(f"找到 {len(positive_samples)} 个正样本")

    # 随机抽取正样本
    if len(positive_samples) > max_positive_samples:
        print(f"随机抽取 {max_positive_samples} 个正样本")
        positive_samples = random.sample(positive_samples, max_positive_samples)

    # 合并所有样本
    all_samples = negative_samples + positive_samples
    print(f"总样本数: {len(all_samples)}")

    # 按任务分组加载数据
    task_data_cache = {}
    grpo_samples = []
    failed_samples = []

    print("构建GRPO样本...")
    for i, sample in enumerate(all_samples):
        if (i + 1) % 100 == 0:
            print(f"处理进度: {i + 1}/{len(all_samples)}")

        task_key = (sample["original_task_id"], sample["attempt_id"])

        # 加载任务数据（使用缓存）
        if task_key not in task_data_cache:
            try:
                model_coords, model_actions, reference_coords, navigation_target = load_task_data(
                    sample["original_task_id"], sample["attempt_id"], base_dir
                )
                task_data_cache[task_key] = (model_coords, model_actions, reference_coords, navigation_target)
            except Exception as e:
                print(f"加载任务数据失败 {task_key}: {e}")
                failed_samples.append(sample)
                continue

        model_coords, model_actions, reference_coords, navigation_target = task_data_cache[task_key]

        # 构建GRPO样本
        try:
            grpo_sample = build_grpo_sample(
                sample, model_coords, model_actions, reference_coords, navigation_target, base_dir
            )
            if grpo_sample:
                grpo_samples.append(grpo_sample)
            else:
                failed_samples.append(sample)
        except Exception as e:
            print(f"构建GRPO样本失败 {sample}: {e}")
            failed_samples.append(sample)

    # 统计信息
    negative_count = sum(1 for s in grpo_samples if s["sample_type"] == "negative")
    positive_count = sum(1 for s in grpo_samples if s["sample_type"] == "positive")

    # 按任务分组统计
    task_stats = defaultdict(lambda: {"negative": 0, "positive": 0})
    for sample in grpo_samples:
        task_stats[sample["original_task_id"]][sample["sample_type"]] += 1

    # 动作频率统计
    action_freq = defaultdict(int)
    for sample in grpo_samples:
        action_freq[sample["model_action"]] += 1

    # 构建最终数据集
    dataset = {
        "samples": grpo_samples,
        "statistics": {
            "total_samples": len(grpo_samples),
            "negative_samples": negative_count,
            "positive_samples": positive_count,
            "failed_samples": len(failed_samples),
            "task_count": len(task_stats),
            "task_distribution": dict(task_stats),
            "action_frequency": dict(action_freq)
        },
        "metadata": {
            "negative_threshold": negative_threshold,
            "max_positive_samples": max_positive_samples,
            "base_dir": base_dir,
            "scores_file": scores_file
        }
    }

    if failed_samples:
        dataset["failed_samples"] = failed_samples

    return dataset


def generate_statistics_report(dataset: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成数据集统计报告

    Args:
        dataset: GRPO数据集

    Returns:
        统计报告字典
    """
    stats = dataset["statistics"]
    samples = dataset["samples"]

    # 奖励值分布统计
    reward_stats = {
        "endpoint_scores": [],
        "trajectory_scores": [],
        "efficiency_scores": [],
        "total_rewards": []
    }

    for sample in samples:
        for action, rewards in sample["action_rewards"].items():
            reward_stats["endpoint_scores"].append(rewards["endpoint_score"])
            reward_stats["trajectory_scores"].append(rewards["trajectory_score"])
            reward_stats["efficiency_scores"].append(rewards["efficiency_score"])
            reward_stats["total_rewards"].append(rewards["total_reward"])

    # 计算统计量
    def calc_stats(values):
        if not values:
            return {"min": 0, "max": 0, "mean": 0, "std": 0}
        values = np.array(values)
        return {
            "min": float(np.min(values)),
            "max": float(np.max(values)),
            "mean": float(np.mean(values)),
            "std": float(np.std(values))
        }

    reward_distribution = {
        "endpoint_scores": calc_stats(reward_stats["endpoint_scores"]),
        "trajectory_scores": calc_stats(reward_stats["trajectory_scores"]),
        "efficiency_scores": calc_stats(reward_stats["efficiency_scores"]),
        "total_rewards": calc_stats(reward_stats["total_rewards"])
    }

    # 样本类型分布
    sample_type_dist = {"negative": 0, "positive": 0}
    for sample in samples:
        sample_type_dist[sample["sample_type"]] += 1

    # 任务贡献度分析
    task_contributions = []
    for task_id, task_stat in stats["task_distribution"].items():
        if isinstance(task_stat, dict) and "negative_samples" in task_stat and "positive_samples" in task_stat:
            # 新的批量处理格式
            total_task_samples = task_stat["negative_samples"] + task_stat["positive_samples"]
            task_contributions.append({
                "task_id": task_id,
                "total_samples": total_task_samples,
                "negative_samples": task_stat["negative_samples"],
                "positive_samples": task_stat["positive_samples"],
                "negative_ratio": task_stat["negative_samples"] / total_task_samples if total_task_samples > 0 else 0
            })
        elif isinstance(task_stat, dict) and "negative" in task_stat and "positive" in task_stat:
            # 旧的单任务格式
            total_task_samples = task_stat["negative"] + task_stat["positive"]
            task_contributions.append({
                "task_id": task_id,
                "total_samples": total_task_samples,
                "negative_samples": task_stat["negative"],
                "positive_samples": task_stat["positive"],
                "negative_ratio": task_stat["negative"] / total_task_samples if total_task_samples > 0 else 0
            })
        else:
            # 处理其他格式或错误情况
            print(f"警告: 任务 {task_id} 的统计格式不正确: {task_stat}")
            continue

    # 按样本数量排序
    task_contributions.sort(key=lambda x: x["total_samples"], reverse=True)

    report = {
        "dataset_summary": {
            "total_samples": stats["total_samples"],
            "negative_samples": stats["negative_samples"],
            "positive_samples": stats["positive_samples"],
            "failed_samples": stats["failed_samples"],
            "task_count": stats["task_count"],
            "negative_ratio": stats["negative_samples"] / stats["total_samples"] if stats["total_samples"] > 0 else 0
        },
        "reward_distribution": reward_distribution,
        "sample_type_distribution": sample_type_dist,
        "action_frequency": stats["action_frequency"],
        "task_contributions": task_contributions[:10],  # 前10个贡献最多的任务
        "top_negative_tasks": [t for t in task_contributions if t["negative_samples"] > 0][:5],
        "top_positive_tasks": [t for t in task_contributions if t["positive_samples"] > 0][:5]
    }

    return report


def build_swift_rlhf_sample(grpo_sample: Dict[str, Any], base_dir: str = "data") -> Dict[str, Any]:
    """
    将GRPO样本转换为Swift RLHF兼容格式

    Args:
        grpo_sample: GRPO样本字典
        base_dir: 数据目录

    Returns:
        Swift RLHF兼容格式的样本字典
    """
    # 系统提示内容
    system_content = """A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> \\n<answer> answer here </answer>.

Within the <think> tag, you must use the following structured format:
<HISTORICAL CONTEXT>
Analysis of historical observations and context.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
Analysis of spatial relationships and goal orientation.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
Decomposition of navigation plan and identification of current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
Analysis of possible actions and their impacts.
</ACTION ANALYSIS>

<DECISION>
Final decision reasoning and selection.
</DECISION>

Ensure that your answer is consistent with and directly derived from your thinking process, maintaining logical coherence between the two sections. User: . Assistant:
You are a visual navigation decision system. You are receiving first-person view navigation images from a drone. The drone can perform the following actions:
- Move forward 10m
- Move backward 10m
- Move left 10m
- Move right 10m
- Move up 10m
- Move down 10m
- Rotate left 22.5 degrees
- Rotate right 22.5 degrees
- Tilt camera up 45 degrees
- Tilt camera down 45 degrees
"""

    # 构建历史图像列表
    task_id = grpo_sample["original_task_id"]
    attempt_id = grpo_sample["attempt_id"]
    step = grpo_sample["step"]

    # 构建图像路径
    reference_dir = os.path.join(base_dir, "dataset_video_instruction", task_id)
    model_dir = os.path.join(base_dir, "dataset_rl", task_id, attempt_id)

    # 修复1: 读取实际的导航目标
    navigation_target = load_navigation_target(task_id, base_dir)
    if not navigation_target:
        # 如果无法读取goal.txt，跳过这个样本
        print(f"警告: 无法读取任务 {task_id} 的导航目标，跳过该样本")
        return None

    # 构建图像列表
    images = []
    history_images = []

    # 初始图像
    initial_image = os.path.join(reference_dir, "initial.png")
    if not os.path.exists(initial_image):
        print(f"警告: 初始图像不存在: {initial_image}")
        return None

    # 历史观察图像
    for i in range(step):
        history_image = os.path.join(model_dir, f"{i}.png")
        if os.path.exists(history_image):
            history_images.append(history_image)

    # 当前位置图像
    current_image = os.path.join(model_dir, f"{step}.png")
    if not os.path.exists(current_image):
        print(f"警告: 当前图像不存在: {current_image}")
        return None

    # 修复2: 应用历史图像采样策略
    if step == 0:
        # 没有历史记录的情况
        images = [initial_image, current_image]
    else:
        # 有历史记录的情况，应用采样策略
        sampled_images = sample_history_images(initial_image, history_images + [current_image], max_frames=8)
        images = sampled_images

    if step == 0:
        # 没有历史记录的情况
        user_content = f"""Current position image:
<image>
Please continue to the navigation target {navigation_target} given at the initial position. Analyze the historical observations and current position carefully. In your <think> section, follow this structured reasoning process:

<HISTORICAL CONTEXT>
   - Describe observations from current and past views, focusing on target-related objects and environmental changes.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
   - Analyze the drone's current position, orientation, visible environmental features or landmarks, and determine the spatial relationship, direction, distance, and potential obstacles to the current step's objective.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
   - Decompose the navigation plan and identify the current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
   - For each action, predict its outcome, evaluate its contribution to the current step's objective, consider risks, and assess information gain.
</ACTION ANALYSIS>

<DECISION>
   - Select the optimal action based on progress, alignment, safety, and information gain for the current step and overall goal.
</DECISION>

Your final <answer> must contain ONLY ONE of the following actions, with no additional text:
- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down"""
    else:
        # 有历史记录的情况
        user_content = f"""Navigation target: {navigation_target}

Historical observations:
Initial position image:
<image>

"""

        # 添加历史观察
        for i in range(1, len(images) - 1):
            if i == 1:
                user_content += f"Historical observation {i}:\n<image>\n\n"
            else:
                user_content += f"Historical observation {i}:\n<image>\n\n"

        user_content += f"""Current position:
<image>

Please continue to the navigation target {navigation_target} given at the initial position. Analyze the historical observations and current position carefully.In your <think> section, follow this structured reasoning process:

<HISTORICAL CONTEXT>
   - Describe observations from current and past views, focusing on target-related objects and environmental changes.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
   - Analyze the drone's current position, orientation, visible environmental features or landmarks, and determine the spatial relationship, direction, distance, and potential obstacles to the current step's objective.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
   - Decompose the navigation plan and identification of current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
   - For each action, predict its outcome, evaluate its contribution to the current step's objective, consider risks, and assess information gain.
</ACTION ANALYSIS>

<DECISION>
   - Select the optimal action based on progress, alignment, safety, and information gain for the current step and overall goal.
</DECISION>

Your final <answer> must contain ONLY ONE of the following actions, with no additional text:
- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down
"""

    # 构建历史动作列表（如果有的话）
    history_actions = []
    if step > 0:
        # 尝试从任务数据中获取历史动作
        try:
            model_actions = load_actions(os.path.join(model_dir, "path.csv"))
            for i in range(step):
                if i < len(model_actions):
                    action_zh = model_actions[i]
                    action_en = ZH_TO_EN_ACTION_MAPPING.get(action_zh, action_zh)
                    history_actions.append(action_en)
        except:
            pass

    # 构建完整动作序列
    action_sequence = []
    try:
        model_actions = load_actions(os.path.join(model_dir, "path.csv"))
        for action_zh in model_actions:
            action_en = ZH_TO_EN_ACTION_MAPPING.get(action_zh, action_zh)
            action_sequence.append(action_en)
    except:
        pass

    # 构建Swift RLHF格式的样本
    # 修复folder_id格式为 "{original_task_id}_{attempt_id}"
    folder_id = f"{grpo_sample['original_task_id']}_{grpo_sample['attempt_id']}"

    # 简化action_reward结构，只保留total_reward值
    simplified_action_rewards = {}
    for action, reward_dict in grpo_sample["action_rewards"].items():
        if isinstance(reward_dict, dict) and "total_reward" in reward_dict:
            simplified_action_rewards[action] = reward_dict["total_reward"]
        else:
            # 向后兼容：如果已经是简化格式
            simplified_action_rewards[action] = reward_dict

    swift_sample = {
        "messages": [
            {"role": "system", "content": system_content},
            {"role": "user", "content": user_content}
        ],
        "images": images,
        "solution": ZH_TO_EN_ACTION_MAPPING.get(grpo_sample["model_action"], grpo_sample["model_action"]),
        "folder_id": folder_id,
        "current_index": grpo_sample["step"],
        "action_sequence": action_sequence,
        "action_reward": simplified_action_rewards
    }

    # 如果有历史动作，添加到样本中
    if history_actions:
        swift_sample["history_actions"] = history_actions

    return swift_sample


def build_swift_rlhf_dataset(grpo_dataset: Dict[str, Any], base_dir: str = "data") -> List[Dict[str, Any]]:
    """
    将GRPO数据集转换为Swift RLHF兼容格式

    Args:
        grpo_dataset: GRPO数据集字典
        base_dir: 数据目录

    Returns:
        Swift RLHF兼容格式的样本列表
    """
    swift_samples = []
    failed_samples = []

    print("转换GRPO样本为Swift RLHF格式...")

    for i, grpo_sample in enumerate(grpo_dataset["samples"]):
        if (i + 1) % 50 == 0:
            print(f"转换进度: {i + 1}/{len(grpo_dataset['samples'])}")

        try:
            swift_sample = build_swift_rlhf_sample(grpo_sample, base_dir)
            swift_samples.append(swift_sample)
        except Exception as e:
            print(f"转换样本失败 {grpo_sample.get('task_id', 'unknown')}: {e}")
            failed_samples.append(grpo_sample)

    print(f"成功转换 {len(swift_samples)} 个样本")
    if failed_samples:
        print(f"转换失败 {len(failed_samples)} 个样本")

    return swift_samples


def scan_all_tasks(base_dir: str = "data") -> List[Dict[str, str]]:
    """
    扫描所有可用的任务

    Args:
        base_dir: 数据目录

    Returns:
        任务信息列表，每个元素包含task_id, attempt_id, path等信息
    """
    tasks = []
    dataset_rl_dir = os.path.join(base_dir, "dataset_rl")

    if not os.path.exists(dataset_rl_dir):
        print(f"警告: 数据目录不存在: {dataset_rl_dir}")
        return tasks

    # 扫描所有数字文件夹（任务ID）
    for task_id in os.listdir(dataset_rl_dir):
        task_path = os.path.join(dataset_rl_dir, task_id)
        if not os.path.isdir(task_path) or not task_id.isdigit():
            continue

        # 扫描每个任务下的尝试ID
        for attempt_id in os.listdir(task_path):
            attempt_path = os.path.join(task_path, attempt_id)
            if not os.path.isdir(attempt_path) or not attempt_id.isdigit():
                continue

            # 检查是否有必要的文件
            path_csv = os.path.join(attempt_path, "path.csv")
            loc_csv = os.path.join(attempt_path, "loc.csv")

            if os.path.exists(path_csv) and os.path.exists(loc_csv):
                tasks.append({
                    "task_id": task_id,
                    "attempt_id": attempt_id,
                    "path": attempt_path,
                    "full_task_id": f"{task_id}_{attempt_id}"
                })

    return sorted(tasks, key=lambda x: (int(x["task_id"]), int(x["attempt_id"])))


def build_grpo_dataset_batch(base_dir: str = "data", max_positive_samples: int = 200,
                           negative_threshold: float = 0.0) -> Dict[str, Any]:
    """
    批量构建所有任务的GRPO数据集

    Args:
        base_dir: 数据目录
        max_positive_samples: 最大正样本数量
        negative_threshold: 负样本阈值

    Returns:
        合并的GRPO数据集
    """
    print("扫描所有可用任务...")
    all_tasks = scan_all_tasks(base_dir)

    if not all_tasks:
        print("未找到任何可用任务")
        return {"samples": [], "statistics": {}, "metadata": {}}

    print(f"找到 {len(all_tasks)} 个任务:")
    for task in all_tasks[:10]:  # 只显示前10个
        print(f"  - 任务 {task['full_task_id']}: {task['path']}")
    if len(all_tasks) > 10:
        print(f"  ... 还有 {len(all_tasks) - 10} 个任务")

    # 为每个任务运行导航评分系统
    print("\n开始批量评分...")
    all_samples = []
    failed_tasks = []
    task_statistics = {}

    for i, task in enumerate(all_tasks):
        print(f"\n处理任务 {i+1}/{len(all_tasks)}: {task['full_task_id']}")

        try:
            # 导入导航评分系统的核心函数
            from navigation_scoring_system import (
                load_coordinates, load_actions,
                calculate_endpoint_score, calculate_trajectory_score, calculate_efficiency_score
            )

            # 加载任务数据
            model_coords = load_coordinates(os.path.join(task["path"], "loc.csv"))
            model_actions = load_actions(os.path.join(task["path"], "path.csv"))

            # 加载参考数据
            reference_dir = os.path.join(base_dir, "dataset_video_instruction", task["task_id"])
            reference_coords = load_coordinates(os.path.join(reference_dir, "loc.csv"))

            # 读取实际的导航目标
            navigation_target = load_navigation_target(task["task_id"], base_dir)
            if not navigation_target:
                print(f"    跳过: 无法读取导航目标")
                failed_tasks.append(task["full_task_id"])
                task_statistics[task["full_task_id"]] = {"success": False, "error": "无法读取导航目标"}
                continue

            if not model_coords or not model_actions or not reference_coords:
                raise ValueError("数据文件不完整")

            # 计算整体评分
            final_loc = reference_coords[-1]
            endpoint_scores = calculate_endpoint_score(model_coords, model_actions, final_loc)
            trajectory_scores = calculate_trajectory_score(model_coords, model_actions, reference_coords)
            efficiency_score = calculate_efficiency_score(model_coords, reference_coords)

            # 计算每一步的评分
            step_details = []
            for step in range(len(model_actions)):
                action = model_actions[step]

                # 获取该步骤的评分
                endpoint_score = endpoint_scores[step] if step < len(endpoint_scores) else 0.0
                trajectory_score = trajectory_scores[step] if step < len(trajectory_scores) else 0.0
                # 效率评分平摊到每个步骤
                efficiency_contribution = efficiency_score / len(model_actions) if len(model_actions) > 0 else 0.0

                total_score = endpoint_score + trajectory_score + efficiency_contribution

                step_details.append({
                    "step": step,
                    "action_id": str(6),  # 简化处理
                    "action_name": action,
                    "endpoint_score": endpoint_score,
                    "trajectory_score": trajectory_score,
                    "efficiency_score": efficiency_contribution,
                    "total_score": total_score
                })

            # 构建任务评分结果
            task_scores = {
                "task_id": task["full_task_id"],
                "original_task_id": task["task_id"],
                "attempt_id": task["attempt_id"],
                "step_details": step_details,
                "final_endpoint_score": step_details[-1]["endpoint_score"] if step_details else 0,
                "final_trajectory_score": step_details[-1]["trajectory_score"] if step_details else 0,
                "final_efficiency_score": step_details[-1]["efficiency_score"] if step_details else 0,
                "total_score": step_details[-1]["total_score"] if step_details else 0
            }

            if task_scores and "step_details" in task_scores:
                # 提取样本
                negative_samples, positive_samples = extract_samples_from_scores(
                    {task["full_task_id"]: task_scores},
                    negative_threshold
                )

                # 限制正样本数量（按任务平均分配）
                max_positive_per_task = max(1, max_positive_samples // len(all_tasks))
                if len(positive_samples) > max_positive_per_task:
                    positive_samples = random.sample(positive_samples, max_positive_per_task)

                # 构建GRPO样本
                task_samples = []
                for sample in negative_samples + positive_samples:
                    try:
                        grpo_sample = build_grpo_sample(
                            sample,
                            model_coords,
                            model_actions,
                            reference_coords,
                            navigation_target
                        )
                        if grpo_sample:
                            task_samples.append(grpo_sample)
                    except Exception as e:
                        print(f"    构建样本失败: {e}")
                        continue

                all_samples.extend(task_samples)
                task_statistics[task["full_task_id"]] = {
                    "negative_samples": len(negative_samples),
                    "positive_samples": len(positive_samples),
                    "total_samples": len(task_samples),
                    "success": True
                }

                print(f"    成功: {len(task_samples)} 个样本 (负样本: {len(negative_samples)}, 正样本: {len(positive_samples)})")
            else:
                failed_tasks.append(task["full_task_id"])
                task_statistics[task["full_task_id"]] = {"success": False, "error": "评分失败"}
                print(f"    失败: 评分结果为空")

        except Exception as e:
            failed_tasks.append(task["full_task_id"])
            task_statistics[task["full_task_id"]] = {"success": False, "error": str(e)}
            print(f"    失败: {e}")
            continue

    # 构建最终数据集
    print(f"\n批量处理完成:")
    print(f"  成功处理: {len(all_tasks) - len(failed_tasks)} 个任务")
    print(f"  失败任务: {len(failed_tasks)} 个")
    print(f"  总样本数: {len(all_samples)}")

    # 统计信息
    total_negative = sum(1 for sample in all_samples if sample["sample_type"] == "negative")
    total_positive = sum(1 for sample in all_samples if sample["sample_type"] == "positive")

    # 动作频率统计
    action_frequency = {}
    for sample in all_samples:
        action = sample.get("model_action", "未知动作")
        action_frequency[action] = action_frequency.get(action, 0) + 1

    statistics = {
        "total_samples": len(all_samples),
        "negative_samples": total_negative,
        "positive_samples": total_positive,
        "failed_samples": 0,
        "task_count": len(all_tasks) - len(failed_tasks),
        "failed_task_count": len(failed_tasks),
        "task_distribution": task_statistics,
        "action_frequency": action_frequency
    }

    metadata = {
        "negative_threshold": negative_threshold,
        "max_positive_samples": max_positive_samples,
        "base_dir": base_dir,
        "batch_processing": True,
        "processed_tasks": len(all_tasks),
        "failed_tasks": failed_tasks
    }

    return {
        "samples": all_samples,
        "statistics": statistics,
        "metadata": metadata
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="GRPO训练数据集构建器")
    parser.add_argument("--scores_file", help="评分结果文件路径（单任务模式必需）")
    parser.add_argument("--output_file", default="grpo_dataset.json", help="输出数据集文件路径")
    parser.add_argument("--statistics_file", default="grpo_statistics.json", help="统计报告文件路径")
    parser.add_argument("--grpo_output_file_swift", help="Swift RLHF兼容格式的输出文件路径（JSONL格式）")
    parser.add_argument("--base_dir", default="data", help="数据目录")
    parser.add_argument("--max_positive_samples", type=int, default=200, help="最大正样本数量")
    parser.add_argument("--negative_threshold", type=float, default=0.0, help="负样本阈值")
    parser.add_argument("--seed", type=int, default=42, help="随机种子")

    # 批量处理选项
    parser.add_argument("--batch_mode", action="store_true", help="启用批量处理模式，自动处理所有任务")
    parser.add_argument("--max_tasks", type=int, help="批量模式下最大处理任务数量（用于测试）")

    args = parser.parse_args()

    # 验证参数
    if not args.batch_mode and not args.scores_file:
        parser.error("单任务模式需要指定 --scores_file 参数，或使用 --batch_mode 启用批量处理")

    if args.batch_mode and args.scores_file:
        print("警告: 批量模式下将忽略 --scores_file 参数")

    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)

    print("=" * 60)
    print("GRPO训练数据集构建器")
    print("=" * 60)
    print(f"运行模式: {'批量处理' if args.batch_mode else '单任务'}")
    if not args.batch_mode:
        print(f"评分文件: {args.scores_file}")
    print(f"输出文件: {args.output_file}")
    print(f"统计文件: {args.statistics_file}")
    if args.grpo_output_file_swift:
        print(f"Swift RLHF输出文件: {args.grpo_output_file_swift}")
    print(f"数据目录: {args.base_dir}")
    print(f"最大正样本数: {args.max_positive_samples}")
    print(f"负样本阈值: {args.negative_threshold}")
    print(f"随机种子: {args.seed}")
    if args.batch_mode and args.max_tasks:
        print(f"最大处理任务数: {args.max_tasks}")
    print("=" * 60)

    # 构建GRPO数据集
    try:
        if args.batch_mode:
            # 批量处理模式
            dataset = build_grpo_dataset_batch(
                args.base_dir,
                args.max_positive_samples,
                args.negative_threshold
            )

            # 如果指定了最大任务数，截取样本
            if args.max_tasks and dataset["samples"]:
                # 按任务分组并限制数量
                task_samples = {}
                for sample in dataset["samples"]:
                    task_id = sample["original_task_id"]
                    if task_id not in task_samples:
                        task_samples[task_id] = []
                    task_samples[task_id].append(sample)

                # 只保留前max_tasks个任务的样本
                limited_samples = []
                for i, (task_id, samples) in enumerate(sorted(task_samples.items())):
                    if i >= args.max_tasks:
                        break
                    limited_samples.extend(samples)

                dataset["samples"] = limited_samples
                dataset["statistics"]["total_samples"] = len(limited_samples)
                print(f"限制为前 {args.max_tasks} 个任务，共 {len(limited_samples)} 个样本")
        else:
            # 单任务模式
            dataset = build_grpo_dataset(
                args.scores_file,
                args.base_dir,
                args.max_positive_samples,
                args.negative_threshold
            )

        # 保存数据集
        print(f"\n保存数据集到: {args.output_file}")
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)

        # 生成统计报告
        print("生成统计报告...")
        report = generate_statistics_report(dataset)

        # 保存统计报告
        print(f"保存统计报告到: {args.statistics_file}")
        with open(args.statistics_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 打印摘要
        print("\n" + "=" * 60)
        print("数据集构建完成")
        print("=" * 60)
        summary = report["dataset_summary"]
        print(f"总样本数: {summary['total_samples']}")
        print(f"负样本数: {summary['negative_samples']} ({summary['negative_ratio']:.2%})")
        print(f"正样本数: {summary['positive_samples']} ({1-summary['negative_ratio']:.2%})")
        print(f"失败样本数: {summary['failed_samples']}")
        print(f"涉及任务数: {summary['task_count']}")

        print(f"\n动作频率分布:")
        action_freq = report["action_frequency"]
        for action, count in sorted(action_freq.items(), key=lambda x: x[1], reverse=True):
            print(f"  {action}: {count}")

        print(f"\n奖励值分布:")
        reward_dist = report["reward_distribution"]
        for reward_type, stats in reward_dist.items():
            print(f"  {reward_type}: 均值={stats['mean']:.3f}, 标准差={stats['std']:.3f}, 范围=[{stats['min']:.3f}, {stats['max']:.3f}]")

        print(f"\n贡献最多的任务 (前5个):")
        for task in report["task_contributions"][:5]:
            print(f"  任务{task['task_id']}: {task['total_samples']}个样本 (负样本{task['negative_samples']}, 正样本{task['positive_samples']})")

        # 如果指定了Swift RLHF输出文件，生成Swift RLHF兼容格式
        if args.grpo_output_file_swift:
            print("\n" + "=" * 60)
            print("生成Swift RLHF兼容格式数据集")
            print("=" * 60)

            try:
                swift_samples = build_swift_rlhf_dataset(dataset, args.base_dir)

                # 保存为JSONL格式
                print(f"保存Swift RLHF数据集到: {args.grpo_output_file_swift}")
                with open(args.grpo_output_file_swift, 'w', encoding='utf-8') as f:
                    for sample in swift_samples:
                        f.write(json.dumps(sample, ensure_ascii=False) + '\n')

                print(f"Swift RLHF数据集已保存，包含 {len(swift_samples)} 个样本")

                # 验证数据格式
                print("验证Swift RLHF数据格式...")
                validation_errors = []

                for i, sample in enumerate(swift_samples[:5]):  # 验证前5个样本
                    # 检查必需字段
                    required_fields = ["messages", "images", "solution", "folder_id", "current_index", "action_sequence", "action_reward"]
                    for field in required_fields:
                        if field not in sample:
                            validation_errors.append(f"样本 {i}: 缺少字段 '{field}'")

                    # 检查messages结构
                    if "messages" in sample:
                        if len(sample["messages"]) != 2:
                            validation_errors.append(f"样本 {i}: messages应包含2个元素")
                        else:
                            if sample["messages"][0]["role"] != "system":
                                validation_errors.append(f"样本 {i}: 第一个message的role应为'system'")
                            if sample["messages"][1]["role"] != "user":
                                validation_errors.append(f"样本 {i}: 第二个message的role应为'user'")

                    # 检查action_reward结构
                    if "action_reward" in sample:
                        expected_actions = ["Move forward", "Move backward", "Move left", "Move right",
                                          "Move up", "Move down", "Rotate left", "Rotate right",
                                          "Look up", "Look down"]
                        for action in expected_actions:
                            if action not in sample["action_reward"]:
                                validation_errors.append(f"样本 {i}: action_reward缺少动作 '{action}'")

                if validation_errors:
                    print("数据格式验证发现问题:")
                    for error in validation_errors[:10]:  # 只显示前10个错误
                        print(f"  - {error}")
                    if len(validation_errors) > 10:
                        print(f"  ... 还有 {len(validation_errors) - 10} 个错误")
                else:
                    print("数据格式验证通过！")

            except Exception as e:
                print(f"生成Swift RLHF数据集时发生错误: {e}")
                import traceback
                traceback.print_exc()

        print("=" * 60)

    except Exception as e:
        print(f"构建数据集时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
