#!/usr/bin/env python3
"""
修复数据集中的图片路径问题
将相对路径 "data/dataset_video_instruction/..." 修改为绝对路径 "/home/<USER>/wzy/NIPS/data/dataset_video_instruction/..."
"""

import json
import os
import shutil
from pathlib import Path

def fix_dataset_paths():
    """修复数据集中的路径问题"""

    # 输入和输出文件路径
    input_file = "/home/<USER>/wzy/NIPS/data/grpo_dataset_all_swift.jsonl"
    backup_file = "/home/<USER>/wzy/NIPS/data/grpo_dataset_all_swift.jsonl.backup"

    # 创建备份
    print(f"创建备份文件: {backup_file}")
    shutil.copy2(input_file, backup_file)

    # 读取并修复数据
    fixed_lines = []
    total_lines = 0
    fixed_count = 0

    print("开始修复路径...")

    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            total_lines += 1
            try:
                data = json.loads(line.strip())

                # 检查是否有images字段
                if 'images' in data:
                    original_images = data['images'][:]
                    modified = False

                    # 修复每个图片路径
                    for i, img_path in enumerate(data['images']):
                        # 检查是否是相对路径（不以 / 开头）
                        if not img_path.startswith('/') and 'dataset_video_instruction' in img_path:
                            # 将相对路径转换为绝对路径
                            new_path = f"/home/<USER>/wzy/NIPS/{img_path}"
                            data['images'][i] = new_path
                            modified = True
                            print(f"第{line_num}行: {img_path} -> {new_path}")

                    if modified:
                        fixed_count += 1

                # 将修复后的数据添加到列表
                fixed_lines.append(json.dumps(data, ensure_ascii=False))

            except json.JSONDecodeError as e:
                print(f"第{line_num}行JSON解析错误: {e}")
                # 保留原始行
                fixed_lines.append(line.strip())

            if line_num % 1000 == 0:
                print(f"已处理 {line_num} 行...")

    # 写入修复后的数据
    print(f"写入修复后的数据到 {input_file}")
    with open(input_file, 'w', encoding='utf-8') as f:
        for line in fixed_lines:
            f.write(line + '\n')

    print(f"修复完成!")
    print(f"总行数: {total_lines}")
    print(f"修复的条目数: {fixed_count}")
    print(f"备份文件: {backup_file}")

if __name__ == "__main__":
    fix_dataset_paths()
