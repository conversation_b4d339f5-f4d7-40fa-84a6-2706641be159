import re
import json
import random
import time
import os
from typing import List, Dict, Optional
from datetime import datetime
from openai import OpenAI
from swift.plugin import ORM, orms

class APIPool:
    """
    API池类，用于管理多个API key，实现负载均衡和容错机制。
    当某个API key出现问题（如余额不足）时进行标记，
    如果出现指定次数的问题，则在API日志中标记并在后续不再使用该API key。
    """
    
    def __init__(self, api_keys: List[str], max_error_count: int = 10, log_dir: str = None, retry_interval_minutes: int = 30):
        """
        初始化API池
        
        Args:
            api_keys: API key列表
            max_error_count: API key允许的最大错误次数，超过此次数将被标记为不可用
            log_dir: 日志目录，默认为当前目录下的api_logs
            retry_interval_minutes: API重启检查间隔（分钟），默认30分钟
        """
        self.api_keys = api_keys
        self.max_error_count = max_error_count
        self.error_counts: Dict[str, int] = {key: 0 for key in api_keys}
        self.available_keys = set(api_keys)
        self.unavailable_timestamps: Dict[str, float] = {}  # 记录API key被标记为不可用的时间戳
        self.retry_interval_seconds = retry_interval_minutes * 60  # 转换为秒
        
        # 设置日志目录
        if log_dir is None:
            self.log_dir = os.path.join(os.getcwd(), 'api_logs')
        else:
            self.log_dir = log_dir
            
        # 创建日志目录（如果不存在）
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 日志文件路径
        self.log_file = os.path.join(self.log_dir, f'api_pool_{datetime.now().strftime("%Y%m%d")}.log')
        
        # 初始化日志
        self._log(f"API池初始化，共{len(api_keys)}个API key")
        for key in api_keys:
            self._log(f"添加API key: {self._mask_api_key(key)}")
    
    def _mask_api_key(self, api_key: str) -> str:
        """
        遮蔽API key，只显示前4位和后4位
        
        Args:
            api_key: 原始API key
            
        Returns:
            遮蔽后的API key
        """
        if len(api_key) <= 8:
            return api_key
        return f"{api_key[:4]}...{api_key[-4:]}"
    
    def _log(self, message: str, is_error: bool = False):
        """
        记录日志
        
        Args:
            message: 日志消息
            is_error: 是否为错误日志，只有错误日志才会记录
        """
        # 只有错误日志才记录
        if is_error:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f"[{timestamp}] {message}"
            
            # 打印到控制台
            print(log_message)
            
            # 写入日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
    
    def get_api_key(self) -> Optional[str]:
        """
        获取一个可用的API key，并检查不可用的API key是否可以重新启用
        
        Returns:
            可用的API key，如果没有可用的API key则返回None
        """
        # 检查是否有不可用的API key可以重新启用
        current_time = time.time()
        keys_to_check = []
        
        # 收集需要重新检查的API key
        for api_key, timestamp in list(self.unavailable_timestamps.items()):
            if current_time - timestamp >= self.retry_interval_seconds:
                keys_to_check.append(api_key)
                
        # 重新检查这些API key
        for api_key in keys_to_check:
            self._log(f"重新检查API key: {self._mask_api_key(api_key)}，已经过了{self.retry_interval_seconds//60}分钟")
            # 重置错误计数并将API key添加回可用列表
            self.reset_error_count(api_key)
            # 从不可用时间戳字典中移除
            self.unavailable_timestamps.pop(api_key, None)
            
        if not self.available_keys:
            self._log("警告：没有可用的API key", is_error=True)
            return None
        
        # 随机选择一个可用的API key
        api_key = random.choice(list(self.available_keys))
        # self._log(f"使用API key: {self._mask_api_key(api_key)}")
        return api_key
    
    def mark_error(self, api_key: str, error_message: str):
        """
        标记API key出现错误
        
        Args:
            api_key: 出现错误的API key
            error_message: 错误信息
        """
        if api_key not in self.api_keys:
            self._log(f"警告：尝试标记未知的API key: {self._mask_api_key(api_key)}", is_error=True)
            return
        
        # 增加错误计数
        self.error_counts[api_key] += 1
        current_count = self.error_counts[api_key]
        
        self._log(f"API key {self._mask_api_key(api_key)} 出现错误 ({current_count}/{self.max_error_count}): {error_message}", is_error=True)
        
        # 如果错误次数超过阈值，标记为不可用
        if current_count >= self.max_error_count and api_key in self.available_keys:
            self.available_keys.remove(api_key)
            # 记录API key被标记为不可用的时间戳
            self.unavailable_timestamps[api_key] = time.time()
            self._log(f"API key {self._mask_api_key(api_key)} 已被标记为不可用，已达到最大错误次数 {self.max_error_count}，将在{self.retry_interval_seconds//60}分钟后重新检查", is_error=True)
    
    def reset_error_count(self, api_key: str):
        """
        重置API key的错误计数
        
        Args:
            api_key: 要重置的API key
        """
        if api_key not in self.api_keys:
            self._log(f"警告：尝试重置未知的API key: {self._mask_api_key(api_key)}", is_error=True)
            return
        
        # 重置错误计数
        self.error_counts[api_key] = 0
        
        # 如果API key不在可用列表中，添加回来
        if api_key not in self.available_keys:
            self.available_keys.add(api_key)
            # self._log(f"API key {self._mask_api_key(api_key)} 已被重新标记为可用")
    
    def get_status(self) -> Dict:
        """
        获取API池状态
        
        Returns:
            包含API池状态信息的字典
        """
        current_time = time.time()
        unavailable_info = {}
        
        for api_key, timestamp in self.unavailable_timestamps.items():
            elapsed_seconds = current_time - timestamp
            remaining_seconds = max(0, self.retry_interval_seconds - elapsed_seconds)
            unavailable_info[self._mask_api_key(api_key)] = {
                "elapsed_minutes": round(elapsed_seconds / 60, 1),
                "remaining_minutes": round(remaining_seconds / 60, 1)
            }
            
        return {
            "total_keys": len(self.api_keys),
            "available_keys": len(self.available_keys),
            "unavailable_keys": len(self.api_keys) - len(self.available_keys),
            "error_counts": {self._mask_api_key(k): v for k, v in self.error_counts.items()},
            "unavailable_info": unavailable_info
        }


class ConsistencyReward(ORM):
    """
    奖励函数，用于评估模型的回答与训练模型的回答是否一致。
    
    从content字段中提取最后一个Question后面的内容和<think></think>标签内的内容，
    让模型根据Question和think过程给出选项，然后比较模型的选项和训练模型的选项是否一致。
    如果一致返回1，不一致返回0。
    """
    
    # 定义类常量用于识别答案
    BOXED_PATTERN = r"\$\\boxed\{([A-H])\}\$"
    ANSWER_PATTERN = r"answer\s+([A-H])\.?"  # answer pattern
    SIMPLE_DOT_PATTERN = r"(?:^|[^A-Za-z])([A-H])\s*\."  # 带点的pattern
    SIMPLE_PATTERN = r"(?:^|[^A-Za-z])([A-H])(?:$|[^A-Za-z])"  # 不带点的pattern
    VALID_OPTIONS = set('ABCDEFGH')
    
    def __init__(self, api_keys=None):
        """
        初始化奖励函数
        
        Args:
            api_keys: 阿里百炼平台API key列表，用于调用外部模型进行推理
        """
        # 默认API key列表
        default_api_keys = [
            'sk-24d1eb151364436098ac65bc9f460836',
            'sk-36a1faa2779146b2b770c9a6a34b48e3',
            'sk-bcfc3d84bc77478280a328fb399ab935',
            'sk-5a735c69f14641f9902ce7f47a40822e',
            'sk-7bab4af376654da2a1aad0d9253bd604'
        ]
        
        # 使用提供的API key列表或默认列表
        if api_keys is None:
            api_keys = default_api_keys
        elif isinstance(api_keys, str):
            api_keys = [api_keys]  # 如果提供单个API key，转换为列表
            
        # 创建API池
        self.api_pool = APIPool(api_keys, max_error_count=10)
        
        # 阿里百炼平台API基础URL
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    @staticmethod
    def extract_question(text):
        """从文本中提取问题内容，支持多种格式"""
        if not text:
            return ""
            
        # 首先尝试找到"Question:"模式
        if "Question:" in text:
            question_parts = text.split("Question:")
            return "Question:" + question_parts[-1].strip()
            
        # 尝试找到包含选项的模式（A. B. C. D.格式）
        options_pattern = r"(.*?)\s*([A-D]\s*\.\s*.*?)\s*([A-D]\s*\.\s*.*?)\s*([A-D]\s*\.\s*.*?)\s*([A-D]\s*\.\s*.*?)"
        match = re.search(options_pattern, text, re.DOTALL)
        if match:
            return match.group(0).strip()
            
        # 尝试找到"Choices:"模式
        if "Choices:" in text:
            # 提取包含问题和选项的完整内容
            return text.strip()
            
        # 如果是messages格式，可能包含视频描述和问题
        # 尝试提取整个内容作为问题
        return text.strip()
    
    @staticmethod
    def extract_think_content(text):
        """从文本中提取<think></think>标签内的内容"""
        if not text:
            return ""
        think_pattern = r"<think>(.*?)</think>"
        match = re.search(think_pattern, text, re.DOTALL)
        if match:
            return match.group(1).strip()
        return ""
    
    def normalize_answer(self, answer: str) -> str:
        """标准化答案格式，从文本中提取答案"""
        answer = answer.strip()
        
        # 首先尝试找到标准格式的答案（$\boxed{X}$）
        boxed_matches = list(re.finditer(self.BOXED_PATTERN, answer, re.IGNORECASE))
        if boxed_matches:
            # 使用最后一个匹配的答案
            return boxed_matches[-1].group(1).upper()
            
        # 其次寻找answer pattern
        answer_matches = list(re.finditer(self.ANSWER_PATTERN, answer, re.IGNORECASE))
        if answer_matches:
            # 使用第一个匹配的答案
            return answer_matches[0].group(1).upper()
            
        # 最后寻找单个字母
        # 先找带点的
        dot_matches = list(re.finditer(self.SIMPLE_DOT_PATTERN, answer, re.IGNORECASE))
        if dot_matches:
            # 使用最后一个带点的匹配
            return dot_matches[-1].group(1).upper()
            
        # 最后找不带点的
        simple_matches = list(re.finditer(self.SIMPLE_PATTERN, answer, re.IGNORECASE))
        if simple_matches:
            # 使用最后一个不带点的匹配
            return simple_matches[-1].group(1).upper()
            
        # 从<answer>标签中提取
        answer_tag_pattern = r"<answer>(.*?)</answer>"
        match = re.search(answer_tag_pattern, answer, re.DOTALL)
        if match:
            answer_text = match.group(1).strip()
            # 递归调用自身处理提取出的内容
            return self.normalize_answer(answer_text)
            
        # 如果都没找到，返回原始文本（会在后续比较中返回0分）
        return answer.upper()
    
    def call_api_for_inference(self, question, think_content):
        """调用阿里百炼API进行推理"""
        if not question or not think_content:
            print("问题内容或思考过程为空，无法调用API")
            return ""
        
        # 最大重试次数
        max_retries = min(3, len(self.api_pool.available_keys))
        retry_count = 0
        
        while retry_count < max_retries:
            # 从API池获取API key
            api_key = self.api_pool.get_api_key()
            if api_key is None:
                print("没有可用的API key，无法调用API")
                return ""
            
            try:
                prompt = f"""这是一个单选题，请根据以下问题和思考过程，选择一个最合适的选项（A-H中的一个字母）。

你只需要回答一个字母，不要有任何解释或其他内容。

问题：{question}

思考过程：{think_content}

请只回答一个字母（A-H）："""
                
                # 创建OpenAI客户端
                client = OpenAI(
                    api_key=api_key,
                    base_url=self.base_url,
                )
                
                # 调用API
                completion = client.chat.completions.create(
                    model="qwen2.5-vl-3b-instruct",  # 使用阿里百炼平台的模型
                    messages=[
                        {'role': 'system', 'content': '你是一个专业的问题解答助手。'},
                        {'role': 'user', 'content': prompt}
                    ],
                    temperature=0.1,
                    max_tokens=10
                )
                
                # 提取答案
                api_answer = completion.choices[0].message.content.strip()
                normalized_answer = self.normalize_answer(api_answer)
                # print(f"API推理结果: 原始={api_answer}, 标准化后={normalized_answer}")
                return normalized_answer
                
            except Exception as e:
                error_message = str(e)
                # 标记API key出现错误
                self.api_pool.mark_error(api_key, error_message)
                
                # 检查错误类型，判断是否需要重试
                if "余额不足" in error_message or "insufficient_quota" in error_message:
                    print(f"API key余额不足: {error_message}")
                    # 继续尝试下一个API key
                    retry_count += 1
                    continue
                elif "rate_limit_exceeded" in error_message:
                    print(f"API调用频率限制: {error_message}")
                    # 等待一段时间后重试
                    time.sleep(2)
                    retry_count += 1
                    continue
                else:
                    print(f"API调用失败: {error_message}")
                    # 其他错误，也尝试重试
                    retry_count += 1
                    continue
        
        print(f"已尝试{max_retries}次API调用，均失败")
        return ""
    
    def __call__(self, completions, solution=None, **kwargs) -> List[float]:
        """
        评估模型回答与训练模型回答的一致性
        
        Args:
            completions: 模型生成的回答列表
            solution: 正确答案列表，用于预先判断模型答案是否正确
            kwargs: 其他参数，包括数据集中的其他字段
                - content: 包含思考过程的字段
                - question: 问题文本
                - query: 可能包含问题的字段
                - prompt: 可能包含问题的字段
                - input: 可能包含问题的字段
        
        Returns:
            一致性奖励列表，一致为1.0，不一致为0.0
        """
        rewards = []
        
        # 确保 completions 和 solution 都是列表
        if not isinstance(completions, list):
            completions = [completions]
            
        if solution is not None and not isinstance(solution, list):
            solution = [solution] * len(completions)
        
        # 打印所有可用的字段，帮助调试
        # print(f"可用字段: {list(kwargs.keys())}")
        
        # 从'messages'字段中提取问题内容
        question_field = None
        
        # 如果有messages字段，从中提取用户问题
        if 'messages' in kwargs and kwargs['messages'] is not None:
            messages = kwargs['messages']
            # print(f"找到messages字段，尝试提取问题内容")
            
            # 如果messages是列表的列表（批处理情况）
            if isinstance(messages, list) and all(isinstance(item, list) for item in messages):
                question_field = []
                for msg_list in messages:
                    # 找到用户消息
                    user_content = ""
                    for msg in msg_list:
                        if isinstance(msg, dict) and msg.get('role') == 'user':
                            user_content = msg.get('content', "")
                    question_field.append(user_content)
                # print(f"从消息列表中提取了 {len(question_field)} 个用户问题")
            
            # 如果messages是单个列表
            elif isinstance(messages, list):
                # 找到用户消息
                user_content = ""
                for msg in messages:
                    if isinstance(msg, dict) and msg.get('role') == 'user':
                        user_content = msg.get('content', "")
                question_field = user_content
                # print(f"从消息列表中提取了用户问题")
        
        # 如果还是没有找到，尝试其他字段
        if question_field is None:
            question_field_names = ['question', 'query', 'prompt', 'input', 'text']
            for field_name in question_field_names:
                if field_name in kwargs and kwargs[field_name] is not None:
                    question_field = kwargs[field_name]
                    # print(f"使用字段 '{field_name}' 作为问题内容")
                    break
        
        # 直接从completions中提取思考过程和答案
        for i, completion in enumerate(completions):
            try:
                # 从模型回答中提取思考过程
                think_content = self.extract_think_content(completion)
                if not think_content:
                    # print(f"无法从样本 {i} 的模型回答中提取思考过程")
                    rewards.append(0.0)
                    continue
                
                # 标准化模型答案
                normalized_completion = self.normalize_answer(completion)
                
                # 如果提供了正确答案，先检查模型答案是否正确
                if solution is not None:
                    # 确保索引在范围内
                    sol = solution[i] if i < len(solution) else solution[-1]
                    normalized_solution = self.normalize_answer(sol)
                    
                    # 如果模型答案不正确，直接返回0.0，不调用API
                    if normalized_completion != normalized_solution:
                        # print(f"模型答案={normalized_completion}，正确答案={normalized_solution}，不正确，跳过API调用")
                        rewards.append(0.0)
                        continue
                
                # 获取问题内容
                if question_field is not None:
                    # 确保问题可访问
                    if isinstance(question_field, list) and i < len(question_field):
                        ques = question_field[i]
                    elif isinstance(question_field, str):
                        ques = question_field
                    else:
                        print(f"无法获取样本 {i} 的问题")
                        rewards.append(0.0)
                        continue
                    
                    # 打印问题内容的一部分，帮助调试
                    # if ques:
                    #     print(f"问题内容前100个字符: {ques[:100]}...")
                    # else:
                    #     print("问题内容为空")
                    
                    # 如果问题内容为空，尝试从completions中提取
                    if not ques:
                        print("问题内容为空，尝试从completions中提取")
                        question_content = self.extract_question(completion)
                    else:
                        # 提取问题内容
                        question_content = self.extract_question(ques)
                    
                    if not question_content:
                        print(f"无法从样本 {i} 的问题中提取内容")
                        rewards.append(0.0)
                        continue
                else:
                    # 如果没有找到问题字段，尝试直接使用completions
                    print(f"没有找到问题字段，尝试直接使用completions")
                    # 假设模型回答中包含问题内容
                    question_content = completion
                    if not question_content:
                        print(f"无法从completions中提取问题内容")
                        rewards.append(0.0)
                        continue
                
                # 打印完整的问题内容，供用户查看
                # print(f"\n提取的完整问题内容:\n{question_content}\n")
                
                # 调用API进行推理
                api_answer = self.call_api_for_inference(question_content, think_content)
                
                # 如果API返回了有效答案，将其与模型答案进行比较
                if api_answer and api_answer in self.VALID_OPTIONS:
                    # 比较API的推理结果与模型答案是否一致
                    if api_answer == normalized_completion:
                        # print(f"一致性检查: API推理结果={api_answer}, 模型答案={normalized_completion}, 一致")
                        rewards.append(1.0)
                    else:
                        # print(f"一致性检查: API推理结果={api_answer}, 模型答案={normalized_completion}, 不一致")
                        rewards.append(0.0)
                else:
                    # API未返回有效答案，默认为不一致
                    print(f"API未返回有效答案，模型答案={normalized_completion}，默认为不一致")
                    rewards.append(0.0)
            except Exception as e:
                print(f"处理样本 {i} 时发生错误: {e}")
                rewards.append(0.0)  # 发生错误时返回0分
        
        return rewards

# 注册奖励函数
orms['consistency'] = ConsistencyReward
