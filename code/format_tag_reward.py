"""
格式标签奖励函数

该模块提供了一个评估模型回答是否符合特定格式的奖励函数。
适用于评估模型回答是否包含指定标签结构的场景。

使用方法:
1. 在Swift框架中作为奖励函数使用:
   ```python
   from format_tag_reward import FormatTagScore

   # 创建实例
   format_score = FormatTagScore()

   # 计算奖励
   rewards = format_score(model_outputs)
   ```

2. 在训练脚本中指定为外部插件:
   ```bash
   swift rlhf \
   --rlhf_type grpo \
   --external_plugins /path/to/format_tag_reward.py \
   # 其他参数...
   ```

"""

import re
from typing import List, Dict, Any, Optional, Union

from swift.plugin.orm import ORM

# 注册奖励函数的全局字典
orms = {}


class FormatTagScore(ORM):
    """
    格式标签奖励函数，用于评估模型回答是否符合特定格式结构。
    
    评分规则:
    1. 如果包含<think>...</think><answer>...</answer>格式，获得0.5分作为基础分
    2. 在<think>标签内每包含一个指定的子标签，额外获得0.1分
    3. 满分为1.0分
    
    支持的子标签:
    - <HISTORICAL CONTEXT>...</HISTORICAL CONTEXT>
    - <SPATIAL AND GOAL ANALYSIS>...</SPATIAL AND GOAL ANALYSIS>
    - <CURRENT STEP IDENTIFICATION>...</CURRENT STEP IDENTIFICATION>
    - <ACTION ANALYSIS>...</ACTION ANALYSIS>
    - <DECISION>...</DECISION>
    """
    
    # 基本格式模式，用于检测<think>和<answer>标签
    BASE_FORMAT_PATTERN = r'<think>.*?</think>\s*<answer>.*?</answer>'
    
    # 子标签模式，用于检测各个子标签
    SUBTAG_PATTERNS = {
        "HISTORICAL CONTEXT": r'<HISTORICAL CONTEXT>.*?</HISTORICAL CONTEXT>',
        "SPATIAL AND GOAL ANALYSIS": r'<SPATIAL AND GOAL ANALYSIS>.*?</SPATIAL AND GOAL ANALYSIS>',
        "CURRENT STEP IDENTIFICATION": r'<CURRENT STEP IDENTIFICATION>.*?</CURRENT STEP IDENTIFICATION>',
        "ACTION ANALYSIS": r'<ACTION ANALYSIS>.*?</ACTION ANALYSIS>',
        "DECISION": r'<DECISION>.*?</DECISION>'
    }
    
    def __init__(self):
        """
        初始化格式标签奖励函数
        """
        pass

    def __call__(self, completions, **kwargs) -> List[float]:
        """
        评估模型回答并返回格式评分
        
        Args:
            completions: 模型生成的回答列表
            kwargs: 其他参数
        
        Returns:
            奖励列表
        """
        rewards = []
        debug = kwargs.get('debug', False)  # 是否打印调试信息
        
        # 确保 completions 是列表
        if not isinstance(completions, list):
            completions = [completions]
            
        for content in completions:
            try:
                # 确保内容是字符串
                if not isinstance(content, str):
                    content = str(content)
                
                # 初始化分数
                score = 0.0
                
                # 检查基本格式 (<think>...</think><answer>...</answer>)
                base_format_match = re.search(self.BASE_FORMAT_PATTERN, content, re.DOTALL)
                if base_format_match:
                    # 基本格式匹配，得0.5分
                    score += 0.5
                    
                    # 提取<think>标签内容
                    think_content = re.search(r'<think>(.*?)</think>', content, re.DOTALL)
                    if think_content:
                        think_text = think_content.group(1)
                        
                        # 检查每个子标签
                        for tag_name, pattern in self.SUBTAG_PATTERNS.items():
                            if re.search(pattern, think_text, re.DOTALL):
                                # 每个子标签加0.1分
                                score += 0.1
                                if debug:
                                    print(f"找到子标签: {tag_name}")
                else:
                    # 没有基本格式，得0分
                    score = 0.0
                
                # 确保分数不超过1.0
                score = min(score, 1.0)
                
                if debug:
                    print(f"格式评分: {score}")
                    print("-" * 50)
                
            except Exception as e:
                print(f"评估格式时发生错误: {e}")
                import traceback
                traceback.print_exc()
                score = 0.0  # 发生错误时返回0分
                
            rewards.append(score)
            
        return rewards


# 注册奖励函数
orms['format_tag_score'] = FormatTagScore

# 为了兼容性，也注册到swift的orms中
try:
    from swift.plugin.orm import orms as swift_orms
    swift_orms['format_tag_score'] = FormatTagScore
except ImportError:
    pass 