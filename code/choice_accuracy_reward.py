import os
import re
import warnings
from typing import Dict, List, Union, Optional

from swift.plugin.orm import ORM

# 注册奖励函数的全局字典
orms = {}


class ChoiceAccuracy(ORM):
    """
    选择题准确性奖励函数，用于评估模型的选项回答是否与正确答案一致。

    从模型回答中提取选项（A-H），并与正确答案进行比较。
    如果一致返回1，不一致返回0。
    """

    # 定义类常量用于识别答案
    BOXED_PATTERN = r"\$\\boxed\{([A-H])\}\$"
    ANSWER_PATTERN = r"answer\s+([A-H])\.?"  # answer pattern
    SIMPLE_DOT_PATTERN = r"(?:^|[^A-Za-z])([A-H])\s*\."  # 带点的pattern，不限制点后面的内容
    SIMPLE_PATTERN = r"(?:^|[^A-Za-z])([A-H])(?:$|[^A-Za-z])"  # 不带点的pattern
    VALID_OPTIONS = set('ABCDEFGH')

    def __init__(self):
        """
        初始化选择题准确性奖励函数
        """
        # 检查是否安装了math_verify包
        import importlib.util
        assert importlib.util.find_spec('math_verify') is not None, (
            "The math_verify package is required but not installed. Please install it using 'pip install math_verify'.")

    def normalize_answer(self, answer: str) -> str:
        """
        标准化答案格式，从文本中提取答案

        Args:
            answer: 包含答案的文本

        Returns:
            标准化后的答案（A-H中的一个字母）
        """
        answer = answer.strip()

        # 首先尝试找到标准格式的答案（$\boxed{X}$）
        boxed_matches = list(re.finditer(self.BOXED_PATTERN, answer, re.IGNORECASE))
        if boxed_matches:
            # 使用最后一个匹配的答案
            return boxed_matches[-1].group(1).upper()

        # 其次寻找answer pattern
        answer_matches = list(re.finditer(self.ANSWER_PATTERN, answer, re.IGNORECASE))
        if answer_matches:
            # 使用第一个匹配的答案
            return answer_matches[0].group(1).upper()

        # 最后寻找单个字母
        # 先找带点的
        dot_matches = list(re.finditer(self.SIMPLE_DOT_PATTERN, answer, re.IGNORECASE))
        if dot_matches:
            # 使用最后一个带点的匹配
            return dot_matches[-1].group(1).upper()

        # 最后找不带点的
        simple_matches = list(re.finditer(self.SIMPLE_PATTERN, answer, re.IGNORECASE))
        if simple_matches:
            # 使用最后一个不带点的匹配
            return simple_matches[-1].group(1).upper()

        # 如果都没找到，返回原始文本（会在后续比较中返回0分）
        return answer.upper()

    def __call__(self, completions, solution, **kwargs) -> List[float]:
        """
        评估模型回答与正确答案的一致性

        Args:
            completions: 模型生成的回答列表
            solution: 正确答案列表
            kwargs: 其他参数

        Returns:
            准确性奖励列表，正确为1.0，错误为0.0
        """
        rewards = []

        # 确保 completions 和 solution 都是列表
        if not isinstance(completions, list):
            completions = [completions]

        if not isinstance(solution, list):
            solution = [solution] * len(completions)

        for content, sol in zip(completions, solution):
            try:
                # 标准化答案并比较
                normalized_content = self.normalize_answer(content)
                normalized_solution = self.normalize_answer(sol)

                # 如果模型答案与正确答案一致，返回1.0，否则返回0.0
                reward = float(normalized_content == normalized_solution)

                # 可选：打印调试信息
                # print(f"模型答案={normalized_content}，正确答案={normalized_solution}，奖励={reward}")
            except Exception as e:
                print(f"处理答案时发生错误: {e}")
                reward = 0.0  # 发生错误时返回0分

            rewards.append(reward)

        return rewards

# 注册奖励函数
orms['choice_accuracy'] = ChoiceAccuracy

# 为了兼容性，也注册为 'choice_accuracy' 名称
from swift.plugin.orm import orms
orms['choice_accuracy'] = ChoiceAccuracy
