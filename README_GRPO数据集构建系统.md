# GRPO训练数据集构建系统说明文档

## 系统概述

本系统基于已实现的导航评分系统，构建用于GRPO（Generalized Reward Preference Optimization）训练的数据集。系统从导航评分结果中提取负样本和正样本，为每个样本的所有10个可能动作计算详细的奖励值，生成与Swift RLHF训练流程兼容的数据格式。

## 主要功能

### 1. 负样本提取
- 从评分结果中提取所有step_score低于0.0的决策点作为需要重新训练的负样本
- 每个负样本包含完整的状态信息、错误动作和对应的低评分值

### 2. 正样本噪声增强
- 从step_score大于等于0.0的决策点中随机抽取最多200个点作为正样本
- 用于平衡训练数据，防止模型过度拟合负样本

### 3. 动作遍历评分
- 对每个被提取的决策点，遍历所有10个可能的导航动作
- 使用现有评分系统的核心函数计算每个动作的奖励值
- 确保计算逻辑与导航评分系统完全一致

### 4. 奖励组合规则
- **负样本决策点**：总奖励 = endpoint_score + trajectory_score
- **正样本决策点**：总奖励 = endpoint_score + trajectory_score + efficiency_score

## 文件结构

```
code/
├── grpo_dataset_builder.py           # GRPO数据集构建器主模块
├── analyze_navigation_scores.py      # 扩展的分析脚本（支持GRPO构建）
├── test_grpo_dataset_builder.py      # 单元测试
├── navigation_scoring_system.py      # 导航评分系统（依赖）
└── README_GRPO数据集构建系统.md      # 本说明文档
```

## 使用方法

### 1. 单任务模式 - 处理特定评分文件

```bash
# 基本用法
python code/grpo_dataset_builder.py --scores_file navigation_scores.json

# 完整参数（单任务模式）
python code/grpo_dataset_builder.py \
    --scores_file navigation_scores.json \
    --output_file grpo_dataset.json \
    --statistics_file grpo_statistics.json \
    --grpo_output_file_swift grpo_dataset_swift.jsonl \
    --base_dir data \
    --max_positive_samples 200 \
    --negative_threshold 0.0 \
    --seed 42
```

### 2. 批量处理模式 - 自动处理所有任务

```bash
# 批量处理所有任务
python code/grpo_dataset_builder.py \
    --batch_mode \
    --output_file grpo_dataset_all.json \
    --grpo_output_file_swift grpo_dataset_all_swift.jsonl \
    --max_positive_samples 500

# 批量处理（限制任务数量，用于测试）
python code/grpo_dataset_builder.py \
    --batch_mode \
    --max_tasks 5 \
    --output_file grpo_dataset_test.json \
    --grpo_output_file_swift grpo_dataset_test_swift.jsonl

# 批量处理完整参数
python code/grpo_dataset_builder.py \
    --batch_mode \
    --output_file grpo_dataset_batch.json \
    --statistics_file grpo_statistics_batch.json \
    --grpo_output_file_swift grpo_dataset_batch_swift.jsonl \
    --base_dir data \
    --max_positive_samples 500 \
    --negative_threshold 0.0 \
    --max_tasks 10 \
    --seed 42
```

### 3. 通过分析脚本构建

```bash
# 分析评分结果并构建GRPO数据集
python code/analyze_navigation_scores.py \
    --input_file navigation_scores.json \
    --build_grpo_dataset \
    --grpo_output_file grpo_dataset.json \
    --grpo_statistics_file grpo_statistics.json \
    --grpo_output_file_swift grpo_dataset_swift.jsonl \
    --max_positive_samples 200 \
    --negative_threshold 0.0
```

## 参数说明

### 运行模式参数
- `--batch_mode`: 启用批量处理模式，自动处理data/dataset_rl目录下的所有任务
- `--scores_file`: 单任务模式下的评分结果文件路径（单任务模式必需）

### 输出文件参数
- `--output_file`: GRPO数据集输出文件路径（默认：grpo_dataset.json）
- `--statistics_file`: 统计报告文件路径（默认：grpo_statistics.json）
- `--grpo_output_file_swift`: Swift RLHF兼容格式的输出文件路径（JSONL格式，可选）

### 数据处理参数
- `--base_dir`: 数据目录路径（默认：data）
- `--max_positive_samples`: 最大正样本数量（默认：200）
- `--negative_threshold`: 负样本阈值（默认：0.0）
- `--seed`: 随机种子（默认：42）

### 批量处理专用参数
- `--max_tasks`: 批量模式下最大处理任务数量（用于测试，可选）

## 输出文件详解

### 1. GRPO数据集文件 (grpo_dataset.json)

```json
{
  "samples": [
    {
      "task_id": "331_0",
      "original_task_id": "331",
      "attempt_id": "0",
      "step": 5,
      "navigation_target": "导航目标描述",
      "current_image": "data/dataset_rl/331/0/5.png",
      "current_location": [x, y, z, rx, ry, yaw, pitch],
      "model_action": "向前运动",
      "model_action_id": "6",
      "sample_type": "negative",
      "original_scores": {
        "endpoint_score": 0.122,
        "trajectory_score": -0.242,
        "efficiency_score": 0.0,
        "total_score": -0.120
      },
      "action_rewards": {
        "Move forward": -0.120,
        "Move backward": -0.288,
        "Move left": 0.114,
        "Move right": -0.182,
        "Move up": -0.260,
        "Move down": 0.207,
        "Rotate left": 0.028,
        "Rotate right": -0.181,
        "Look up": -0.467,
        "Look down": -0.019
      },
      "metadata": {
        "reference_steps": 15,
        "model_steps": 24,
        "final_location": [x, y, z, rx, ry, yaw, pitch]
      }
    }
    // ... 更多样本
  ],
  "statistics": {
    "total_samples": 24,
    "negative_samples": 19,
    "positive_samples": 5,
    "failed_samples": 0,
    "task_count": 1,
    "task_distribution": {
      "331": {"negative": 19, "positive": 5}
    },
    "action_frequency": {
      "向前运动": 24
    }
  },
  "metadata": {
    "negative_threshold": 0.0,
    "max_positive_samples": 200,
    "base_dir": "data",
    "scores_file": "navigation_scores.json"
  }
}
```

### 2. Swift RLHF兼容格式文件 (grpo_dataset_swift.jsonl)

JSONL格式，每行一个JSON对象，完全兼容Swift RLHF训练流程：

```json
{"messages": [{"role": "system", "content": "A conversation between User and Assistant..."}, {"role": "user", "content": "Current position image:\n<image>\nPlease continue to the navigation target..."}], "images": ["data/dataset_video_instruction/331/initial.png", "data/dataset_rl/331/0/0.png", "data/dataset_rl/331/0/1.png"], "solution": "Move forward", "folder_id": "331_0", "current_index": 1, "action_sequence": ["Move forward", "Move backward", "Move left"], "action_reward": {"Move forward": 1.0, "Move backward": -0.42893897148921983, "Move left": 0.11446805434531712, "Move right": -0.1825433428354375, "Move up": -0.2605784112984122, "Move down": 0.20768226331034545, "Rotate left": 0.02862465459250719, "Rotate right": -0.1819448457606674, "Look up": -0.4678976198252673, "Look down": -0.01912986876654771}}
```

**关键字段说明：**
- `messages`: 包含system和user消息的对话格式
- `images`: 完整的图像路径列表（初始图像 + 历史观察 + 当前位置）
- `solution`: 模型选择的动作（英文）
- `folder_id`: 格式为"{original_task_id}_{attempt_id}"，如"331_0"
- `current_index`: 当前步骤索引
- `action_sequence`: 完整的动作序列
- `action_reward`: 简化的奖励值，直接映射动作名称到总奖励值

### 3. 统计报告文件 (grpo_statistics.json)

```json
{
  "dataset_summary": {
    "total_samples": 24,
    "negative_samples": 19,
    "positive_samples": 5,
    "failed_samples": 0,
    "task_count": 1,
    "negative_ratio": 0.79
  },
  "reward_distribution": {
    "endpoint_scores": {
      "min": -0.496,
      "max": 0.496,
      "mean": 0.013,
      "std": 0.286
    },
    "trajectory_scores": {
      "min": -0.773,
      "max": 0.645,
      "mean": -0.016,
      "std": 0.305
    },
    "efficiency_scores": {
      "min": 0.0,
      "max": 0.1,
      "mean": 0.021,
      "std": 0.041
    },
    "total_rewards": {
      "min": -0.995,
      "max": 0.994,
      "mean": 0.018,
      "std": 0.549
    }
  },
  "sample_type_distribution": {
    "negative": 19,
    "positive": 5
  },
  "action_frequency": {
    "向前运动": 24
  },
  "task_contributions": [
    {
      "task_id": "331",
      "total_samples": 24,
      "negative_samples": 19,
      "positive_samples": 5,
      "negative_ratio": 0.79
    }
  ]
}
```

## 数据格式兼容性

### 与Swift RLHF训练流程兼容
- 数据格式参考`data/process_numeric_dataset_with_rewards.py`
- 包含必要的元数据字段（task_id, step_index, action_reward等）
- 支持英文动作名称映射
- 提供完整的奖励分解信息

### 动作映射表
```python
ZH_TO_EN_ACTION_MAPPING = {
    "向前运动": "Move forward",
    "向后运动": "Move backward",
    "向左运动": "Move left",
    "向右运动": "Move right",
    "向上运动": "Move up",
    "向下运动": "Move down",
    "向左旋转": "Rotate left",
    "向右旋转": "Rotate right",
    "向上看": "Look up",
    "向下看": "Look down"
}
```

## 质量保证

### 单元测试
运行单元测试验证系统功能：
```bash
python code/test_grpo_dataset_builder.py
```

测试覆盖：
- 样本提取功能
- 动作应用功能
- 奖励计算功能
- GRPO样本构建功能
- 统计报告生成功能

### 数据验证
- 自动检查图像文件存在性
- 验证坐标数据完整性
- 确保奖励计算一致性
- 提供详细的错误报告

## 使用示例

### 完整工作流程

```bash
# 1. 生成导航评分
python code/navigation_scoring_system.py --task_id 331 --output_file scores_331.json

# 2. 构建GRPO数据集
python code/grpo_dataset_builder.py --scores_file scores_331.json --output_file grpo_331.json

# 3. 查看统计信息
python code/analyze_navigation_scores.py --input_file scores_331.json --build_grpo_dataset
```

### 批量处理

```bash
# 1. 评估所有任务
python code/navigation_scoring_system.py --output_file all_scores.json

# 2. 构建大规模GRPO数据集
python code/grpo_dataset_builder.py \
    --scores_file all_scores.json \
    --output_file grpo_large_dataset.json \
    --max_positive_samples 500
```

## 注意事项

1. **数据依赖**：确保导航评分系统已正确运行并生成评分文件
2. **存储空间**：大规模数据集可能占用较多存储空间
3. **计算时间**：每个样本需要计算10个动作的奖励，处理时间较长
4. **随机性**：使用固定随机种子确保结果可重现
5. **兼容性**：确保与现有Swift RLHF训练流程的兼容性

## 系统要求

- Python 3.7+
- numpy
- 标准库: json, csv, argparse, random, math
- 依赖: navigation_scoring_system.py

## 版本历史

- **v1.0**: 初始版本，支持基本的GRPO数据集构建功能
- 包含负样本提取、正样本增强、动作遍历评分等核心功能
- 提供完整的统计报告和数据验证机制
