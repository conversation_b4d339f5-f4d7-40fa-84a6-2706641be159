import copy
import os
import cv2
import numpy as np
import pandas as pd
import base64
from qwen_api_client import QwenAPIClient
import pickle
from airsim_utils.coord_transformation import quaternion2eularian_angles
import airsim
from collections import deque
import time

# 定义导航动作列表
NAVIGATION_ACTIONS = [
    "Move forward",
    "Move backward",
    "Move left",
    "Move right",
    "Move up",
    "Move down",
    "Rotate left",
    "Rotate right",
    "Look up",
    "Look down"
]

# 定义动作的简写或变体映射
ACTION_VARIANTS = {
    # 英文完整匹配（优先级最高）
    "move forward": "Move forward",
    "move backward": "Move backward",
    "move left": "Move left",
    "move right": "Move right",
    "move up": "Move up",
    "move down": "Move down",
    "rotate left": "Rotate left",
    "rotate right": "Rotate right",
    "look up": "Look up",
    "look down": "Look down",

    # 英文简写匹配（优先级次之）
    "forward": "Move forward",
    "backward": "Move backward",
    "left": "Move left",
    "right": "Move right",
    "up": "Move up",
    "down": "Move down",

    # 其他英文变体
    "tilt up": "Look up",
    "tilt down": "Look down",
    "go forward": "Move forward",
    "go backward": "Move backward",
    "go left": "Move left",
    "go right": "Move right",
    "go up": "Move up",
    "go down": "Move down",
    "turn left": "Rotate left",
    "turn right": "Rotate right",
    "move_forth": "Move forward",
    "move_back": "Move backward",
    "turn_left": "Rotate left",
    "turn_right": "Rotate right",
    "angle_up": "Look up",
    "angle_down": "Look down",
    "forth": "Move forward",
    "back": "Move backward",

    # 中文匹配
    "向前": "Move forward",
    "向后": "Move backward",
    "向左": "Move left",
    "向右": "Move right",
    "向上": "Move up",
    "向下": "Move down",
    "左转": "Rotate left",
    "右转": "Rotate right",
    "向上看": "Look up",
    "向下看": "Look down",
    "向前运动": "Move forward",
    "向后运动": "Move backward",
    "向左移动": "Move left",
    "向右移动": "Move right",
    "向上运动": "Move up",
    "向下运动": "Move down",
    "向左旋转": "Rotate left",
    "向右旋转": "Rotate right"
}

# 标准动作到枚举值的映射
ACTION_TO_ENUM = {
    "Move forward": 6,     # 向前运动
    "Move backward": 7,    # 向后运动
    "Move left": 8,        # 向左运动
    "Move right": 9,       # 向右运动
    "Move up": 10,         # 向上运动
    "Move down": 11,       # 向下运动
    "Rotate left": 2,      # 向左旋转
    "Rotate right": 3,     # 向右旋转
    "Look up": 4,          # 向上看
    "Look down": 5         # 向下看
}

def normalize_action(action):
    """
    标准化动作格式，匹配到预定义的导航动作
    
    Args:
        action: 包含动作的文本，可以是字符串或其他类型

    Returns:
        标准化后的动作文本
    """
    # 处理非字符串类型
    if not action:
        return ""

    # 如果是列表，递归处理第一个元素
    if isinstance(action, list):
        if len(action) > 0:
            return normalize_action(action[0])
        else:
            return ""

    # 如果不是字符串，尝试转换为字符串
    if not isinstance(action, str):
        try:
            action = str(action)
        except:
            return ""

    # 去除首尾空白并转为小写以便比较
    action_lower = action.strip().lower()

    # 1. 直接检查完整匹配（最高优先级）
    for nav_action in NAVIGATION_ACTIONS:
        if nav_action.lower() == action_lower:
            return nav_action

    # 2. 检查是否包含完整的动作短语（次高优先级）
    # 例如："I should look up" 应该匹配 "Look up" 而不是 "Move up"
    exact_matches = []
    for nav_action in NAVIGATION_ACTIONS:
        if nav_action.lower() in action_lower:
            # 记录匹配的动作和它在文本中的位置
            exact_matches.append((nav_action, action_lower.find(nav_action.lower())))

    # 如果找到完整匹配，返回最前面出现的那个
    if exact_matches:
        # 按照出现位置排序，返回最前面出现的
        exact_matches.sort(key=lambda x: x[1])
        return exact_matches[0][0]

    # 3. 检查动作变体映射（第三优先级）
    # 先检查较长的变体，避免错误匹配
    # 例如："look up" 应该匹配 "Look up" 而不是 "Move up"
    variant_matches = []
    for variant, standard_action in sorted(ACTION_VARIANTS.items(), key=lambda x: -len(x[0])):
        if variant.lower() in action_lower:
            # 记录匹配的变体、对应的标准动作和它在文本中的位置
            variant_matches.append((variant, standard_action, action_lower.find(variant.lower())))

    # 如果找到变体匹配，返回最长的那个变体对应的标准动作
    if variant_matches:
        # 按照变体长度和出现位置排序，优先选择较长的变体和较后出现的
        variant_matches.sort(key=lambda x: (len(x[0]), x[2]))
        return variant_matches[-1][1]

    # 如果没有找到匹配项，返回原始文本
    return action

def parse_llm_action(llm_output: str):
    """
    将LLM回复中的动作指令提取并匹配到预定义的动作枚举值
    
    Args:
        llm_output: LLM的回复文本
        
    Returns:
        动作的枚举值，如果没有匹配到则返回-1
    """
    # 先尝试提取Command部分
    try:
        # 尝试提取"Command:"后面的内容
        if "Command:" in llm_output:
            command_str = llm_output.split("Command:")[-1].strip()
        else:
            command_str = llm_output
    except:
        command_str = llm_output
    
    # 标准化动作
    normalized_action = normalize_action(command_str)
    
    # 映射到枚举值
    if normalized_action in ACTION_TO_ENUM:
        return ACTION_TO_ENUM[normalized_action]
    
    # 如果没有匹配到标准动作，尝试在整个文本中查找
    normalized_action = normalize_action(llm_output)
    if normalized_action in ACTION_TO_ENUM:
        return ACTION_TO_ENUM[normalized_action]
    
    # 没有匹配到任何动作
    return -1

class ActionGen:
    # Agent的动作逻辑
    def __init__(self, model, client, airsim_client, task_desc):
        """
        :param model: LM model
        :param client: LLM client
        :param airsim_client: Airsim client
        :param task_desc: 导航目标的文本描述
        """
        self.model = model
        self.model_class = model.split('-')[0]
        self.llm_client = client
        self.queue = deque()
        self.messages = [] # 历史LLM对话信息
        self.airsim_client = airsim_client
        self.task_desc = task_desc

    def query(self, camera_angle):
        """
        单步动作逻辑

        :param camera_angle: 当前无人机的摄像头云台角度
        :return: LLM的输出
        """

        # 获取前视摄像头的视觉观测
        img1 = self.airsim_client.get_image()

        # Getting the base64 string
        _, buffer = cv2.imencode('.jpg', img1)

        # 使用 Base64 编码
        base64_image1 = base64.b64encode(buffer).decode('utf-8')

        # 分情况，如果是首次query，需要在prompt交代背景；否则继续问询
        if len(self.messages) == 0:
            UserContent = f"Please follow the instructions provided to control the camera gimbal angle and drone to gradually move to the customer's designated location.\
        Assuming the angle range of the camera gimbal is -90 degrees to 90 degrees, where -90 degrees represents vertical downward view, \
                0 degrees represents horizontal view, and 90 degrees represents vertical upward view. \
                            \n\
                            The camera angle adjustment command:\n\
                                angle_down, angle_up \n\
                                \n\
                                Drone command:\n\
                                move_forth, move_back, turn_left, turn_right, turn_left, turn_right, move_up, move_down\n\
        \n\
        Example:\n\
            The navigation goal is: main entrance of the building directly below. The current angle of the camera gimbal is {camera_angle}. \n\
            \n\
            Thinking: Should first lower the altitude and then search\n\
            Command: move_forth\n\
            The thought process is placed after Thinking. After 'Command:', only display the actual executed command and do not output any other redundant information (One command a time)\n\
                            \n\
                            The navigation goal is: {self.task_desc}. The current angle of the camera gimbal is {camera_angle}. \n\
                            Note, avoid constantly spinning in place\n\
                            \n\
                            Thinking:\n\
                            Command:\
                        "
        else:
            UserContent = f"The navigation goal is: {self.task_desc}. The current angle of the camera gimbal is {camera_angle}.\n\
            continue to output the thinking and command to approach the navigation destination\n\
            Thinking:\n\
            Command:\
                "

        # 调用 QwenAPIClient - 修改API调用方式
        self.messages.append({"role": "user", "content": [
            {
                "type": "text",
                "text": UserContent
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{copy.deepcopy(base64_image1)}"
                }
            },
        ]})
        try:
            # 使用 QwenAPIClient 的 chat 方法
            answer = self.llm_client.chat(
                messages=self.messages,
                model=self.model
            )
            print(f'LLM: {answer}')
        except Exception as e:
            print(f'Error: LM response - {e}')
            answer = "Error"

        self.messages.append({"role": "assistant", "content": answer})

        return answer

class AirsimClient:
    # Airsim常用控制和感知指令
    def __init__(self, vehicle_name=""):
        AirSim_client = airsim.VehicleClient()
        AirSim_client.confirmConnection()
        self.client = AirSim_client

    def set_vehicle_pose(self, position, orientation):
        """
        无人机瞬移到指定位姿
        :param position: xyz坐标
        :param orientation: 旋转角
        """
        client = self.client
        pose = airsim.Pose(airsim.Vector3r(*position), airsim.to_quaternion(*orientation))
        client.simSetVehiclePose(pose, True)

    def set_camera_angle(self, angle):
        """
        将无人机摄像头云台角度调整为指定角度
        :param angle: 摄像头云台角度
        :return:
        """
        # 将角度转换为四元数设置相机姿态
        client = self.client
        camera_pose = airsim.Pose(airsim.Vector3r(0, 0, 0), airsim.to_quaternion(angle * np.pi / 180, 0, 0))
        client.simSetCameraPose("0", camera_pose)

    def move_relative(self, dx, dy, dz):
        """
        根据无人机当前朝向，向前移动指定距离
        dx, dy, dz 是相对于无人机局部坐标系的移动量。
        """
        client = self.client
        pose = client.simGetVehiclePose()
        orientation = airsim.to_eularian_angles(pose.orientation)  # 获取当前朝向的欧拉角
        yaw = orientation[2]  # 获取偏航角（yaw）

        # 计算局部坐标系下的移动向量在世界坐标系中的分量
        forward = np.array([np.cos(yaw), np.sin(yaw), 0])  # 前方向量
        right = np.array([-np.sin(yaw), np.cos(yaw), 0])  # 右方向量
        up = np.array([0, 0, 1])  # 上方向量

        # 将局部坐标系的移动量转换到世界坐标系
        move_vector = dx * forward + dy * right + dz * up
        new_position = np.array([pose.position.x_val, pose.position.y_val, pose.position.z_val]) + move_vector

        # 设置新的位置
        self.set_vehicle_pose(new_position, orientation)

    def get_current_state(self):
        # get world frame pos and orientation
        # orientation is in roll, pitch, yaw format
        client = self.client
        state = client.simGetGroundTruthKinematics()
        pos = state.position.to_numpy_array()
        ori = quaternion2eularian_angles(state.orientation)
        return pos, ori

    def get_image(self):
        # 获取无人机前置摄像头的RGB观测
        response = self.client.simGetImages([airsim.ImageRequest("0", airsim.ImageType.Scene, False, False)])
        img1d = np.frombuffer(response[0].image_data_uint8, dtype=np.uint8)
        if img1d.size == (response[0].height * response[0].width * 3):
            img_rgb = img1d.reshape(response[0].height, response[0].width, 3)
        return img_rgb

class VLN_evaluator:
    # 视觉语言导航评测
    def __init__(self, root_dir, eval_model, llm_client, agent_method):
        """

        :param root_dir: 导航数据集的路径
        :param eval_model: llm的名字
        :param llm_client: llm client
        :param agent_method: 评测方法
        """
        self.root_dir = root_dir
        self.eval_model = eval_model
        self.airsim_client = AirsimClient()
        self.agent_method = agent_method
        self.llm_client = llm_client
        self.load_navi_task()

    def load_navi_task(self):
        # 导入导航数据
        with open(os.path.join(self.root_dir, 'navi_data.pkl'), 'rb') as f:
            self.navi_data = pickle.load(f)

    def evaluation(self):  # 评测方法的导航效果，输出SR（成功率），SPL（考虑距离加权的成功率），NE（最终到达位置与目标位置的距离）

        navi_data = self.navi_data
        navi_data_pd = pd.DataFrame(navi_data)

        # 考虑分三组统计，分别是短距离，中距离，长距离
        short_len = navi_data_pd['gt_traj_len'].quantile(1/3)
        middle_len = navi_data_pd['gt_traj_len'].quantile(2/3)
        SR_count_sets = np.zeros((3,))
        num_sets = np.zeros((3,))
        ne_count_sets = np.zeros((3,))
        SPL_sets = np.zeros((3,))

        # 所有一起统计
        SR_count = 0.0
        SPL = 0.0
        ne_count = 0.0

        # 逐个case导航
        for idx in range(len(navi_data)):

            # 获得当前case的导航数据
            navi_task = navi_data[idx]
            start_pos = navi_task["start_pos"]
            start_rot = navi_task["start_rot"]
            gt_traj = navi_task["gt_traj"]
            target_pos = navi_task["target_pos"]
            gt_traj_len = navi_task["gt_traj_len"]
            task_desc = navi_task["task_desc"]

            # 初始化Agent
            agent = ActionGen(self.eval_model, self.llm_client, self.airsim_client, task_desc)

            # 初始化位姿和云台角度
            self.airsim_client.set_vehicle_pose(start_pos, start_rot)
            self.camera_angle = 0
            self.airsim_client.set_camera_angle(self.camera_angle)
            print('当前导航目标：%s' % task_desc)

            # Print当前位姿
            cur_pos, cur_rot = self.airsim_client.get_current_state()
            print(f"pos: {cur_pos}, rot: {cur_rot}")

            # 记录过程中完整的轨迹
            traj_df = pd.DataFrame(columns=['pos', 'rot', 'camera_angle'])
            traj_df.loc[traj_df.shape[0]] = [start_pos, start_rot, self.camera_angle]

            traj_len = 0.0  # 记录本次导航移动的距离
            step = 0  # 当前决策步数
            max_steps = 50  # 最大动作步数
            threshold = 20  # 多少米内判定为导航成功

            # 逐步决策
            while step < max_steps:  # 50步内没完成则停止

                # Agent根据当前观测输出动作
                answer = agent.query(self.camera_angle)

                # 提取Agent输出的文本中的动作
                act = parse_llm_action(answer)
                print("action: ", act)

                # 执行动作
                self.perform_act(act)
                time.sleep(0.1)

                former_pos = cur_pos  # 动作前的位置
                cur_pos, cur_rot = self.airsim_client.get_current_state()  # 动作后的位置
                traj_df.loc[traj_df.shape[0]] = [cur_pos, cur_rot, self.camera_angle]  # 记录当前位置
                traj_len += np.linalg.norm(cur_pos - former_pos)  # 累加移动的距离
                step += 1  # 更新当前step

                # 输出当前位置与目标位置的距离
                dist = np.linalg.norm(cur_pos - target_pos)
                print(f"Task idx: {idx}, current step size: {step}, current dist: {dist}")

                # 如果距离小于给定阈值，判定为导航成功。如果距离过远，判定为导航失败
                if dist < threshold:
                    break
                elif dist > 300:
                    break

            # 记录该case最终的位姿
            print(f"Max step size reached or target reached. step: {step}")
            dist = np.linalg.norm(cur_pos - target_pos)

            # 保存本次case的完整路径
            save_folder_path = 'results/%s/%s' % (self.agent_method, self.eval_model)
            if not os.path.exists(save_folder_path):  # 检查路径是否存在
                os.makedirs(save_folder_path)  # 创建文件夹（包括父目录）
            traj_df.to_csv(os.path.join(save_folder_path, '%d.csv' % idx), index=False)

            # 计算指标
            if gt_traj_len < short_len:
                num_sets[0] += 1
                ne_count_sets[0] += dist
            elif gt_traj_len < middle_len:
                num_sets[1] += 1
                ne_count_sets[1] += dist
            else:
                num_sets[2] += 1
                ne_count_sets[2] += dist

            if dist < threshold:
                SR_count += 1
                SPL_count = gt_traj_len / max(gt_traj_len, traj_len)
                SPL += SPL_count

                if gt_traj_len < short_len:
                    SR_count_sets[0] += 1
                    SPL_sets[0] += gt_traj_len / max(gt_traj_len, traj_len)
                elif gt_traj_len < middle_len:
                    SR_count_sets[1] += 1
                    SPL_sets[1] += gt_traj_len / max(gt_traj_len, traj_len)
                else:
                    SR_count_sets[2] += 1
                    SPL_sets[2] += gt_traj_len / max(gt_traj_len, traj_len)

            ne_count += dist
            print(f"####### SR count: {SR_count}, SPL: {SPL}, NE: {ne_count}")
            print('各分组SR:', SR_count_sets / num_sets)
            print('各分组SPL:', SPL_sets / num_sets)
            print('各分组DTG:', ne_count_sets / num_sets)
            print('各分组数量:', num_sets)

        # 所有case完成后，计算指标
        SR = SR_count / len(navi_data)
        NE = ne_count / len(navi_data)
        print(f"SR: {SR}, SPL: {SPL}, NE: {NE}")
        np.set_printoptions(precision=3)
        print('各分组SR:', SR_count_sets / num_sets)
        print('各分组SPL:', SPL_sets / num_sets)
        print('各分组DTG:', ne_count_sets / num_sets)

    def perform_act(self, act_enum):
        """
        执行文本中提取的动作
        :param act_enum: 动作index
        """

        # 动作对应表
        commands_map = {
            6: ('向前运动', (10, 0, 0)),
            7: ('向后运动', (-10, 0, 0)),
            8: ('向左运动', (0, -10, 0)),
            9: ('向右运动', (0, 10, 0)),
            10: ('向上运动', (0, 0, -10)),
            11: ('向下运动', (0, 0, 10)),
            2: ('向左旋转', -22.5),
            3: ('向右旋转', 22.5),
            4: ('向上看', 45),
            5: ('向下看', -45)
        }

        try:
            # 提取出当前动作的信息
            command, value = commands_map[act_enum]

            # 执行动作
            if command in ['向上看', '向下看']:
                # 云台调整
                pre_angle = self.camera_angle
                self.camera_angle += value
                self.camera_angle = max(-90, min(90, self.camera_angle))  # 限制云台角度在 -90 到 90 度之间
                self.airsim_client.set_camera_angle(self.camera_angle)
            elif act_enum in commands_map.keys():
                # 位置或旋转调整
                if isinstance(value, tuple):  # 运动命令
                    dx, dy, dz = value
                    self.airsim_client.move_relative(dx, dy, dz)
                else:  # 旋转命令
                    yaw_change = value
                    pose = self.airsim_client.client.simGetVehiclePose()
                    current_orientation = airsim.to_eularian_angles(pose.orientation)
                    new_orientation = [current_orientation[0], current_orientation[1], current_orientation[2] + np.radians(yaw_change)]
                    self.airsim_client.set_vehicle_pose([pose.position.x_val, pose.position.y_val, pose.position.z_val], new_orientation)
            else:
                print(f"Unknown action {act_enum}, keep still.")
        except:
            pass

if __name__ == "__main__":

    # 定义LLM client - 修改为QwenAPIClient
    model = "qwen2.5-vl-3b-instruct"  # 使用Qwen模型
    # 初始化 QwenAPIClient 替代 AzureOpenAI
    llm_client = QwenAPIClient(
        api_url="http://localhost:8000",  # 默认API地址，可根据实际情况修改
        timeout=60,  # 增加超时时间以处理导航图像
        max_retries=3
    )

    # 本次评测的方法
    agent_method = 'action_generation'

    # 初始化和运行
    vln_eval = VLN_evaluator("dataset", model, llm_client, agent_method)
    vln_eval.evaluation()

