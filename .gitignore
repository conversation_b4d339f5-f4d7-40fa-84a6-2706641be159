# 忽略所有图片文件
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.tif
*.webp
*.svg
*.ico
*.psd
*.ai
*.eps

# 忽略数据文件
*.csv
*.json
*.txt
*.jsonl

# 忽略模型文件和权重
*.pt
*.pth
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.mlmodel
*.caffemodel
*.weights
*.bin
*.ckpt
*.safetensors
*.model
*.params
*.pdparams
*.pdopt
*.pdmodel

# 忽略模型目录
model/*/weights/
model/*/checkpoints/
model/*/pretrained/
model/*/*.bin
model/*/*.ckpt
model/*/*.pt
model/*/*.pth
model/*/*.h5
model/*/*.onnx
model/*/*.safetensors

# 忽略特定的模型目录
model/Qwen2.5-VL-3B-Instruct/
model/Qwen2.5-VL-3B-Instruct-Epoch18/

# 忽略数据集目录中的图片和数据文件
dataset*/*/images/
dataset*/*/*.jpg
dataset*/*/*.jpeg
dataset*/*/*.png
dataset*/*/*.csv
dataset*/*/*.json
dataset*/*/*.txt
dataset*/*/*.jsonl

# 忽略数据目录
dataset/
raw_data/
processed_data/
annotations/
labels/

# 忽略缓存文件
.cache/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 忽略日志文件
*.log
logs/
tensorboard/
runs/

# 忽略临时文件
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 例外规则 - 不忽略这些文件
!config.json
!settings.json
!requirements.txt
!README.md
!LICENSE.txt
!.gitignore
!setup.py
!pyproject.toml
!model/swift/windows/*.py
!model/swift/windows/*.bat
!model/swift/windows/*.md
