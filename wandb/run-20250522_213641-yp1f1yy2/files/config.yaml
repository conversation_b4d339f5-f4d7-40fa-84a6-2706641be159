_attn_implementation_autoset:
    value: true
_name_or_path:
    value: /home/<USER>/.cache/modelscope/hub/Qwen/Qwen2___5-VL-7B-Instruct
_wandb:
    value:
        cli_version: 0.19.11
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
            - "1": 'profiling/Time taken: GRPOTrainer._get_per_token_logps'
              "5": 1
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.10.16
        t:
            "1":
                - 1
                - 11
                - 30
                - 41
                - 49
                - 51
                - 55
                - 71
                - 84
                - 95
                - 98
                - 105
            "2":
                - 1
                - 11
                - 30
                - 41
                - 49
                - 51
                - 55
                - 71
                - 84
                - 95
                - 98
                - 105
            "3":
                - 7
                - 13
                - 19
                - 23
                - 55
                - 66
            "4": 3.10.16
            "5": 0.19.11
            "6": 4.51.3
            "8":
                - 5
            "9":
                "1": transformers_trainer
            "12": 0.19.11
            "13": linux-x86_64
acc_steps:
    value: 1
acc_strategy:
    value: token
accelerator_config:
    value:
        dispatch_batches: false
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.95
adam_epsilon:
    value: 1e-08
add_cross_attention:
    value: false
architectures:
    value:
        - Qwen2_5_VLForConditionalGeneration
async_generate:
    value: false
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
beta:
    value: 0.001
bf16:
    value: true
bf16_full_eval:
    value: false
bos_token_id:
    value: 151643
cache_implementation:
    value: null
check_model:
    value: true
chunk_size_feed_forward:
    value: 0
cosine_max_len:
    value: 1024
cosine_max_len_value_correct:
    value: 0.5
cosine_max_len_value_wrong:
    value: 0
cosine_min_len_value_correct:
    value: 1
cosine_min_len_value_wrong:
    value: -0.5
cross_attention_hidden_size:
    value: null
data_seed:
    value: 42
dataloader_drop_last:
    value: true
dataloader_num_workers:
    value: 4
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: 10
dataset_shuffle:
    value: true
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value:
        bf16:
            enabled: auto
        fp16:
            enabled: auto
            hysteresis: 2
            initial_scale_power: 16
            loss_scale: 0
            loss_scale_window: 1000
            min_loss_scale: 1
        gradient_accumulation_steps: auto
        gradient_clipping: auto
        steps_per_print: 2000
        train_batch_size: auto
        train_micro_batch_size_per_gpu: auto
        wall_clock_breakdown: false
        zero_optimization:
            contiguous_gradients: true
            offload_optimizer:
                device: none
                pin_memory: true
            offload_param:
                device: none
                pin_memory: true
            overlap_comm: false
            reduce_bucket_size: auto
            stage: 3
            stage3_gather_16bit_weights_on_model_save: true
            stage3_max_live_parameters: 1e+09
            stage3_max_reuse_distance: 1e+09
            stage3_param_persistence_threshold: auto
            stage3_prefetch_bucket_size: 0
            sub_group_size: 1e+09
            zero_quantized_gradients: false
            zero_quantized_weights: false
disable_dropout:
    value: false
disable_tqdm:
    value: false
diversity_penalty:
    value: 0
do_eval:
    value: true
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
ds3_gather_for_generation:
    value: true
dynamic_sample:
    value: false
early_stopping:
    value: false
encoder_no_repeat_ngram_size:
    value: 0
eos_token_id:
    value: 151645
epsilon:
    value: 0.2
epsilon_high:
    value: null
eval_accumulation_steps:
    value: null
eval_datasets:
    value: []
eval_datasets_args:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_generation_config:
    value: null
eval_limit:
    value: null
eval_on_start:
    value: false
eval_steps:
    value: 200000
eval_strategy:
    value: steps
eval_use_evalscope:
    value: false
eval_use_gather_object:
    value: false
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: false
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_num:
    value: 1
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
galore_config:
    value: null
gc_collect_after_offload:
    value: false
gradient_accumulation_steps:
    value: 4
gradient_checkpointing:
    value: true
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: true
group_by_length:
    value: false
half_precision_backend:
    value: auto
hidden_act:
    value: silu
hidden_size:
    value: 3584
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
image_token_id:
    value: 151655
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
intermediate_size:
    value: 18944
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 5e-07
length_column_name:
    value: length
length_penalty:
    value: 1
lmdeploy_cache_max_entry_count:
    value: 0.8
lmdeploy_device:
    value: auto
lmdeploy_session_len:
    value: null
load_best_model_at_end:
    value: false
local_rank:
    value: 0
local_repo_path:
    value: null
log_completions:
    value: true
log_level:
    value: debug
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: /home/<USER>/wzy/NIPS/port/v9-20250522-213558/runs
logging_first_step:
    value: true
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 1
logging_strategy:
    value: steps
loss_type:
    value: grpo
lr_scheduler_kwargs:
    value: null
lr_scheduler_type:
    value: cosine
mask_truncated_completions:
    value: false
max_completion_length:
    value: 1024
max_grad_norm:
    value: 1
max_length:
    value: 20
max_position_embeddings:
    value: 128000
max_prompt_length:
    value: 512
max_resample_times:
    value: 3
max_steps:
    value: -1
max_window_layers:
    value: 28
metric_for_best_model:
    value: reward
metric_warmup_step:
    value: 0
min_length:
    value: 0
min_p:
    value: null
model/num_parameters:
    value: 0
model_init_kwargs:
    value: null
model_type:
    value: qwen2_5_vl
move_model_batches:
    value: null
mp_parameters:
    value: ""
multi_turn_func:
    value: null
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 28
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_completions_to_print:
    value: null
num_generations:
    value: 8
num_hidden_layers:
    value: 28
num_infer_workers:
    value: 8
num_iterations:
    value: 1
num_key_value_heads:
    value: 4
num_return_sequences:
    value: 1
num_train_epochs:
    value: 4
offload_model:
    value: false
offload_optimizer:
    value: false
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
optimizer:
    value: null
output_attentions:
    value: false
output_dir:
    value: /home/<USER>/wzy/NIPS/port/v9-20250522-213558
output_hidden_states:
    value: false
output_scores:
    value: false
overlong_filter:
    value: false
overwrite_output_dir:
    value: false
pad_token_id:
    value: 151643
past_index:
    value: -1
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
prediction_loss_only:
    value: false
prefix:
    value: null
problem_type:
    value: null
push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
ray_scope:
    value: last
ref_model_mixup_alpha:
    value: 0.6
ref_model_sync_steps:
    value: 512
remove_invalid_values:
    value: false
remove_unused_columns:
    value: false
repetition_max_penalty:
    value: -1
repetition_n_grams:
    value: 3
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
reward_weights:
    value:
        - 0.3
        - 0.7
rms_norm_eps:
    value: 1e-06
rope_scaling:
    value:
        mrope_section:
            - 16
            - 24
            - 24
        rope_type: default
        type: default
rope_theta:
    value: 1e+06
run_name:
    value: /home/<USER>/wzy/NIPS/port/v9-20250522-213558
save_on_each_node:
    value: false
save_only_model:
    value: true
save_safetensors:
    value: true
save_steps:
    value: 200
save_strategy:
    value: steps
save_total_limit:
    value: 200
scale_rewards:
    value: true
seed:
    value: 42
sep_token_id:
    value: null
shuffle_dataset:
    value: true
skip_memory_metrics:
    value: true
sleep_level:
    value: 0
sliding_window:
    value: 32768
soft_cache_length:
    value: null
soft_max_length:
    value: null
stop_words:
    value: []
suppress_tokens:
    value: null
sync_ref_model:
    value: false
task_specific_params:
    value: null
temperature:
    value: 1
tensor_parallel_size:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: null
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 0.9
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: bfloat16
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tp_size:
    value: 0
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
train_dataloader_shuffle:
    value: true
train_type:
    value: full
transformers_version:
    value: 4.51.3
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: false
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_liger_loss:
    value: false
use_lmdeploy:
    value: false
use_mps_device:
    value: false
use_sliding_window:
    value: false
use_vllm:
    value: false
video_token_id:
    value: 151656
vision_config:
    value:
        _attn_implementation_autoset: false
        _name_or_path: ""
        add_cross_attention: false
        architectures: null
        bad_words_ids: null
        begin_suppress_tokens: null
        bos_token_id: null
        chunk_size_feed_forward: 0
        cross_attention_hidden_size: null
        decoder_start_token_id: null
        depth: 32
        diversity_penalty: 0
        do_sample: false
        early_stopping: false
        encoder_no_repeat_ngram_size: 0
        eos_token_id: null
        exponential_decay_length_penalty: null
        finetuning_task: null
        forced_bos_token_id: null
        forced_eos_token_id: null
        fullatt_block_indexes:
            - 7
            - 15
            - 23
            - 31
        hidden_act: silu
        hidden_size: 1280
        id2label:
            "0": LABEL_0
            "1": LABEL_1
        in_channels: 3
        in_chans: 3
        intermediate_size: 3420
        is_decoder: false
        is_encoder_decoder: false
        label2id:
            LABEL_0: 0
            LABEL_1: 1
        length_penalty: 1
        max_length: 20
        min_length: 0
        model_type: qwen2_5_vl
        no_repeat_ngram_size: 0
        num_beam_groups: 1
        num_beams: 1
        num_heads: 16
        num_return_sequences: 1
        out_hidden_size: 3584
        output_attentions: false
        output_hidden_states: false
        output_scores: false
        pad_token_id: null
        patch_size: 14
        prefix: null
        problem_type: null
        remove_invalid_values: false
        repetition_penalty: 1
        return_dict: true
        return_dict_in_generate: false
        sep_token_id: null
        spatial_merge_size: 2
        spatial_patch_size: 14
        suppress_tokens: null
        task_specific_params: null
        temperature: 1
        temporal_patch_size: 2
        tf_legacy_loss: false
        tie_encoder_decoder: false
        tie_word_embeddings: true
        tokenizer_class: null
        tokens_per_second: 2
        top_k: 50
        top_p: 1
        torch_dtype: bfloat16
        torchscript: false
        typical_p: 1
        use_bfloat16: false
        window_size: 112
vision_end_token_id:
    value: 151653
vision_start_token_id:
    value: 151652
vision_token_id:
    value: 151654
vllm_device:
    value:
        - auto
vllm_dtype:
    value: null
vllm_enable_prefix_caching:
    value: true
vllm_enforce_eager:
    value: false
vllm_gpu_memory_utilization:
    value: 0.9
vllm_guided_decoding_regex:
    value: null
vllm_max_model_len:
    value: null
vllm_max_num_seqs:
    value: 256
vllm_server_host:
    value: null
vllm_server_port:
    value: 8000
vllm_server_timeout:
    value: 240
vocab_size:
    value: 152064
wandb_log_unique_prompts:
    value: null
warmup_ratio:
    value: 0.05
warmup_steps:
    value: 0
weight_decay:
    value: 0.1
