# Qwen模型API服务与跨服务器通信

本目录包含用于启动Qwen2.5-3B-Instruction-Best模型API服务和跨服务器通信的脚本。

## 文件说明

- `start_qwen_model_server.py`: Qwen模型API服务器，基于FastAPI实现
- `start_qwen_service.sh`: 启动Qwen模型API服务的脚本
- `qwen_api_client.py`: Qwen模型API客户端，用于在embodied_vln.py中调用Qwen模型
- `setup_ssh_tunnel.sh`: 设置SSH端口转发，实现跨服务器API通信

## 使用方法

### 1. 启动Qwen模型API服务

在本地服务器上启动Qwen模型API服务：

```bash
bash model/start_qwen_service.sh
```

服务启动后，可以通过以下URL访问：

- 健康检查: http://localhost:8000/health
- API接口: http://localhost:8000/v1/chat/completions

### 3. 设置SSH端口转发（跨服务器通信）

#### 从Linux服务器连接到Windows主机

如果需要从Linux服务器访问Windows主机上的服务，可以使用SSH端口转发：

1. 编辑`setup_ssh_tunnel.sh`脚本，设置正确的Windows主机IP、用户名和SSH密钥路径
2. 运行脚本：

```bash
bash model/setup_ssh_tunnel.sh
```

这将创建一个端口转发，将Linux服务器上的8000端口转发到Windows主机上的8000端口。

#### 从Windows主机连接到Linux服务器（推荐）

如果需要从Windows主机访问Linux服务器上的Qwen多模态模型API服务，可以在Windows上创建SSH隧道：

1. 创建一个名为`setup_ssh_tunnel.bat`的批处理文件，内容如下：

```batch
@echo off
echo 正在建立SSH隧道...
echo 本地端口: 8000 -> 远程端口: 8000
echo 连接到: batchcom@*************:30008

:: 使用SSH密钥建立隧道
ssh -N -L 8000:localhost:8000 -i "C:\Users\<USER>\.ssh\id_rsa_temp" -p 30008 batchcom@*************

:: 如果连接断开，显示提示
echo SSH隧道已断开连接。
echo 请按任意键退出...
pause > nul
```

2. 在Windows主机上双击运行`setup_ssh_tunnel.bat`脚本

这将创建一个端口转发，将Windows主机上的8000端口转发到Linux服务器上的8000端口。

### 4. 使用API客户端

#### 在本地测试API客户端

```bash
python model/qwen_api_client.py
```

#### 从其他服务器调用API

```bash
python model/qwen_api_client.py --remote http://<服务器IP>:8000
```

#### 分析图像

```bash
python model/qwen_api_client.py --remote http://<服务器IP>:8000 --image <图像路径> --prompt "描述这张图片"
```

### 5. 在模拟器中使用Qwen多模态模型

在本地服务器上运行导航评测，使用Qwen多模态模型：

```bash
python baseline_cv/embodied_vln.py --use_qwen --api_url http://localhost:8000
```

在模拟器服务器上运行导航评测，通过SSH端口转发使用本地的Qwen多模态模型：

```bash
python baseline_cv/embodied_vln.py --use_qwen --api_url http://localhost:8000
```

## 历史记录管理

`embodied_vln.py`中的`ActionGen`类已经实现了历史记录管理功能：

1. 历史动作和图像记录：保存历史动作和图像，用于构建提示
2. 动作压缩：合并连续相同的动作，例如将连续的"向前移动"合并为一条信息
3. 历史记录清空：当开始新的导航任务时，清空历史记录

## 测试API是否可用

在Windows主机上，可以使用以下Python代码测试API是否可用：

```python
# test_api.py
import requests
import json

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("API健康状态: 正常")
            print(response.json())
            return True
        else:
            print(f"API健康状态: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API健康状态: 连接失败 ({str(e)})")
        return False

def test_api_chat():
    """测试API聊天功能"""
    try:
        # 构建请求数据
        data = {
            "model": "Qwen2.5-3B-Instruction-Best",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }

        # 发送请求
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=data
        )

        if response.status_code == 200:
            result = response.json()
            print("API聊天功能: 正常")
            print("回复:", result["choices"][0]["message"]["content"])
            return True
        else:
            print(f"API聊天功能: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API聊天功能: 连接失败 ({str(e)})")
        return False

if __name__ == "__main__":
    print("正在测试API...")
    health_ok = test_api_health()

    if health_ok:
        print("\n正在测试聊天功能...")
        test_api_chat()

    print("\n测试完成")
```

使用方法：
1. 确保已经建立SSH隧道
2. 在Windows主机上运行：`python test_api.py`

## 多模态API接口

### 文本聊天接口

- 端点: `/v1/chat/completions`
- 方法: POST
- 请求格式:
  ```json
  {
    "model": "qwen2.5-vl-3b-instruct",
    "messages": [
      {"role": "system", "content": "You are a helpful assistant."},
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7,
    "max_tokens": 4096
  }
  ```

### 图像分析接口

- 端点: `/v1/chat/completions`
- 方法: POST
- 请求格式:
  ```json
  {
    "model": "qwen2.5-vl-3b-instruct",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "描述这张图片"},
          {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
        ]
      }
    ],
    "temperature": 0.7,
    "max_tokens": 1000
  }
  ```

## 模型选择

服务器会尝试按以下顺序加载模型：

1. 首先尝试加载本地的`/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct-Epoch18`多模态模型
2. 如果加载失败，尝试加载`/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct`
3. 如果仍然失败，尝试从Hugging Face下载`Qwen/Qwen2.5-VL-3B-Instruct`模型

## 注意事项

1. 确保模型路径正确，默认使用`/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct-Epoch18`
2. 服务器会在无法加载本地模型时自动从Hugging Face下载模型，确保网络连接正常
3. 如果使用SSH端口转发，确保SSH密钥配置正确
4. 在Windows上使用SSH隧道时，确保已安装OpenSSH客户端
5. 如果遇到问题，可以查看日志文件：
   - Qwen多模态模型API服务日志：`model/qwen_service.log`
   - SSH端口转发日志：`model/ssh_tunnel.log`
6. 如果在Windows上遇到SSH密钥权限问题，可能需要调整密钥文件的权限
7. 多模态模型需要更多GPU内存，确保有足够的显存（至少10GB）
8. 如果需要停止服务，请使用`stop_qwen_service.sh`脚本，而不是直接杀死进程
9. 服务器绑定到`0.0.0.0`，可以从任何网络接口访问，确保网络安全
