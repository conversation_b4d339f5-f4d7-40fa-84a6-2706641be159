#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型API测试脚本
用于测试文本聊天功能
"""

import sys
import argparse
import requests
import json
import logging
import time

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_chat(api_url, prompt, system_prompt="You are a helpful assistant.", temperature=0.7, max_tokens=2000):
    """测试聊天功能
    
    Args:
        api_url: API服务的URL
        prompt: 用户提示
        system_prompt: 系统提示
        temperature: 温度参数
        max_tokens: 最大生成token数
        
    Returns:
        聊天结果
    """
    # 构建请求数据
    logger.info(f"正在构建请求数据，提示词: {prompt}")
    request_data = {
        "model": "qwen2.5-vl-3b-instruct",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    # 发送请求
    logger.info(f"正在发送请求到: {api_url}/v1/chat/completions")
    start_time = time.time()
    try:
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            json=request_data,
            timeout=30
        )
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"请求成功，耗时: {time.time() - start_time:.2f}秒")
                return content
            else:
                logger.error(f"API响应格式不正确: {result}")
                return None
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        logger.error(f"请求过程中发生错误: {str(e)}")
        return None

def check_api_health(api_url):
    """检查API服务健康状态
    
    Args:
        api_url: API服务的URL
        
    Returns:
        服务是否健康
    """
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("API服务健康状态: 正常")
            return True
        else:
            logger.error(f"API服务健康状态: 异常 (状态码: {response.status_code})")
            return False
    except Exception as e:
        logger.error(f"检查API服务健康状态时出错: {str(e)}")
        return False

def get_server_info(api_url):
    """获取服务器信息
    
    Args:
        api_url: API服务的URL
        
    Returns:
        服务器信息
    """
    try:
        response = requests.get(f"{api_url}/server_info", timeout=5)
        if response.status_code == 200:
            info = response.json()
            logger.info(f"服务器信息: IP={info.get('server_ip')}, "
                       f"模型已加载={info.get('model_loaded')}, "
                       f"GPU={info.get('gpu_info')}")
            return info
        else:
            logger.error(f"获取服务器信息失败 (状态码: {response.status_code})")
            return None
    except Exception as e:
        logger.error(f"获取服务器信息时出错: {str(e)}")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Qwen2.5-VL-3B-Instruct 多模态模型API测试脚本")
    parser.add_argument("--api_url", type=str, default="http://localhost:8000", 
                        help="API服务的URL，默认为http://localhost:8000")
    parser.add_argument("--prompt", type=str, default="请介绍一下你自己，你是什么模型？", 
                        help="提示词，默认为'请介绍一下你自己，你是什么模型？'")
    parser.add_argument("--system_prompt", type=str, default="You are a helpful assistant.", 
                        help="系统提示，默认为'You are a helpful assistant.'")
    args = parser.parse_args()
    
    # 检查API服务健康状态
    logger.info(f"正在连接到API服务: {args.api_url}")
    if not check_api_health(args.api_url):
        logger.error("API服务不可用，请确保服务已启动")
        sys.exit(1)
    
    # 获取服务器信息
    get_server_info(args.api_url)
    
    # 测试聊天
    logger.info(f"正在测试聊天功能")
    logger.info(f"提示词: {args.prompt}")
    result = test_chat(args.api_url, args.prompt, args.system_prompt)
    
    if result:
        print("\n" + "="*80)
        print("聊天结果:")
        print("="*80)
        print(result)
        print("="*80)
        logger.info("测试完成")
    else:
        logger.error("聊天测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
