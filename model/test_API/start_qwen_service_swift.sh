#!/bin/bash
# 此脚本用于启动Qwen2.5-VL-3B-Instruct多模态模型服务
# 使用ms-swift框架进行推理
# 支持跨服务器API通信

# 设置环境变量，指定使用GPU 0
export CUDA_VISIBLE_DEVICES=0
export CUDA_HOME=/home/<USER>/wzy/cuda-12.4-toolkit
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 获取本机IP地址
get_local_ip() {
    ip addr show | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n 1
}

LOCAL_IP=$(get_local_ip)

# 检查服务是否已经运行
if pgrep -f "start_qwen_model_server_swift.py" > /dev/null; then
    echo "Qwen模型服务(Swift)已经在运行"
    echo "可通过 http://${LOCAL_IP}:8000 访问API服务"
    exit 0
fi

# 确保日志目录存在
mkdir -p model/logs

# 启动模型服务
echo "正在启动Qwen2.5-VL-3B-Instruct多模态模型服务(Swift)..."
echo "使用模型路径: /home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct-Epoch18"
nohup python model/start_qwen_model_server_swift.py > model/qwen_service_swift.log 2>&1 &

# 获取进程ID
PID=$!
echo "服务已启动，进程ID: $PID"

# 等待服务启动
echo "等待服务启动..."
for i in {1..60}; do
    echo -n "."
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "\nQwen2.5-VL-3B-Instruct多模态模型服务(Swift)启动成功"
        echo "API服务地址: http://${LOCAL_IP}:8000"
        echo "API端点: http://${LOCAL_IP}:8000/v1/chat/completions"
        echo "健康检查: http://${LOCAL_IP}:8000/health"
        echo "服务器信息: http://${LOCAL_IP}:8000/server_info"
        echo ""
        echo "可以使用以下命令测试API客户端:"
        echo "python model/test_qwen_vl_api.py --api_url http://${LOCAL_IP}:8000 --image /home/<USER>/wzy/NIPS/dataset_video_temp/60/3.png"
        echo ""
        echo "要从其他服务器访问，请使用:"
        echo "python model/test_qwen_vl_api.py --api_url http://${LOCAL_IP}:8000 --image <图像路径>"
        exit 0
    fi
    sleep 2
done

echo -e "\n服务启动超时，请检查日志文件..."
tail -n 50 model/qwen_service_swift.log

# 如果服务启动失败
echo "服务启动失败，请检查日志文件: model/qwen_service_swift.log"
exit 1
