#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Qwen多模态模型API是否可用
"""

import requests
import json
import base64
import os
from PIL import Image
import io

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("API健康状态: 正常")
            print(response.json())
            return True
        else:
            print(f"API健康状态: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API健康状态: 连接失败 ({str(e)})")
        return False

def test_text_chat():
    """测试API文本聊天功能"""
    try:
        # 构建请求数据
        data = {
            "model": "Qwen2.5-VL-3B-Instruct",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }

        # 发送请求
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=data
        )

        if response.status_code == 200:
            result = response.json()
            print("API文本聊天功能: 正常")
            print("回复:", result["choices"][0]["message"]["content"])
            return True
        else:
            print(f"API文本聊天功能: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API文本聊天功能: 连接失败 ({str(e)})")
        return False

def test_multimodal_chat():
    """测试API多模态聊天功能"""
    try:
        # 加载测试图像
        image_path = "test_image.jpg"  # 替换为实际的图像路径

        # 如果图像不存在，创建一个简单的测试图像
        if not os.path.exists(image_path):
            print(f"图像 {image_path} 不存在，创建一个测试图像...")
            img = Image.new('RGB', (100, 100), color = (73, 109, 137))
            img.save(image_path)

        # 将图像转换为base64
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

        # 构建请求数据
        data = {
            "model": "Qwen2.5-VL-3B-Instruct",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "text", "text": "What do you see in this image?"},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_string}"}}
                ]}
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }

        # 发送请求
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=data
        )

        if response.status_code == 200:
            result = response.json()
            print("API多模态聊天功能: 正常")
            print("回复:", result["choices"][0]["message"]["content"])
            return True
        else:
            print(f"API多模态聊天功能: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API多模态聊天功能: 连接失败 ({str(e)})")
        return False

def test_image_analysis_endpoint():
    """测试图像分析端点"""
    try:
        # 加载测试图像
        image_path = "test_image.jpg"  # 替换为实际的图像路径

        # 如果图像不存在，创建一个简单的测试图像
        if not os.path.exists(image_path):
            print(f"图像 {image_path} 不存在，创建一个测试图像...")
            img = Image.new('RGB', (100, 100), color = (73, 109, 137))
            img.save(image_path)

        # 将图像转换为base64
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')

        # 构建图像URL
        image_url = f"data:image/jpeg;base64,{encoded_string}"

        # 发送请求
        response = requests.post(
            "http://localhost:8000/v1/analyze_image",
            params={"image_url": image_url, "prompt": "详细描述这张图片"}
        )

        if response.status_code == 200:
            result = response.json()
            print("API图像分析端点: 正常")
            print("分析结果:", result["analysis"])
            return True
        else:
            print(f"API图像分析端点: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API图像分析端点: 连接失败 ({str(e)})")
        return False

if __name__ == "__main__":
    print("正在测试API...")
    health_ok = test_api_health()

    if health_ok:
        print("\n正在测试文本聊天功能...")
        text_ok = test_text_chat()

        if text_ok:
            print("\n正在测试多模态聊天功能...")
            multimodal_ok = test_multimodal_chat()

            if multimodal_ok:
                print("\n正在测试图像分析端点...")
                image_analysis_ok = test_image_analysis_endpoint()

    print("\n测试完成")
