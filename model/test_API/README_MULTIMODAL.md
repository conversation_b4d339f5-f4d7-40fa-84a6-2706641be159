# Qwen多模态模型API服务与跨服务器通信

本文档说明如何使用Qwen2.5-VL-3B-Instruct多模态模型API服务，以及如何实现跨服务器的API通信。

## 文件说明

- `start_qwen_model_server.py`: Qwen多模态模型API服务器，基于FastAPI实现
- `start_qwen_service.sh`: 启动Qwen多模态模型API服务的脚本
- `setup_ssh_tunnel.sh`: Linux服务器上设置SSH端口转发的脚本
- `setup_ssh_tunnel.bat`: Windows主机上设置SSH端口转发的批处理文件
- `test_multimodal_api.py`: 测试Qwen多模态模型API的脚本

## 使用方法

### 1. 启动和停止Qwen多模态模型API服务

#### 1.1 启动服务

在Linux服务器上启动Qwen多模态模型API服务：

```bash
bash model/start_qwen_service.sh
```

服务启动后，可以通过以下URL访问：

- 健康检查: http://localhost:8000/health
- API接口: http://localhost:8000/v1/chat/completions

#### 1.2 停止服务

有多种方法可以停止正在运行的Qwen模型服务：

**方法1：使用进程ID关闭**

```bash
# 查找服务进程ID
ps aux | grep "start_qwen_model_server.py"

# 终止进程
kill -9 进程ID
```

**方法2：使用pkill命令**

```bash
# 直接根据进程名称终止
pkill -f "start_qwen_model_server.py"
```

**方法3：创建并使用停止脚本**

创建`model/stop_qwen_service.sh`文件：

```bash
#!/bin/bash
# 此脚本用于停止Qwen多模态模型服务

echo "正在停止Qwen2.5-VL-3B-Instruct多模态模型服务..."

# 查找并终止模型服务进程
if pkill -f "start_qwen_model_server.py"; then
    echo "服务已成功停止"
else
    echo "未找到运行中的服务"
fi

# 如果有SSH隧道，也一并关闭
if pkill -f "ssh -N -L 8000:localhost:8000"; then
    echo "SSH隧道已关闭"
fi

echo "清理完成"
```

然后执行：

```bash
chmod +x model/stop_qwen_service.sh
bash model/stop_qwen_service.sh
```

**验证服务是否已停止**

```bash
curl -s http://localhost:8000/health || echo "服务已停止"
```

如果服务已停止，将显示"服务已停止"；如果服务仍在运行，将显示健康检查的响应。

#### 1.3 重启服务

如果需要重启服务（例如，在修改代码后），可以使用以下命令：

```bash
bash model/restart_qwen_service.sh
```

这个脚本会先停止现有服务，然后启动新的服务。

### 2. 设置SSH端口转发（跨服务器通信）

#### 从Linux服务器连接到另一台Linux服务器

如果需要从一台Linux服务器访问另一台Linux服务器上的服务，可以使用SSH端口转发：

1. 编辑`setup_ssh_tunnel.sh`脚本，设置正确的服务器IP、用户名和SSH密钥路径
2. 运行脚本：

```bash
bash model/setup_ssh_tunnel.sh
```

这将创建一个端口转发，将本地的8000端口转发到远程服务器上的8000端口。

#### 从Windows主机连接到Linux服务器

如果需要从Windows主机访问Linux服务器上的Qwen多模态模型API服务，可以在Windows上创建SSH隧道：

1. 编辑`setup_ssh_tunnel.bat`文件，设置正确的服务器IP、用户名和SSH密钥路径
2. 在Windows主机上双击运行`setup_ssh_tunnel.bat`脚本

这将创建一个端口转发，将Windows主机上的8000端口转发到Linux服务器上的8000端口。

**停止Windows上的SSH隧道**

要停止Windows上的SSH隧道，可以使用提供的批处理文件：

1. 在Windows主机上双击运行`stop_ssh_tunnel.bat`脚本

或者，您可以直接关闭SSH隧道的命令行窗口。

### 3. 测试API服务

在建立SSH隧道后，可以使用`test_multimodal_api.py`脚本测试API服务是否可用：

```bash
python model/test_multimodal_api.py
```

这将测试API的健康状态、文本聊天功能和多模态聊天功能。

### 4. 在应用程序中使用API

在应用程序中，可以使用以下URL访问API：

#### 4.1 聊天完成端点

```
http://localhost:8000/v1/chat/completions
```

API接受以下格式的请求：

```json
{
  "model": "Qwen2.5-VL-3B-Instruct",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "temperature": 0.7,
  "max_tokens": 100
}
```

对于多模态请求，可以使用以下格式：

```json
{
  "model": "Qwen2.5-VL-3B-Instruct",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": [
      {"type": "text", "text": "What do you see in this image?"},
      {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
    ]}
  ],
  "temperature": 0.7,
  "max_tokens": 300
}
```

#### 4.2 图像分析端点

```
http://localhost:8000/v1/analyze_image
```

这个端点专门用于图像分析，接受以下参数：

- `image_url`: 图像的URL，可以是网络URL或者base64编码的图像数据
- `prompt`: 分析图像的提示，默认为"描述这张图片"

示例请求：

```
POST http://localhost:8000/v1/analyze_image?image_url=data:image/jpeg;base64,...&prompt=详细描述这张图片
```

响应格式：

```json
{
  "analysis": "这是一张蓝色背景的图片，显示了..."
}
```

## 注意事项

1. 确保服务器上已安装所需的依赖：
   - Python 3.8+
   - PyTorch
   - FastAPI
   - Uvicorn
   - ms-swift

2. 如果使用SSH端口转发，确保SSH密钥配置正确，并且有权限访问远程服务器。

3. 如果遇到问题，可以查看日志文件：
   - Qwen多模态模型API服务日志：`model/qwen_service.log`
   - SSH端口转发日志：`model/ssh_tunnel.log`

4. 如果在Windows上遇到SSH密钥权限问题，可能需要调整密钥文件的权限。

5. 如果遇到模型加载错误，可能是因为模型路径不正确，请检查`start_qwen_model_server.py`文件中的`load_model`函数，确保模型路径正确。

6. 多模态模型需要更多的GPU内存，请确保服务器上有足够的GPU内存。
