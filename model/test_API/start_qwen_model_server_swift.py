#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型服务器
使用ms-swift框架进行推理
提供API接口，用于跨服务器调用Qwen多模态模型
支持处理图像和文本输入
支持远程服务器通过API调用
"""

import os
import torch
import uvicorn
import logging
import json
import base64
import requests
import socket
import time
from io import BytesIO
from PIL import Image
from fastapi import FastAPI, HTTPException, Request, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Union
from contextlib import asynccontextmanager

# 设置环境变量，设置最大模型长度为16K
os.environ['MAX_MODEL_LEN'] = '16384'

# 导入ms-swift框架相关模块
from swift.llm import (
    get_model_tokenizer,
    InferRequest,
    PtEngine,
    RequestConfig,
    get_template
)
from swift.llm.template import load_image, load_file

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler("model/qwen_service_swift.log"),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger(__name__)

# 设置环境变量，指定使用的GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# 获取本机IP地址，用于显示服务信息
def get_local_ip():
    try:
        # 创建一个临时socket连接来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        logger.warning(f"无法获取本机IP地址: {str(e)}")
        return "127.0.0.1"

# 全局变量，用于存储模型、分词器和引擎
model = None
tokenizer = None
engine = None
template = None

# 定义生命周期管理器
@asynccontextmanager
async def lifespan(app):
    # 启动时加载模型
    logger.info("服务启动中，开始加载模型...")
    success = load_model()
    if not success:
        logger.error("无法加载模型，服务可能无法正常工作")
    else:
        local_ip = get_local_ip()
        logger.info(f"模型加载成功，服务可在 http://{local_ip}:8000 访问")
        logger.info(f"API端点: http://{local_ip}:8000/v1/chat/completions")
        logger.info(f"健康检查: http://{local_ip}:8000/health")
    yield
    # 关闭时清理资源
    logger.info("服务关闭，清理资源...")
    global model, tokenizer, engine, template
    model = None
    tokenizer = None
    engine = None
    template = None
    torch.cuda.empty_cache()  # 清理GPU内存

# 创建FastAPI应用，使用生命周期管理器
app = FastAPI(
    title="Qwen2.5-VL-3B-Instruct API Server (Swift)",
    description="跨服务器多模态模型API服务，使用ms-swift框架，支持图像和文本处理",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件，允许跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 添加全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {str(exc)}")
    import traceback
    logger.error(f"详细错误: {traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"服务器内部错误: {str(exc)}"}
    )

# 定义请求模型
class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[Dict[str, Any]]]

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 4096

# 定义响应模型
class ChatCompletionChoice(BaseModel):
    message: ChatMessage
    index: int = 0

class ChatCompletionResponse(BaseModel):
    id: str = "qwen-model-response"
    object: str = "chat.completion"
    choices: List[ChatCompletionChoice]

# 辅助函数：处理图像
def process_image_url(image_url):
    """处理图像URL，支持base64和HTTP URL"""
    if image_url.startswith('data:'):
        # 处理base64编码的图像
        base64_data = image_url.split(',')[1]
        image_bytes = base64.b64decode(base64_data)
        image = Image.open(BytesIO(image_bytes))
    else:
        # 处理HTTP URL
        response = requests.get(image_url)
        image = Image.open(BytesIO(response.content))
    return image

# 模型加载函数
def load_model(model_name="/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct"):
    global model, tokenizer, engine, template

    logger.info(f"加载本地Qwen多模态模型: {model_name}")
    start_time = time.time()

    try:
        # 使用ms-swift框架加载模型和分词器
        logger.info("使用ms-swift框架加载多模态模型和分词器")

        # 加载模型和分词器
        model, tokenizer = get_model_tokenizer(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        logger.info("模型和分词器加载成功")

        # 获取模板
        template = get_template("qwen2_5_vl", tokenizer)
        logger.info("模板加载成功")

        # 设置最大模型长度
        model.config.max_model_len = 16384  # 设置为16K
        logger.info("模型最大长度设置为16K")

        # 创建引擎
        engine = PtEngine.from_model_template(model, template)
        logger.info("引擎创建成功")

        logger.info(f"模型类型: {type(model).__name__}")
        logger.info(f"模型加载耗时: {time.time() - start_time:.2f}秒")

        # 测试模型是否可用
        test_messages = [
            {"role": "user", "content": "Hello, how are you?"}
        ]
        infer_request = InferRequest(messages=test_messages)
        request_config = RequestConfig(max_tokens=10, temperature=0.7)

        response = engine.infer([infer_request], request_config)[0]
        test_response = response.choices[0].message.content

        logger.info(f"测试响应: {test_response}")
        logger.info("多模态模型加载并测试成功")
        return True
    except Exception as e:
        logger.error(f"多模态模型加载失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

        # 尝试加载备用路径
        try:
            backup_path = "/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct"
            logger.info(f"尝试从备用路径加载模型: {backup_path}")

            # 加载模型和分词器
            model, tokenizer = get_model_tokenizer(
                backup_path,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            logger.info("备用路径模型和分词器加载成功")

            # 获取模板
            template = get_template("qwen2_5_vl", tokenizer)
            logger.info("模板加载成功")

            # 设置最大模型长度
            model.config.max_model_len = 16384  # 设置为16K
            logger.info("模型最大长度设置为16K")

            # 创建引擎
            engine = PtEngine.from_model_template(model, template)
            logger.info("引擎创建成功")

            # 测试模型是否可用
            test_messages = [
                {"role": "user", "content": "Hello, how are you?"}
            ]
            infer_request = InferRequest(messages=test_messages)
            request_config = RequestConfig(max_tokens=10, temperature=0.7)

            response = engine.infer([infer_request], request_config)[0]
            test_response = response.choices[0].message.content

            logger.info(f"测试响应: {test_response}")
            logger.info("备用路径模型加载并测试成功")
            return True
        except Exception as backup_e:
            logger.error(f"备用路径模型加载失败: {str(backup_e)}")

            # 最后尝试从Hugging Face加载模型
            try:
                hf_path = "Qwen/Qwen2.5-VL-3B-Instruct"  # 使用VL模型
                logger.info(f"尝试从Hugging Face加载模型: {hf_path}")

                # 加载模型和分词器
                model, tokenizer = get_model_tokenizer(
                    hf_path,
                    torch_dtype=torch.float16,
                    device_map="auto"
                )
                logger.info("Hugging Face模型和分词器加载成功")

                # 获取模板
                template = get_template("qwen2_5_vl", tokenizer)
                logger.info("模板加载成功")

                # 设置最大模型长度
                model.config.max_model_len = 16384  # 设置为16K
                logger.info("模型最大长度设置为16K")

                # 创建引擎
                engine = PtEngine.from_model_template(model, template)
                logger.info("引擎创建成功")

                # 测试模型是否可用
                test_messages = [
                    {"role": "user", "content": "Hello, how are you?"}
                ]
                infer_request = InferRequest(messages=test_messages)
                request_config = RequestConfig(max_tokens=10, temperature=0.7)

                response = engine.infer([infer_request], request_config)[0]
                test_response = response.choices[0].message.content

                logger.info(f"测试响应: {test_response}")
                logger.info("Hugging Face模型加载并测试成功")
                return True
            except Exception as hf_e:
                logger.error(f"Hugging Face模型加载失败: {str(hf_e)}")
                logger.error(f"详细错误: {traceback.format_exc()}")
                return False

# 定义聊天完成端点
@app.post("/v1/chat/completions", response_model=ChatCompletionResponse)
async def chat_completion(request: ChatCompletionRequest):
    global model, tokenizer, engine, template

    if model is None or tokenizer is None or engine is None:
        raise HTTPException(status_code=500, detail="模型未加载")

    try:
        # 转换请求消息为模型接受的格式
        messages = []
        for msg in request.messages:
            if isinstance(msg.content, str):
                # 纯文本消息
                messages.append({"role": msg.role, "content": msg.content})
            else:
                # 多模态消息
                content_list = msg.content
                text_parts = []
                image_urls = []

                # 提取文本和图像URL
                for item in content_list:
                    if item.get("type") == "text":
                        text_parts.append(item.get("text", ""))
                    elif item.get("type") == "image_url":
                        image_url = item.get("image_url", {}).get("url", "")
                        if image_url:
                            image_urls.append(image_url)

                # 合并文本部分
                text_content = " ".join(text_parts)

                # 如果有图像，处理多模态内容
                if image_urls:
                    logger.info("处理多模态输入")
                    # 加载图像
                    images = []
                    for url in image_urls:
                        try:
                            image = process_image_url(url)
                            images.append(image)
                        except Exception as e:
                            logger.error(f"处理图像失败: {str(e)}")

                    # 构建多模态消息
                    if images:
                        # 使用swift的模板处理多模态内容
                        content = []
                        content.append({"type": "text", "text": text_content})
                        for image in images:
                            content.append({"type": "image", "image": image})

                        messages.append({"role": msg.role, "content": content})
                    else:
                        # 如果图像处理失败，只使用文本
                        messages.append({"role": msg.role, "content": text_content})
                else:
                    # 纯文本消息
                    messages.append({"role": msg.role, "content": text_content})

        # 创建推理请求
        infer_request = InferRequest(messages=messages)

        # 创建请求配置
        request_config = RequestConfig(
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )

        # 执行推理
        logger.info(f"开始推理，消息数量: {len(messages)}")
        start_time = time.time()
        response = engine.infer([infer_request], request_config)[0]

        # 检查响应
        if not response or not response.choices or len(response.choices) == 0:
            logger.error("模型返回空响应")
            raise HTTPException(status_code=500, detail="模型返回空响应")

        # 获取输出文本
        output_text = response.choices[0].message.content
        logger.info(f"推理完成，耗时: {time.time() - start_time:.2f}秒，输出长度: {len(output_text)}")

        # 构建响应
        choice = ChatCompletionChoice(
            message=ChatMessage(role="assistant", content=output_text.strip())
        )

        return ChatCompletionResponse(choices=[choice])

    except Exception as e:
        logger.error(f"生成失败: {str(e)}")
        # 记录更详细的错误信息
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

# 图像分析端点
@app.post("/v1/analyze_image")
async def analyze_image(image_url: str, prompt: str = "描述这张图片"):
    global model, tokenizer, engine, template

    if model is None or tokenizer is None or engine is None:
        raise HTTPException(status_code=500, detail="模型未加载")

    try:
        # 处理图像
        image = process_image_url(image_url)

        # 构建多模态消息
        content = []
        content.append({"type": "text", "text": prompt})
        content.append({"type": "image", "image": image})

        messages = [{"role": "user", "content": content}]

        # 创建推理请求
        infer_request = InferRequest(messages=messages)

        # 创建请求配置
        request_config = RequestConfig(
            max_tokens=1000,
            temperature=0.7
        )

        # 执行推理
        logger.info(f"开始图像分析，提示词: {prompt}")
        start_time = time.time()
        response = engine.infer([infer_request], request_config)[0]

        # 检查响应
        if not response or not response.choices or len(response.choices) == 0:
            logger.error("模型返回空响应")
            raise HTTPException(status_code=500, detail="模型返回空响应")

        # 获取输出文本
        output_text = response.choices[0].message.content
        logger.info(f"图像分析完成，耗时: {time.time() - start_time:.2f}秒，输出长度: {len(output_text)}")

        return {"analysis": output_text.strip()}

    except Exception as e:
        logger.error(f"图像分析失败: {str(e)}")
        # 记录更详细的错误信息
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"图像分析失败: {str(e)}")

# 健康检查端点
@app.get("/health")
async def health_check():
    if model is None or tokenizer is None or engine is None:
        raise HTTPException(status_code=503, detail="模型未加载")
    return {"status": "healthy", "model_loaded": True}

# 服务器信息端点
@app.get("/server_info")
async def server_info():
    """返回服务器信息，包括IP地址、模型状态等"""
    local_ip = get_local_ip()
    gpu_info = ""
    try:
        # 获取GPU信息
        if torch.cuda.is_available():
            gpu_info = f"{torch.cuda.get_device_name(0)} ({torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB)"
        else:
            gpu_info = "No GPU available"
    except Exception as e:
        gpu_info = f"Error getting GPU info: {str(e)}"

    return {
        "server_ip": local_ip,
        "api_endpoint": f"http://{local_ip}:8000/v1/chat/completions",
        "health_endpoint": f"http://{local_ip}:8000/health",
        "model_loaded": model is not None,
        "model_type": type(model).__name__ if model else "None",
        "engine_type": type(engine).__name__ if engine else "None",
        "gpu_info": gpu_info,
        "cuda_visible_devices": os.environ.get("CUDA_VISIBLE_DEVICES", "Not set")
    }

# 主函数
if __name__ == "__main__":
    try:
        # 获取本机IP地址
        local_ip = get_local_ip()
        logger.info(f"本机IP地址: {local_ip}")

        # 在端口8000上启动服务，绑定到所有网络接口以允许远程访问
        logger.info("启动Qwen2.5-VL-3B-Instruct多模态模型API服务，使用ms-swift框架...")
        logger.info(f"服务启动后可通过 http://{local_ip}:8000 访问")
        logger.info("按Ctrl+C停止服务")

        # 启动服务
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        logger.info("接收到停止信号，服务正在关闭...")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
