#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Qwen模型API是否可用
"""

import requests
import json

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("API健康状态: 正常")
            print(response.json())
            return True
        else:
            print(f"API健康状态: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API健康状态: 连接失败 ({str(e)})")
        return False

def test_api_chat():
    """测试API聊天功能"""
    try:
        # 构建请求数据
        data = {
            "model": "Qwen2.5-3B-Instruction-Best",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        # 发送请求
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("API聊天功能: 正常")
            print("回复:", result["choices"][0]["message"]["content"])
            return True
        else:
            print(f"API聊天功能: 异常 (状态码: {response.status_code})")
            print(response.text)
            return False
    except Exception as e:
        print(f"API聊天功能: 连接失败 ({str(e)})")
        return False

if __name__ == "__main__":
    print("正在测试API...")
    health_ok = test_api_health()
    
    if health_ok:
        print("\n正在测试聊天功能...")
        test_api_chat()
    
    print("\n测试完成")
