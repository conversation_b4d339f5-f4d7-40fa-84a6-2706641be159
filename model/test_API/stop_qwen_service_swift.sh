#!/bin/bash
# 此脚本用于停止Qwen2.5-VL-3B-Instruct多模态模型服务(Swift)

echo "正在停止Qwen2.5-VL-3B-Instruct多模态模型服务(Swift)..."

# 查找服务进程ID
PID=$(pgrep -f "start_qwen_model_server_swift.py")

if [ -n "$PID" ]; then
    echo "找到服务进程，PID: $PID"
    
    # 尝试优雅地终止进程
    echo "正在优雅地终止进程..."
    kill $PID
    
    # 等待进程终止
    echo "等待进程终止..."
    for i in {1..10}; do
        if ! ps -p $PID > /dev/null; then
            echo "进程已终止"
            break
        fi
        echo -n "."
        sleep 1
    done
    
    # 如果进程仍在运行，强制终止
    if ps -p $PID > /dev/null; then
        echo "进程未响应，正在强制终止..."
        kill -9 $PID
        sleep 1
    fi
    
    echo "服务已停止"
else
    echo "未找到运行中的服务"
fi

# 如果有SSH隧道，也一并关闭
if pkill -f "ssh -N -L 8000:localhost:8000"; then
    echo "SSH隧道已关闭"
fi

# 清理GPU内存
echo "正在清理GPU内存..."
nvidia-smi --gpu-reset 2>/dev/null || echo "无法重置GPU，可能需要手动清理"

echo "清理完成"

# 验证服务是否已停止
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "警告：服务似乎仍在运行，可能需要手动终止进程"
    echo "可以使用以下命令查看进程："
    echo "ps aux | grep start_qwen_model_server_swift.py"
    echo "可以使用以下命令强制终止所有相关进程："
    echo "pkill -9 -f start_qwen_model_server_swift.py"
else
    echo "验证完成：服务已成功停止"
fi

# 显示GPU状态
echo "当前GPU状态："
nvidia-smi
