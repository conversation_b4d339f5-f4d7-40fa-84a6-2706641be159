#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型API综合测试脚本
可以测试文本聊天和图像分析功能
"""

import os
import sys
import argparse
import requests
import json
import base64
import logging
from PIL import Image
import time

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def encode_image_to_base64(image_path):
    """将图像编码为base64字符串
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        base64编码的图像字符串
    """
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{encoded_string}"
    except Exception as e:
        logger.error(f"图像编码失败: {str(e)}")
        return None

def test_image_analysis(api_url, image_path, prompt="请详细描述这张图片中的内容"):
    """测试图像分析功能
    
    Args:
        api_url: API服务的URL
        image_path: 图像文件路径
        prompt: 提示词
        
    Returns:
        分析结果
    """
    # 检查图像文件是否存在
    if not os.path.exists(image_path):
        logger.error(f"图像文件不存在: {image_path}")
        return None
    
    # 编码图像
    logger.info(f"正在编码图像: {image_path}")
    base64_image = encode_image_to_base64(image_path)
    if not base64_image:
        return None
    
    # 构建请求数据
    logger.info(f"正在构建请求数据，提示词: {prompt}")
    request_data = {
        "model": "qwen2.5-vl-3b-instruct",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": base64_image}}
                ]
            }
        ],
        "temperature": 0.7,
        "max_tokens": 2000
    }
    
    # 发送请求
    logger.info(f"正在发送请求到: {api_url}/v1/chat/completions")
    start_time = time.time()
    try:
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            json=request_data,
            timeout=60
        )
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"请求成功，耗时: {time.time() - start_time:.2f}秒")
                return content
            else:
                logger.error(f"API响应格式不正确: {result}")
                return None
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        logger.error(f"请求过程中发生错误: {str(e)}")
        return None

def test_chat(api_url, prompt, system_prompt="You are a helpful assistant."):
    """测试聊天功能
    
    Args:
        api_url: API服务的URL
        prompt: 用户提示
        system_prompt: 系统提示
        
    Returns:
        聊天结果
    """
    # 构建请求数据
    logger.info(f"正在构建请求数据，提示词: {prompt}")
    request_data = {
        "model": "qwen2.5-vl-3b-instruct",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 2000
    }
    
    # 发送请求
    logger.info(f"正在发送请求到: {api_url}/v1/chat/completions")
    start_time = time.time()
    try:
        response = requests.post(
            f"{api_url}/v1/chat/completions",
            json=request_data,
            timeout=30
        )
        
        # 检查响应
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                logger.info(f"请求成功，耗时: {time.time() - start_time:.2f}秒")
                return content
            else:
                logger.error(f"API响应格式不正确: {result}")
                return None
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None
    except Exception as e:
        logger.error(f"请求过程中发生错误: {str(e)}")
        return None

def check_api_health(api_url):
    """检查API服务健康状态
    
    Args:
        api_url: API服务的URL
        
    Returns:
        服务是否健康
    """
    try:
        response = requests.get(f"{api_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info("API服务健康状态: 正常")
            return True
        else:
            logger.error(f"API服务健康状态: 异常 (状态码: {response.status_code})")
            return False
    except Exception as e:
        logger.error(f"检查API服务健康状态时出错: {str(e)}")
        return False

def get_server_info(api_url):
    """获取服务器信息
    
    Args:
        api_url: API服务的URL
        
    Returns:
        服务器信息
    """
    try:
        response = requests.get(f"{api_url}/server_info", timeout=5)
        if response.status_code == 200:
            info = response.json()
            logger.info(f"服务器信息: IP={info.get('server_ip')}, "
                       f"模型已加载={info.get('model_loaded')}, "
                       f"GPU={info.get('gpu_info')}")
            return info
        else:
            logger.error(f"获取服务器信息失败 (状态码: {response.status_code})")
            return None
    except Exception as e:
        logger.error(f"获取服务器信息时出错: {str(e)}")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Qwen2.5-VL-3B-Instruct 多模态模型API综合测试脚本")
    parser.add_argument("--api_url", type=str, default="http://localhost:8000", 
                        help="API服务的URL，默认为http://localhost:8000")
    parser.add_argument("--mode", type=str, choices=["chat", "image", "all"], default="all", 
                        help="测试模式，可选值: chat(仅聊天), image(仅图像), all(全部)")
    parser.add_argument("--image", type=str, default="/home/<USER>/wzy/NIPS/dataset_video_temp/60/3.png", 
                        help="要分析的图像文件路径")
    parser.add_argument("--image_prompt", type=str, default="请详细描述这张图片中的内容", 
                        help="图像分析提示词")
    parser.add_argument("--chat_prompt", type=str, default="请介绍一下你自己，你是什么模型？", 
                        help="聊天提示词")
    args = parser.parse_args()
    
    # 检查API服务健康状态
    logger.info(f"正在连接到API服务: {args.api_url}")
    if not check_api_health(args.api_url):
        logger.error("API服务不可用，请确保服务已启动")
        sys.exit(1)
    
    # 获取服务器信息
    get_server_info(args.api_url)
    
    # 根据模式执行测试
    if args.mode in ["chat", "all"]:
        # 测试聊天
        logger.info(f"正在测试聊天功能")
        logger.info(f"提示词: {args.chat_prompt}")
        chat_result = test_chat(args.api_url, args.chat_prompt)
        
        if chat_result:
            print("\n" + "="*80)
            print("聊天结果:")
            print("="*80)
            print(chat_result)
            print("="*80)
            logger.info("聊天测试完成")
        else:
            logger.error("聊天测试失败")
    
    if args.mode in ["image", "all"]:
        # 测试图像分析
        logger.info(f"正在分析图像: {args.image}")
        logger.info(f"提示词: {args.image_prompt}")
        image_result = test_image_analysis(args.api_url, args.image, args.image_prompt)
        
        if image_result:
            print("\n" + "="*80)
            print("图像分析结果:")
            print("="*80)
            print(image_result)
            print("="*80)
            logger.info("图像分析测试完成")
        else:
            logger.error("图像分析测试失败")
    
    logger.info("所有测试完成")

if __name__ == "__main__":
    main()
