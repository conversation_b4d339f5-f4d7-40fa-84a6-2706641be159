#!/bin/bash
# 此脚本用于设置SSH端口转发，实现跨服务器API通信

# 配置参数
REMOTE_HOST="*************"  # 远程服务器IP
REMOTE_USER="batchcom"       # 远程服务器用户名
REMOTE_PORT=30008            # 远程服务器SSH端口
LOCAL_PORT=8000              # 本地API服务端口
REMOTE_PORT=8000             # 远程转发端口
SSH_KEY="/home/<USER>/wzy/NIPS/watch_dog/id_rsa_8"  # SSH密钥路径

# 检查是否已经有SSH端口转发在运行
if pgrep -f "ssh -N -L $LOCAL_PORT:localhost:$REMOTE_PORT" > /dev/null; then
    echo "SSH端口转发已经在运行"
    exit 0
fi

# 启动SSH端口转发
echo "正在启动SSH端口转发..."
echo "本地端口: $LOCAL_PORT -> 远程端口: $REMOTE_PORT"
echo "连接到: $REMOTE_USER@$REMOTE_HOST:$REMOTE_PORT"

# 使用nohup在后台运行SSH端口转发
nohup ssh -N -L $LOCAL_PORT:localhost:$REMOTE_PORT -i $SSH_KEY -p $REMOTE_PORT $REMOTE_USER@$REMOTE_HOST > model/ssh_tunnel.log 2>&1 &

# 获取进程ID
PID=$!
echo "SSH端口转发已启动，进程ID: $PID"

# 等待SSH端口转发建立
echo "等待SSH端口转发建立..."
for i in {1..10}; do
    echo -n "."
    if nc -z localhost $LOCAL_PORT > /dev/null 2>&1; then
        echo -e "\nSSH端口转发已建立"
        exit 0
    fi
    sleep 1
done

echo -e "\nSSH端口转发建立超时，请检查日志文件: model/ssh_tunnel.log"
exit 1
