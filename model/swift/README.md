# Qwen2.5-VL-3B-Instruct 多模态模型API服务 (Swift版)

本目录包含使用ms-swift框架实现的Qwen2.5-VL-3B-Instruct多模态模型API服务，支持跨服务器通信，特别是Windows到Linux的连接。该服务解决了原始transformers框架在处理多模态输入时的问题，提供了更稳定的图像和文本处理能力。

## 文件说明

- `start_qwen_model_server_swift.py`: 主服务器代码，使用ms-swift框架和FastAPI实现
- `start_qwen_service_swift.sh`: 启动服务的脚本
- `stop_qwen_service_swift.sh`: 停止服务的脚本
- `test_qwen_vl_api.py`: 测试多模态API功能的脚本
- `logs/`: 日志文件目录
- `windows/`: Windows客户端脚本目录
  - `qwen_api_client.py`: Windows端API客户端
  - `test_qwen_api.py`: Windows端测试脚本
  - `setup_ssh_tunnel.bat`: 设置SSH隧道的批处理脚本
  - `integration_guide.md`: 模拟器集成指南

## 服务器端使用方法 (Linux)

### 启动服务

在Linux服务器上启动多模态API服务：

```bash
cd /home/<USER>/wzy/NIPS
bash model/swift/start_qwen_service_swift.sh
```

服务启动后，将在以下端点提供API：
- API服务地址: http://<服务器IP>:8000
- API端点: http://<服务器IP>:8000/v1/chat/completions
- 图像分析端点: http://<服务器IP>:8000/v1/analyze_image
- 健康检查: http://<服务器IP>:8000/health
- 服务器信息: http://<服务器IP>:8000/server_info

启动脚本会自动显示服务器IP地址和API端点信息，方便您进行远程连接。

### 测试服务

在Linux服务器上测试多模态API服务：

```bash
cd /home/<USER>/wzy/NIPS
python model/swift/test_qwen_vl_api.py
```

您可以指定要分析的图像：

```bash
python model/swift/test_qwen_vl_api.py --image /home/<USER>/wzy/NIPS/dataset_video_temp/60/3.png
```

### 停止服务

在Linux服务器上停止多模态API服务：

```bash
cd /home/<USER>/wzy/NIPS
bash model/swift/stop_qwen_service_swift.sh
```

停止脚本会优雅地终止服务进程，并清理GPU内存。

## 客户端使用方法 (Windows)

### 准备工作

在Windows电脑上使用API服务前，需要完成以下准备工作：

1. 确保已安装Python 3.8+和必要的依赖包：
   ```bash
   pip install requests pillow
   ```

2. 从Linux服务器复制以下文件到Windows电脑上的工作目录：
   - `model/swift/windows/qwen_api_client.py`
   - `model/swift/windows/test_qwen_api.py`
   - `model/swift/windows/setup_ssh_tunnel.bat`

### 设置SSH隧道

Windows客户端需要通过SSH隧道连接到Linux服务器。我们提供了一个批处理脚本来设置SSH隧道：

1. 编辑`setup_ssh_tunnel.bat`文件，确认服务器信息正确：
   ```
   服务器: *************
   端口: 30008
   用户名: batchcom
   密钥文件: C:\Users\<USER>\.ssh\id_rsa_temp
   ```

2. 双击运行`setup_ssh_tunnel.bat`脚本，它将创建一个SSH隧道，将Windows上的8000端口转发到Linux服务器上的8000端口

3. 保持SSH隧道窗口打开，隧道才能保持活动状态

### 测试API连接

使用提供的Python脚本测试API连接：

```bash
# 在Windows上运行
python test_qwen_api.py
```

您也可以指定图像文件进行测试：

```bash
python test_qwen_api.py --image path/to/your/image.jpg
```

如果一切正常，您将看到模型的响应。

### 在模拟器中使用API

我们已经修改了`baseline_cv/embodied_vln.py`文件，使其能够通过SSH隧道连接到API服务。您可以使用以下命令运行模拟器：

```bash
cd /home/<USER>/wzy/NIPS
python baseline_cv/embodied_vln.py --use_qwen --api_url http://localhost:8000 --use_swift
```

或者使用提供的运行脚本：

```bash
cd /home/<USER>/wzy/NIPS
bash baseline_cv/run_embodied_vln.sh --use-swift
```

## 跨服务器连接详情

### 服务器信息

- **Linux服务器**: *************:30008
- **用户名**: batchcom
- **身份文件**: C:\Users\<USER>\.ssh\id_rsa_temp
- **API端口**: 8000

### 连接架构

```
+----------------+                +------------------+
| Windows电脑     |                | Linux服务器      |
|                |                |                  |
|  模拟器         |                |  Qwen API服务    |
|  (客户端)       |                |  (服务器端)      |
|                |                |                  |
|  localhost:8000+<--- SSH隧道 --->+  localhost:8000 |
+----------------+                +------------------+
```

### 连接方式

1. **SSH隧道**: 将Windows上的本地端口8000转发到Linux服务器上的8000端口
   ```
   ssh -N -L 8000:localhost:8000 -i "C:\Users\<USER>\.ssh\id_rsa_temp" -p 30008 batchcom@*************
   ```

2. **API调用**: 通过本地端口8000访问API服务
   ```python
   client = QwenAPIClient(api_url="http://localhost:8000")
   ```

### 网络要求

- Windows电脑必须能够通过SSH连接到Linux服务器
- Linux服务器上的防火墙必须允许SSH连接
- 端口30008必须对Windows电脑开放
- 确保SSH密钥文件权限正确（Windows上可能需要调整权限）

## 故障排除

### 1. 无法建立SSH隧道

**症状**: 运行`setup_ssh_tunnel.bat`时出现连接错误

**解决方案**:
- 检查SSH密钥文件是否存在且路径正确
- 确认服务器地址和端口是否正确
- 确认Windows上已安装OpenSSH客户端
- 尝试手动运行SSH命令以查看详细错误信息：
  ```
  ssh -v -N -L 8000:localhost:8000 -i "C:\Users\<USER>\.ssh\id_rsa_temp" -p 30008 batchcom@*************
  ```

### 2. API连接失败

**症状**: 测试脚本无法连接到API服务

**解决方案**:
- 确认SSH隧道是否正常运行（隧道窗口应保持打开状态）
- 检查Linux服务器上的API服务是否已启动：
  ```bash
  ps aux | grep start_qwen_model_server_swift.py
  ```
- 在Linux服务器上测试API服务是否可访问：
  ```bash
  curl http://localhost:8000/health
  ```
- 在Windows上测试本地端口转发是否正常：
  ```bash
  curl http://localhost:8000/health
  ```

### 3. 模型响应错误

**症状**: API返回错误或空响应

**解决方案**:
- 检查日志文件：
  ```bash
  tail -f model/swift/logs/qwen_service_swift.log
  ```
- 确认模型已正确加载
- 尝试重启API服务：
  ```bash
  bash model/swift/stop_qwen_service_swift.sh
  bash model/swift/start_qwen_service_swift.sh
  ```
- 检查GPU内存是否足够：
  ```bash
  nvidia-smi
  ```

### 4. 多模态输入问题

**症状**: 图像分析功能不工作

**解决方案**:
- 确认图像文件存在且可读
- 检查图像格式是否支持（推荐使用JPG或PNG格式）
- 尝试使用较小的图像文件（大图像可能导致内存问题）
- 检查API请求格式是否正确

## 技术细节

### API接口

1. **文本聊天接口**:
   - 端点: `/v1/chat/completions`
   - 方法: POST
   - 请求格式:
     ```json
     {
       "model": "qwen2.5-vl-3b-instruct",
       "messages": [
         {"role": "system", "content": "You are a helpful assistant."},
         {"role": "user", "content": "Hello, how are you?"}
       ],
       "temperature": 0.7,
       "max_tokens": 4096
     }
     ```

2. **图像分析接口**:
   - 端点: `/v1/chat/completions`
   - 方法: POST
   - 请求格式:
     ```json
     {
       "model": "qwen2.5-vl-3b-instruct",
       "messages": [
         {
           "role": "user",
           "content": [
             {"type": "text", "text": "描述这张图片"},
             {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
           ]
         }
       ],
       "temperature": 0.7,
       "max_tokens": 1000
     }
     ```

### 模型信息

- **模型名称**: Qwen2.5-VL-3B-Instruct
- **模型路径**: `/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct-Epoch18`
- **框架**: ms-swift
- **量化**: float16
- **最大上下文长度**: 16384 tokens

## 总结

本项目使用ms-swift框架实现了Qwen2.5-VL-3B-Instruct多模态模型的API服务，解决了原始transformers框架在处理多模态输入时的问题。主要特点包括：

1. **稳定的多模态处理**: 使用ms-swift框架提供稳定的图像和文本处理能力
2. **跨服务器通信**: 支持从Windows电脑通过SSH隧道连接到Linux服务器上的API服务
3. **完整的工具链**: 提供了服务器端和客户端的完整工具链，包括启动、停止、测试和集成脚本
4. **灵活的API接口**: 支持纯文本聊天和图像分析功能
5. **详细的文档**: 提供了详细的使用说明、故障排除指南和技术细节

通过这套解决方案，您可以在模拟器中使用Qwen2.5-VL-3B-Instruct多模态模型进行视觉语言导航任务，实现跨服务器的API通信。
