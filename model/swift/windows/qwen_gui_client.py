#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型图形界面客户端
用于从Windows连接到Linux服务器上的API服务
支持多模态对话、历史对话保存和加载、对话重置、消息重新编辑等功能
"""

import os
import sys
import json
import time
import logging
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from tkinter.font import Font
from PIL import Image, ImageTk
import datetime
from typing import List, Dict, Any, Optional, Union
from qwen_api_client import QwenAPIClient

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler("qwen_gui_client.log"),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger(__name__)

# 定义颜色和样式
COLORS = {
    "bg": "#f5f5f5",
    "user_msg_bg": "#e1f5fe",
    "assistant_msg_bg": "#f1f8e9",
    "system_msg_bg": "#fff3e0",
    "button_bg": "#2196f3",
    "button_fg": "white",
    "button_active_bg": "#1976d2",
    "status_ok": "#4caf50",
    "status_error": "#f44336",
    "edit_bg": "#ffecb3"
}

class ScrollableFrame(ttk.Frame):
    """可滚动的框架，用于显示消息历史"""

    def __init__(self, container, *args, **kwargs):
        super().__init__(container, *args, **kwargs)
        self.canvas = tk.Canvas(self, bg=COLORS["bg"], highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas, style="Messages.TFrame")

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(
                scrollregion=self.canvas.bbox("all")
            )
        )

        self.canvas_frame = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # 绑定事件，使框架随窗口调整大小
        self.canvas.bind("<Configure>", self.on_canvas_configure)

        # 绑定鼠标滚轮事件
        self.canvas.bind_all("<MouseWheel>", self.on_mousewheel)

    def on_canvas_configure(self, event):
        """当画布大小改变时调整框架宽度"""
        self.canvas.itemconfig(self.canvas_frame, width=event.width)

    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def scroll_to_bottom(self):
        """滚动到底部"""
        self.canvas.update_idletasks()
        self.canvas.yview_moveto(1.0)

class QwenChatGUI:
    """Qwen2.5-VL-3B-Instruct 多模态模型图形界面客户端"""

    def __init__(self, root, api_url="http://localhost:8000"):
        """初始化图形界面

        Args:
            root: tkinter根窗口
            api_url: API服务的URL，默认为http://localhost:8000
        """
        self.root = root
        self.api_url = api_url
        self.client = None
        self.messages = []  # 消息历史
        self.conversation_files = []  # 对话文件列表
        self.current_conversation_file = None  # 当前对话文件
        self.editing_message_index = None  # 正在编辑的消息索引
        self.image_path = None  # 当前选择的图像路径
        self.image_preview = None  # 图像预览

        # 设置窗口
        self.setup_window()

        # 设置样式
        self.setup_styles()

        # 创建界面组件
        self.create_widgets()

        # 连接到API服务
        self.connect_to_api()

        # 加载对话历史文件列表
        self.load_conversation_files()

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("Qwen2.5-VL-3B-Instruct 多模态对话客户端")
        self.root.geometry("1000x800")
        self.root.minsize(800, 600)
        self.root.configure(bg=COLORS["bg"])

        # 使窗口在调整大小时更新布局
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

    def setup_styles(self):
        """设置ttk样式"""
        self.style = ttk.Style()
        self.style.configure("TFrame", background=COLORS["bg"])
        self.style.configure("Messages.TFrame", background=COLORS["bg"])
        self.style.configure("UserMsg.TFrame", background=COLORS["user_msg_bg"])
        self.style.configure("AssistantMsg.TFrame", background=COLORS["assistant_msg_bg"])
        self.style.configure("SystemMsg.TFrame", background=COLORS["system_msg_bg"])
        self.style.configure("EditMsg.TFrame", background=COLORS["edit_bg"])

        self.style.configure("TButton",
                            background=COLORS["button_bg"],
                            foreground=COLORS["button_fg"],
                            padding=5)
        self.style.map("TButton",
                      background=[("active", COLORS["button_active_bg"])])

        self.style.configure("Status.TLabel",
                            background=COLORS["bg"],
                            padding=5)
        self.style.configure("StatusOK.TLabel",
                            foreground=COLORS["status_ok"])
        self.style.configure("StatusError.TLabel",
                            foreground=COLORS["status_error"])

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 顶部工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))

        # 连接状态
        self.status_label = ttk.Label(toolbar_frame, text="未连接", style="Status.TLabel")
        self.status_label.pack(side="left", padx=5)

        # 对话操作按钮
        buttons_frame = ttk.Frame(toolbar_frame)
        buttons_frame.pack(side="right")

        ttk.Button(buttons_frame, text="新建对话", command=self.new_conversation).pack(side="left", padx=2)
        ttk.Button(buttons_frame, text="保存对话", command=self.save_conversation).pack(side="left", padx=2)
        ttk.Button(buttons_frame, text="加载对话", command=self.show_load_dialog).pack(side="left", padx=2)
        ttk.Button(buttons_frame, text="重置对话", command=self.reset_conversation).pack(side="left", padx=2)

        # 消息历史区域
        self.messages_frame = ScrollableFrame(main_frame)
        self.messages_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))

        # 输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, sticky="ew")
        input_frame.columnconfigure(0, weight=1)

        # 图像选择区域
        image_frame = ttk.Frame(input_frame)
        image_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))

        ttk.Button(image_frame, text="选择图像", command=self.select_image).pack(side="left", padx=(0, 5))
        self.image_label = ttk.Label(image_frame, text="未选择图像")
        self.image_label.pack(side="left", fill="x", expand=True)
        ttk.Button(image_frame, text="清除图像", command=self.clear_image).pack(side="right")

        # 文本输入区域
        text_frame = ttk.Frame(input_frame)
        text_frame.grid(row=1, column=0, sticky="ew")
        text_frame.columnconfigure(0, weight=1)

        self.input_text = scrolledtext.ScrolledText(text_frame, height=5, wrap=tk.WORD)
        self.input_text.grid(row=0, column=0, sticky="ew")
        self.input_text.bind("<Control-Return>", self.send_message)

        # 发送按钮
        send_frame = ttk.Frame(input_frame)
        send_frame.grid(row=2, column=0, sticky="e", pady=(5, 0))

        self.send_button = ttk.Button(send_frame, text="发送 (Ctrl+Enter)", command=self.send_message)
        self.send_button.pack(side="right")

        # 底部状态栏
        self.bottom_status = ttk.Label(main_frame, text="就绪", style="Status.TLabel")
        self.bottom_status.grid(row=3, column=0, sticky="w", pady=(5, 0))

    def connect_to_api(self):
        """连接到API服务"""
        try:
            self.update_status("正在连接到API服务...", is_error=False)
            self.client = QwenAPIClient(api_url=self.api_url)
            if self.client.check_health():
                self.update_status("已连接到API服务", is_error=False)
            else:
                self.update_status("无法连接到API服务", is_error=True)
        except Exception as e:
            logger.error(f"连接API服务失败: {str(e)}")
            self.update_status(f"连接失败: {str(e)}", is_error=True)

    def update_status(self, message, is_error=False):
        """更新状态标签

        Args:
            message: 状态消息
            is_error: 是否为错误状态
        """
        self.status_label.config(text=message)
        if is_error:
            self.status_label.configure(style="StatusError.TLabel")
        else:
            self.status_label.configure(style="StatusOK.TLabel")
        self.bottom_status.config(text=f"{datetime.datetime.now().strftime('%H:%M:%S')} - {message}")

    def select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像",
            filetypes=[("图像文件", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )
        if file_path:
            try:
                self.image_path = file_path
                self.image_label.config(text=os.path.basename(file_path))

                # 显示图像预览
                self.show_image_preview(file_path)

                self.update_status(f"已选择图像: {os.path.basename(file_path)}", is_error=False)
            except Exception as e:
                logger.error(f"加载图像失败: {str(e)}")
                self.update_status(f"加载图像失败: {str(e)}", is_error=True)

    def show_image_preview(self, image_path):
        """显示图像预览

        Args:
            image_path: 图像文件路径
        """
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("图像预览")
            preview_window.geometry("400x400")

            # 加载图像
            image = Image.open(image_path)

            # 调整图像大小以适应窗口
            image.thumbnail((380, 380))

            # 转换为Tkinter可用的格式
            photo = ImageTk.PhotoImage(image)

            # 显示图像
            label = ttk.Label(preview_window, image=photo)
            label.image = photo  # 保持引用，防止被垃圾回收
            label.pack(padx=10, pady=10)

            # 添加关闭按钮
            ttk.Button(preview_window, text="关闭", command=preview_window.destroy).pack(pady=10)
        except Exception as e:
            logger.error(f"显示图像预览失败: {str(e)}")
            messagebox.showerror("错误", f"显示图像预览失败: {str(e)}")

    def clear_image(self):
        """清除选择的图像"""
        self.image_path = None
        self.image_label.config(text="未选择图像")
        self.update_status("已清除图像", is_error=False)

    def send_message(self, event=None):
        """发送消息

        Args:
            event: 事件对象，用于绑定快捷键
        """
        # 获取输入文本
        text = self.input_text.get("1.0", tk.END).strip()

        # 检查是否有输入
        if not text and not self.image_path:
            self.update_status("请输入文本或选择图像", is_error=True)
            return

        # 检查是否正在编辑消息
        if self.editing_message_index is not None:
            self.update_edited_message(text)
            return

        # 检查API客户端是否可用
        if not self.client:
            self.update_status("未连接到API服务", is_error=True)
            return

        # 构建用户消息
        user_message = {"role": "user", "content": text}

        # 如果有图像，构建多模态消息
        if self.image_path:
            user_message = {
                "role": "user",
                "content": [
                    {"type": "text", "text": text},
                    {"type": "image_path", "image_path": self.image_path}
                ]
            }

        # 添加用户消息到历史
        self.messages.append(user_message)

        # 显示用户消息
        self.display_message(user_message)

        # 清空输入框
        self.input_text.delete("1.0", tk.END)

        # 清除图像
        self.clear_image()

        # 禁用发送按钮
        self.send_button.config(state="disabled")

        # 更新状态
        self.update_status("正在生成回复...", is_error=False)

        # 在后台线程中获取回复
        threading.Thread(target=self.get_response, daemon=True).start()

    def get_response(self):
        """在后台线程中获取模型回复"""
        try:
            # 准备发送到API的消息
            api_messages = []

            # 添加系统消息（如果有）
            system_message = next((msg for msg in self.messages if msg["role"] == "system"), None)
            if system_message:
                api_messages.append({"role": "system", "content": system_message["content"]})

            # 添加用户和助手消息
            for msg in self.messages:
                if msg["role"] == "system":
                    continue

                if isinstance(msg["content"], list):
                    # 处理多模态消息
                    content_list = []
                    for item in msg["content"]:
                        if item.get("type") == "text":
                            content_list.append({"type": "text", "text": item["text"]})
                        elif item.get("type") == "image_path":
                            # 将图像路径转换为base64
                            image_path = item["image_path"]
                            try:
                                with open(image_path, "rb") as image_file:
                                    import base64
                                    encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                                    image_url = f"data:image/jpeg;base64,{encoded_string}"
                                    content_list.append({"type": "image_url", "image_url": {"url": image_url}})
                            except Exception as e:
                                logger.error(f"图像编码失败: {str(e)}")

                    api_messages.append({"role": msg["role"], "content": content_list})
                else:
                    # 处理纯文本消息
                    api_messages.append({"role": msg["role"], "content": msg["content"]})

            # 调用API获取回复
            response = self.client.chat(api_messages)

            if response:
                # 添加助手回复到历史
                assistant_message = {"role": "assistant", "content": response}
                self.messages.append(assistant_message)

                # 在主线程中显示助手回复
                self.root.after(0, lambda: self.display_message(assistant_message))
                self.root.after(0, lambda: self.update_status("回复已生成", is_error=False))
            else:
                self.root.after(0, lambda: self.update_status("获取回复失败", is_error=True))
        except Exception as e:
            logger.error(f"获取回复失败: {str(e)}")
            self.root.after(0, lambda: self.update_status(f"获取回复失败: {str(e)}", is_error=True))
        finally:
            # 启用发送按钮
            self.root.after(0, lambda: self.send_button.config(state="normal"))

    def display_message(self, message):
        """显示消息

        Args:
            message: 消息对象，包含role和content字段
        """
        # 创建消息框架
        msg_frame = ttk.Frame(self.messages_frame.scrollable_frame, padding=10)
        msg_frame.pack(fill="x", padx=10, pady=5)

        # 根据角色设置样式
        if message["role"] == "user":
            msg_frame.configure(style="UserMsg.TFrame")
            prefix = "用户: "
        elif message["role"] == "assistant":
            msg_frame.configure(style="AssistantMsg.TFrame")
            prefix = "助手: "
        elif message["role"] == "system":
            msg_frame.configure(style="SystemMsg.TFrame")
            prefix = "系统: "
        else:
            prefix = f"{message['role']}: "

        # 创建标题行，包含角色和操作按钮
        header_frame = ttk.Frame(msg_frame)
        header_frame.pack(fill="x", pady=(0, 5))

        # 角色标签
        role_label = ttk.Label(header_frame, text=prefix, font=("Arial", 10, "bold"))
        role_label.pack(side="left")

        # 时间戳
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        time_label = ttk.Label(header_frame, text=timestamp, font=("Arial", 8))
        time_label.pack(side="left", padx=5)

        # 操作按钮
        buttons_frame = ttk.Frame(header_frame)
        buttons_frame.pack(side="right")

        # 获取消息索引
        msg_index = len(self.messages) - 1

        # 编辑按钮（仅用户消息可编辑）
        if message["role"] == "user":
            edit_button = ttk.Button(
                buttons_frame,
                text="编辑",
                command=lambda idx=msg_index: self.edit_message(idx),
                width=5
            )
            edit_button.pack(side="left", padx=2)

        # 复制按钮
        copy_button = ttk.Button(
            buttons_frame,
            text="复制",
            command=lambda content=self.get_message_text(message): self.copy_to_clipboard(content),
            width=5
        )
        copy_button.pack(side="left", padx=2)

        # 消息内容
        content_frame = ttk.Frame(msg_frame)
        content_frame.pack(fill="x")

        if isinstance(message["content"], list):
            # 多模态消息
            for item in message["content"]:
                if item.get("type") == "text":
                    text_widget = scrolledtext.ScrolledText(
                        content_frame,
                        wrap=tk.WORD,
                        height=min(10, max(1, item["text"].count('\n') + 1)),
                        font=("Arial", 10)
                    )
                    text_widget.insert(tk.END, item["text"])
                    text_widget.configure(state="disabled")
                    text_widget.pack(fill="x", pady=2)
                elif item.get("type") == "image_path":
                    # 显示图像路径
                    path_label = ttk.Label(content_frame, text=f"图像: {os.path.basename(item['image_path'])}")
                    path_label.pack(fill="x", pady=2)

                    # 添加查看图像按钮
                    view_button = ttk.Button(
                        content_frame,
                        text="查看图像",
                        command=lambda path=item["image_path"]: self.show_image_preview(path)
                    )
                    view_button.pack(pady=2)
        else:
            # 纯文本消息
            text_widget = scrolledtext.ScrolledText(
                content_frame,
                wrap=tk.WORD,
                height=min(15, max(1, message["content"].count('\n') + 1)),
                font=("Arial", 10)
            )
            text_widget.insert(tk.END, message["content"])
            text_widget.configure(state="disabled")
            text_widget.pack(fill="x")

        # 滚动到底部
        self.messages_frame.scroll_to_bottom()

    def get_message_text(self, message):
        """获取消息的文本内容

        Args:
            message: 消息对象

        Returns:
            消息的文本内容
        """
        if isinstance(message["content"], list):
            # 多模态消息，提取文本部分
            text_parts = []
            for item in message["content"]:
                if item.get("type") == "text":
                    text_parts.append(item["text"])
            return "\n".join(text_parts)
        else:
            # 纯文本消息
            return message["content"]

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板

        Args:
            text: 要复制的文本
        """
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.update_status("已复制到剪贴板", is_error=False)

    def edit_message(self, index):
        """编辑消息

        Args:
            index: 消息索引
        """
        if index >= len(self.messages) or self.messages[index]["role"] != "user":
            return

        # 设置编辑状态
        self.editing_message_index = index

        # 获取消息文本
        message_text = self.get_message_text(self.messages[index])

        # 填充到输入框
        self.input_text.delete("1.0", tk.END)
        self.input_text.insert(tk.END, message_text)

        # 更改发送按钮文本
        self.send_button.config(text="更新消息")

        # 更新状态
        self.update_status(f"正在编辑消息 #{index+1}", is_error=False)

        # 如果消息包含图像，恢复图像路径
        if isinstance(self.messages[index]["content"], list):
            for item in self.messages[index]["content"]:
                if item.get("type") == "image_path":
                    self.image_path = item["image_path"]
                    self.image_label.config(text=os.path.basename(self.image_path))

    def update_edited_message(self, text):
        """更新编辑后的消息

        Args:
            text: 编辑后的文本
        """
        if self.editing_message_index is None:
            return

        # 获取原始消息
        original_message = self.messages[self.editing_message_index]

        # 更新消息内容
        if self.image_path and isinstance(original_message["content"], list):
            # 更新多模态消息
            new_content = [
                {"type": "text", "text": text},
                {"type": "image_path", "image_path": self.image_path}
            ]
            self.messages[self.editing_message_index]["content"] = new_content
        elif self.image_path:
            # 将纯文本消息转换为多模态消息
            new_content = [
                {"type": "text", "text": text},
                {"type": "image_path", "image_path": self.image_path}
            ]
            self.messages[self.editing_message_index]["content"] = new_content
        else:
            # 更新纯文本消息
            self.messages[self.editing_message_index]["content"] = text

        # 清空输入框
        self.input_text.delete("1.0", tk.END)

        # 恢复发送按钮文本
        self.send_button.config(text="发送 (Ctrl+Enter)")

        # 清除编辑状态
        self.editing_message_index = None

        # 清除图像
        self.clear_image()

        # 更新状态
        self.update_status("消息已更新", is_error=False)

        # 重新显示所有消息
        self.refresh_messages()

        # 如果编辑的是最后一条用户消息，自动重新生成回复
        if self.editing_message_index == len(self.messages) - 2:
            # 移除最后一条助手消息
            if len(self.messages) > 0 and self.messages[-1]["role"] == "assistant":
                self.messages.pop()

            # 重新生成回复
            self.send_button.config(state="disabled")
            self.update_status("正在重新生成回复...", is_error=False)
            threading.Thread(target=self.get_response, daemon=True).start()

    def refresh_messages(self):
        """刷新消息显示"""
        # 清空消息框架
        for widget in self.messages_frame.scrollable_frame.winfo_children():
            widget.destroy()

        # 重新显示所有消息
        for message in self.messages:
            self.display_message(message)

    def new_conversation(self):
        """创建新对话"""
        if self.messages and messagebox.askyesno("确认", "创建新对话将清除当前对话。是否继续？"):
            self.reset_conversation()

    def reset_conversation(self):
        """重置当前对话"""
        self.messages = []
        self.current_conversation_file = None
        self.editing_message_index = None
        self.image_path = None
        self.image_label.config(text="未选择图像")
        self.input_text.delete("1.0", tk.END)
        self.send_button.config(text="发送 (Ctrl+Enter)")

        # 清空消息框架
        for widget in self.messages_frame.scrollable_frame.winfo_children():
            widget.destroy()

        self.update_status("对话已重置", is_error=False)

    def save_conversation(self):
        """保存当前对话"""
        if not self.messages:
            messagebox.showinfo("提示", "当前没有对话内容可保存")
            return

        # 如果已有文件名，直接保存
        if self.current_conversation_file:
            file_path = self.current_conversation_file
        else:
            # 否则弹出保存对话框
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"qwen_conversation_{timestamp}.json"

            file_path = filedialog.asksaveasfilename(
                title="保存对话",
                defaultextension=".json",
                initialfile=default_filename,
                filetypes=[("JSON文件", "*.json")]
            )

            if not file_path:
                return

        try:
            # 准备保存数据
            save_data = {
                "messages": self.messages,
                "timestamp": datetime.datetime.now().isoformat(),
                "metadata": {
                    "api_url": self.api_url,
                    "client_version": "1.0.0"
                }
            }

            # 保存到文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            self.current_conversation_file = file_path
            self.update_status(f"对话已保存到: {os.path.basename(file_path)}", is_error=False)

            # 更新对话文件列表
            self.load_conversation_files()
        except Exception as e:
            logger.error(f"保存对话失败: {str(e)}")
            self.update_status(f"保存失败: {str(e)}", is_error=True)
            messagebox.showerror("错误", f"保存对话失败: {str(e)}")

    def load_conversation_files(self):
        """加载对话文件列表"""
        self.conversation_files = []

        # 检查对话目录
        conversations_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "conversations")
        if not os.path.exists(conversations_dir):
            os.makedirs(conversations_dir)

        # 扫描对话文件
        for file in os.listdir(conversations_dir):
            if file.endswith(".json"):
                self.conversation_files.append(os.path.join(conversations_dir, file))

    def show_load_dialog(self):
        """显示加载对话对话框"""
        file_path = filedialog.askopenfilename(
            title="加载对话",
            filetypes=[("JSON文件", "*.json")]
        )

        if file_path:
            self.load_conversation(file_path)

    def load_conversation(self, file_path):
        """加载对话

        Args:
            file_path: 对话文件路径
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            if "messages" not in data:
                raise ValueError("无效的对话文件格式")

            # 重置当前对话
            self.reset_conversation()

            # 加载消息
            self.messages = data["messages"]

            # 更新当前文件
            self.current_conversation_file = file_path

            # 刷新消息显示
            self.refresh_messages()

            self.update_status(f"已加载对话: {os.path.basename(file_path)}", is_error=False)
        except Exception as e:
            logger.error(f"加载对话失败: {str(e)}")
            self.update_status(f"加载失败: {str(e)}", is_error=True)
            messagebox.showerror("错误", f"加载对话失败: {str(e)}")

def main():
    """主函数"""
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="Qwen2.5-VL-3B-Instruct 多模态模型图形界面客户端")
    parser.add_argument("--api_url", type=str, default="http://localhost:8000",
                        help="API服务的URL，默认为http://localhost:8000")
    args = parser.parse_args()

    # 创建根窗口
    root = tk.Tk()

    # 创建应用
    app = QwenChatGUI(root, api_url=args.api_url)

    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main()
