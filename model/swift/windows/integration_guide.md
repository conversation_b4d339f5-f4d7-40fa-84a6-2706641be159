# Qwen2.5-VL-3B-Instruct 多模态模型 Windows 客户端集成指南

本指南提供了在 Windows 系统上使用 Qwen2.5-VL-3B-Instruct 多模态模型 API 服务的详细说明，包括图形界面客户端的安装、配置和使用方法。

## 目录

1. [系统要求](#系统要求)
2. [安装步骤](#安装步骤)
3. [SSH 隧道设置](#ssh-隧道设置)
4. [图形界面客户端使用](#图形界面客户端使用)
5. [API 客户端集成](#api-客户端集成)
6. [故障排除](#故障排除)
7. [高级功能](#高级功能)

## 系统要求

- Windows 10/11 操作系统
- Python 3.8 或更高版本
- 以下 Python 包：
  - requests
  - pillow (PIL)
  - tkinter (通常随 Python 安装)
- SSH 客户端 (用于创建 SSH 隧道)

## 安装步骤

1. **安装 Python**

   如果尚未安装 Python，请从 [Python 官网](https://www.python.org/downloads/windows/) 下载并安装最新版本的 Python。安装时请确保勾选"Add Python to PATH"选项。

2. **安装必要的 Python 包**

   打开命令提示符或 PowerShell，运行以下命令：

   ```bash
   pip install requests pillow
   ```

3. **下载客户端文件**

   从 Linux 服务器复制以下文件到 Windows 电脑上的工作目录：
   - `qwen_api_client.py` - API 客户端类
   - `qwen_gui_client.py` - 图形界面客户端
   - `setup_ssh_tunnel.bat` - SSH 隧道设置脚本
   - `test_qwen_api.py` - API 测试脚本

4. **创建对话保存目录**

   在工作目录下创建 `conversations` 文件夹，用于保存对话历史：

   ```bash
   mkdir conversations
   ```

## SSH 隧道设置

Windows 客户端需要通过 SSH 隧道连接到 Linux 服务器上的 API 服务。

1. **编辑 SSH 隧道脚本**

   打开 `setup_ssh_tunnel.bat` 文件，确认以下信息是否正确：

   ```
   服务器: *************
   端口: 30008
   用户名: batchcom
   密钥文件: C:\Users\<USER>\.ssh\id_rsa_temp
   ```

   根据您的实际情况修改这些参数。

2. **运行 SSH 隧道脚本**

   双击 `setup_ssh_tunnel.bat` 文件运行，它将创建一个 SSH 隧道，将 Windows 上的本地端口 8000 转发到 Linux 服务器上的 8000 端口。

   > **注意**：保持 SSH 隧道窗口打开，隧道才能保持活动状态。

3. **验证 SSH 隧道**

   打开浏览器，访问 `http://localhost:8000/health`，如果返回 `{"status":"healthy","model_loaded":true}`，则表示隧道设置成功。

## 图形界面客户端使用

图形界面客户端提供了友好的用户界面，支持多模态对话、历史对话保存和加载、对话重置、消息重新编辑等功能。

1. **启动图形界面客户端**

   打开命令提示符或 PowerShell，切换到工作目录，运行：

   ```bash
   python qwen_gui_client.py
   ```

   如果 API 服务不在默认地址，可以指定 API URL：

   ```bash
   python qwen_gui_client.py --api_url http://localhost:8000
   ```

2. **界面功能说明**

   - **顶部工具栏**：显示连接状态和对话操作按钮
   - **消息历史区域**：显示对话历史
   - **图像选择区域**：选择要发送的图像
   - **文本输入区域**：输入要发送的文本
   - **发送按钮**：发送消息（也可以使用 Ctrl+Enter 快捷键）

3. **基本操作**

   - **发送文本消息**：在文本输入框中输入文本，点击"发送"按钮或按 Ctrl+Enter
   - **发送图像**：点击"选择图像"按钮，选择图像文件，然后输入文本并发送
   - **查看图像**：对于包含图像的消息，可以点击"查看图像"按钮查看图像
   - **复制消息**：点击消息右上角的"复制"按钮，将消息内容复制到剪贴板
   - **编辑消息**：点击用户消息右上角的"编辑"按钮，编辑消息内容，然后点击"更新消息"按钮

4. **对话管理**

   - **新建对话**：点击"新建对话"按钮，创建一个新的对话
   - **保存对话**：点击"保存对话"按钮，将当前对话保存到文件
   - **加载对话**：点击"加载对话"按钮，从文件加载对话
   - **重置对话**：点击"重置对话"按钮，清除当前对话

## API 客户端集成

如果您需要在自己的应用程序中集成 Qwen2.5-VL-3B-Instruct 多模态模型 API，可以使用提供的 `qwen_api_client.py` 文件。

1. **基本用法**

   ```python
   from qwen_api_client import QwenAPIClient
   
   # 创建客户端实例
   client = QwenAPIClient(api_url="http://localhost:8000")
   
   # 检查服务是否可用
   if client.check_health():
       # 发送文本消息
       messages = [
           {"role": "system", "content": "You are a helpful assistant."},
           {"role": "user", "content": "你好，请介绍一下自己"}
       ]
       response = client.chat(messages)
       print(response)
       
       # 分析图像
       image_response = client.analyze_image("path/to/image.jpg", "描述这张图片")
       print(image_response)
   ```

2. **多模态消息**

   ```python
   # 构建多模态消息
   messages = [
       {"role": "user", "content": [
           {"type": "text", "text": "这张图片是什么？"},
           {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
       ]}
   ]
   
   # 发送多模态消息
   response = client.chat(messages)
   ```

3. **自定义参数**

   ```python
   # 设置温度和最大生成token数
   response = client.chat(
       messages=messages,
       temperature=0.5,
       max_tokens=2000
   )
   ```

## 故障排除

### 1. SSH 隧道问题

**症状**：无法连接到 API 服务，状态显示"未连接"

**解决方案**：
- 确认 SSH 隧道窗口是否打开
- 检查 SSH 密钥文件路径是否正确
- 尝试手动运行 SSH 命令：
  ```
  ssh -v -N -L 8000:localhost:8000 -i "C:\Users\<USER>\.ssh\id_rsa_temp" -p 30008 batchcom@*************
  ```
- 确认 Linux 服务器上的 API 服务是否已启动

### 2. 图像处理问题

**症状**：发送图像后无响应或报错

**解决方案**：
- 确认图像文件格式是否支持（推荐使用 JPG 或 PNG 格式）
- 尝试使用较小的图像文件（大图像可能导致内存问题）
- 检查 Python 是否安装了 PIL 库：`pip install pillow`

### 3. 界面显示问题

**症状**：界面显示异常或无法正常操作

**解决方案**：
- 确认 Python 版本是否为 3.8 或更高版本
- 确认 tkinter 是否正确安装：`python -m tkinter`
- 尝试重新启动客户端

## 高级功能

### 1. 自定义系统提示

您可以通过编辑代码添加系统提示，以控制模型的行为：

```python
# 在 qwen_gui_client.py 中的 reset_conversation 方法中添加
def reset_conversation(self):
    self.messages = [
        {"role": "system", "content": "你是一个有用的助手，擅长回答问题和分析图像。"}
    ]
    # 其他代码...
```

### 2. 批量处理图像

如果需要批量处理图像，可以编写脚本调用 API 客户端：

```python
import os
from qwen_api_client import QwenAPIClient

client = QwenAPIClient()
image_dir = "path/to/images"
results = {}

for image_file in os.listdir(image_dir):
    if image_file.endswith((".jpg", ".png", ".jpeg")):
        image_path = os.path.join(image_dir, image_file)
        result = client.analyze_image(image_path, "描述这张图片")
        results[image_file] = result

# 保存结果
import json
with open("analysis_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

### 3. 集成到其他应用

您可以将 API 客户端集成到其他 Windows 应用程序中，例如：

- 桌面应用程序
- Web 应用程序
- 自动化脚本
- 数据处理管道

只需导入 `qwen_api_client.py` 并创建 `QwenAPIClient` 实例即可。
