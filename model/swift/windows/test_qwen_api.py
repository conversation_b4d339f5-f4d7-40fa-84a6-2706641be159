#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型API测试脚本
用于测试从Windows连接到Linux服务器上的API服务
"""

import os
import sys
import argparse
import logging
import time
from qwen_api_client import QwenAPIClient

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_text_chat(client, prompt="你好，请介绍一下自己"):
    """测试文本聊天功能"""
    logger.info(f"测试文本聊天，提示词: {prompt}")
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": prompt}
    ]
    
    start_time = time.time()
    response = client.chat(messages)
    elapsed_time = time.time() - start_time
    
    if response:
        logger.info(f"聊天成功，耗时: {elapsed_time:.2f}秒")
        return response
    else:
        logger.error("聊天失败")
        return None

def test_image_analysis(client, image_path, prompt="请详细描述这张图片中的内容"):
    """测试图像分析功能"""
    if not os.path.exists(image_path):
        logger.error(f"图像文件不存在: {image_path}")
        return None
    
    logger.info(f"测试图像分析，图像: {image_path}, 提示词: {prompt}")
    
    start_time = time.time()
    response = client.analyze_image(image_path, prompt)
    elapsed_time = time.time() - start_time
    
    if response:
        logger.info(f"图像分析成功，耗时: {elapsed_time:.2f}秒")
        return response
    else:
        logger.error("图像分析失败")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Qwen2.5-VL-3B-Instruct 多模态模型API测试脚本")
    parser.add_argument("--api_url", type=str, default="http://localhost:8000", 
                        help="API服务的URL，默认为http://localhost:8000")
    parser.add_argument("--mode", type=str, choices=["text", "image", "both"], default="both", 
                        help="测试模式: text(仅文本), image(仅图像), both(两者)")
    parser.add_argument("--image", type=str, default="test_image.jpg", 
                        help="要分析的图像文件路径")
    parser.add_argument("--text_prompt", type=str, default="你好，请介绍一下自己", 
                        help="文本聊天的提示词")
    parser.add_argument("--image_prompt", type=str, default="请详细描述这张图片中的内容", 
                        help="图像分析的提示词")
    args = parser.parse_args()
    
    # 创建API客户端
    logger.info(f"连接到API服务: {args.api_url}")
    client = QwenAPIClient(api_url=args.api_url)
    
    # 检查服务是否可用
    if not client.check_health():
        logger.error("API服务不可用，请确保SSH隧道已建立，并且Linux服务器上的API服务已启动")
        sys.exit(1)
    
    # 根据模式执行测试
    if args.mode in ["text", "both"]:
        # 测试文本聊天
        text_response = test_text_chat(client, args.text_prompt)
        if text_response:
            print("\n" + "="*80)
            print("文本聊天结果:")
            print("="*80)
            print(text_response)
            print("="*80)
        else:
            print("\n文本聊天测试失败")
    
    if args.mode in ["image", "both"]:
        # 测试图像分析
        if os.path.exists(args.image):
            image_response = test_image_analysis(client, args.image, args.image_prompt)
            if image_response:
                print("\n" + "="*80)
                print("图像分析结果:")
                print("="*80)
                print(image_response)
                print("="*80)
            else:
                print("\n图像分析测试失败")
        else:
            logger.error(f"图像文件不存在: {args.image}")
            print(f"\n图像文件不存在: {args.image}")
            print("请提供有效的图像文件路径，或使用 --mode text 仅测试文本聊天")
    
    logger.info("测试完成")

if __name__ == "__main__":
    main()
