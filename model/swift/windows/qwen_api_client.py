#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Qwen2.5-VL-3B-Instruct 多模态模型API客户端
用于从Windows连接到Linux服务器上的API服务
支持处理图像和文本输入
"""

import os
import requests
import json
import logging
import base64
import time
from io import BytesIO
from PIL import Image
from typing import List, Dict, Any, Optional, Union

# 设置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QwenAPIClient:
    """
    Qwen2.5-VL-3B-Instruct 多模态模型API客户端
    用于跨服务器调用Qwen多模态模型API
    支持处理图像和文本输入
    """
    
    def __init__(self, api_url: str = "http://localhost:8000", timeout: int = 30, max_retries: int = 3):
        """
        初始化客户端
        
        Args:
            api_url: API服务的URL，默认为http://localhost:8000（通过SSH隧道访问）
            timeout: 请求超时时间（秒），默认为30秒
            max_retries: 最大重试次数，默认为3次
        """
        self.api_url = api_url
        self.chat_endpoint = f"{api_url}/v1/chat/completions"
        self.health_endpoint = f"{api_url}/health"
        self.server_info_endpoint = f"{api_url}/server_info"
        self.image_analysis_endpoint = f"{api_url}/v1/analyze_image"
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 检查服务是否可用
        self.check_health()
    
    def check_health(self) -> bool:
        """
        检查服务是否可用
        
        Returns:
            服务是否可用
        """
        try:
            response = requests.get(self.health_endpoint, timeout=5)
            if response.status_code == 200:
                logger.info(f"Qwen API服务可用: {self.api_url}")
                
                # 获取服务器信息
                try:
                    info_response = requests.get(self.server_info_endpoint, timeout=5)
                    if info_response.status_code == 200:
                        server_info = info_response.json()
                        logger.info(f"服务器信息: IP={server_info.get('server_ip')}, "
                                   f"模型已加载={server_info.get('model_loaded')}, "
                                   f"GPU={server_info.get('gpu_info')}")
                except Exception as info_e:
                    logger.warning(f"无法获取服务器信息: {str(info_e)}")
                
                return True
            else:
                logger.warning(f"Qwen API服务不可用，状态码: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"检查Qwen API服务健康状态时出错: {str(e)}")
            logger.warning(f"请确保SSH隧道已建立，并且Linux服务器上的API服务已启动")
            return False
    
    def _encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """
        将图像编码为base64字符串
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            base64编码的图像字符串，如果失败则返回None
        """
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return f"data:image/jpeg;base64,{encoded_string}"
        except Exception as e:
            logger.error(f"图像编码失败: {str(e)}")
            return None
    
    def chat(self, 
             messages: List[Dict[str, Any]], 
             model: str = "qwen2.5-vl-3b-instruct",
             temperature: float = 0.7,
             max_tokens: int = 4096) -> Optional[str]:
        """
        调用聊天API
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "Hello"}, ...]
            model: 模型名称，默认为qwen2.5-vl-3b-instruct
            temperature: 温度参数，控制生成的随机性，默认为0.7
            max_tokens: 最大生成token数，默认为4096
            
        Returns:
            模型生成的回复，如果出错则返回None
        """
        for attempt in range(self.max_retries):
            try:
                # 构建请求数据
                data = {
                    "model": model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens
                }
                
                # 发送请求
                response = requests.post(self.chat_endpoint, json=data, timeout=self.timeout)
                
                # 检查响应
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.warning(f"API响应格式不正确: {result}")
                        if attempt < self.max_retries - 1:
                            logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                            time.sleep(1)  # 等待1秒后重试
                            continue
                        return None
                else:
                    logger.warning(f"API调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    if attempt < self.max_retries - 1:
                        logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return None
            except Exception as e:
                logger.error(f"调用Qwen API时出错: {str(e)}")
                if attempt < self.max_retries - 1:
                    logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                    time.sleep(1)  # 等待1秒后重试
                    continue
                return None
        
        return None  # 所有重试都失败
    
    def analyze_image(self, 
                     image_path: str, 
                     prompt: str = "描述这张图片", 
                     temperature: float = 0.7,
                     max_tokens: int = 1000) -> Optional[str]:
        """
        分析图像
        
        Args:
            image_path: 图像文件路径
            prompt: 提示词，默认为"描述这张图片"
            temperature: 温度参数，控制生成的随机性，默认为0.7
            max_tokens: 最大生成token数，默认为1000
            
        Returns:
            图像分析结果，如果失败则返回None
        """
        # 编码图像
        base64_image = self._encode_image_to_base64(image_path)
        if not base64_image:
            return None
        
        for attempt in range(self.max_retries):
            try:
                # 构建请求数据
                data = {
                    "model": "qwen2.5-vl-3b-instruct",
                    "messages": [
                        {"role": "user", "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": base64_image}}
                        ]}
                    ],
                    "temperature": temperature,
                    "max_tokens": max_tokens
                }
                
                # 发送请求
                response = requests.post(self.chat_endpoint, json=data, timeout=self.timeout)
                
                # 检查响应
                if response.status_code == 200:
                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        return result["choices"][0]["message"]["content"]
                    else:
                        logger.warning(f"API响应格式不正确: {result}")
                        if attempt < self.max_retries - 1:
                            logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                            time.sleep(1)
                            continue
                        return None
                else:
                    logger.warning(f"API调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    if attempt < self.max_retries - 1:
                        logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                        time.sleep(1)
                        continue
                    return None
            except Exception as e:
                logger.error(f"调用Qwen API时出错: {str(e)}")
                if attempt < self.max_retries - 1:
                    logger.info(f"尝试重试 ({attempt+1}/{self.max_retries})...")
                    time.sleep(1)
                    continue
                return None
        
        return None  # 所有重试都失败
