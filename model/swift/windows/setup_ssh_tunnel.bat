@echo off
echo ======================================================
echo  Qwen2.5-VL-3B-Instruct API服务 - SSH隧道设置工具
echo ======================================================
echo.
echo 此脚本将创建一个SSH隧道，将本地端口8000转发到Linux服务器上的8000端口
echo 这样您就可以通过 http://localhost:8000 访问Linux服务器上的API服务
echo.
echo 服务器信息:
echo  - 主机: *************
echo  - 端口: 30008
echo  - 用户: batchcom
echo  - 密钥: C:\Users\<USER>\.ssh\id_rsa_temp
echo.
echo 请确保:
echo  1. 您已安装OpenSSH客户端
echo  2. SSH密钥文件存在且权限正确
echo  3. Linux服务器上的API服务已启动
echo.
echo 按任意键开始创建SSH隧道，或按Ctrl+C取消...
pause > nul

echo.
echo 正在建立SSH隧道...
echo 本地端口: 8000 -> 远程端口: 8000
echo 连接到: batchcom@*************:30008
echo.
echo 隧道建立后，您可以通过 http://localhost:8000 访问API服务
echo 请保持此窗口打开，关闭窗口将断开隧道连接
echo.

:: 使用SSH密钥建立隧道
ssh -N -L 8000:localhost:8000 -i "C:\Users\<USER>\.ssh\id_rsa_temp" -p 30008 batchcom@*************

:: 如果连接断开，显示提示
echo.
echo SSH隧道已断开连接。
echo 如需重新连接，请重新运行此脚本。
echo.
echo 按任意键退出...
pause > nul
