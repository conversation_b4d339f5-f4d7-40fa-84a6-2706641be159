#!/bin/bash
# 此脚本用于启动Qwen2.5-VL-3B-Instruct多模态模型服务
# 使用ms-swift框架进行推理
# 支持跨服务器API通信

# 激活conda环境
echo "正在激活conda环境 grpo..."
source /opt/conda/etc/profile.d/conda.sh
conda activate grpo

# 如果环境激活失败，则退出
if [ $? -ne 0 ]; then
    echo "错误: 无法激活conda环境 grpo"
    echo "请确保conda已正确安装且环境存在"
    exit 1
fi

# 设置环境变量，指定使用GPU 0
export CUDA_VISIBLE_DEVICES=0
export CUDA_HOME=/home/<USER>/wzy/cuda-12.4-toolkit
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 获取本机IP地址
get_local_ip() {
    ip addr show | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n 1
}

LOCAL_IP=$(get_local_ip)

# 检查服务是否已经运行
if pgrep -f "model/swift/start_qwen_model_server_swift.py" > /dev/null; then
    echo "Qwen模型服务(Swift)已经在运行"
    echo "可通过 http://${LOCAL_IP}:8000 访问API服务"
    exit 0
fi

# 确保日志目录存在
mkdir -p model/swift/logs

# 设置模型路径
MODEL_PATH="/home/<USER>/wzy/NIPS/model/Qwen2.5-VL-3B-Instruct"

# 检查模型路径是否存在
if [ ! -d "$MODEL_PATH" ]; then
    echo "错误: 模型路径不存在: $MODEL_PATH"
    echo "请确保模型已下载并放置在正确的位置"
    exit 1
fi

# 启动模型服务
echo "正在启动Qwen2.5-VL-3B-Instruct多模态模型服务(Swift)..."
echo "使用模型路径: $MODEL_PATH"

# 修改环境变量，确保模型能够正确加载
export MAX_MODEL_LEN=16384
export MODEL_PATH="$MODEL_PATH"

# 清理旧的日志文件
echo "" > model/swift/logs/qwen_service_swift.log

# 确保在正确的目录下运行
cd /home/<USER>/wzy/NIPS

# 检查Python环境
echo "使用的Python解释器: $(which python)"
echo "Python版本: $(python --version)"
echo "当前工作目录: $(pwd)"

# 检查必要的Python包
echo "检查必要的Python包..."
python -c "import torch; print(f'PyTorch版本: {torch.__version__}, CUDA可用: {torch.cuda.is_available()}')" || { echo "错误: 无法导入PyTorch"; exit 1; }
python -c "import transformers; print(f'Transformers版本: {transformers.__version__}')" || { echo "错误: 无法导入Transformers"; exit 1; }
python -c "import swift; print(f'Swift版本: {swift.__version__}')" || { echo "警告: 无法导入Swift，但将继续尝试"; }

# 启动服务，并将输出重定向到日志文件
echo "启动服务..."
nohup python model/swift/start_qwen_model_server_swift.py > model/swift/logs/qwen_service_swift.log 2>&1 &

# 获取进程ID
PID=$!
echo "服务已启动，进程ID: $PID"

# 等待服务启动
echo "等待服务启动..."
echo "正在检查进程状态..."

# 检查进程是否存在
if ! ps -p $PID > /dev/null; then
    echo "错误: 服务进程已经终止"
    echo "查看日志文件获取更多信息:"
    echo "-----------------------------------"
    tail -n 20 model/swift/logs/qwen_service_swift.log
    echo "-----------------------------------"
    echo "完整日志: model/swift/logs/qwen_service_swift.log"
    exit 1
fi

# 等待服务启动
MAX_WAIT=90  # 增加等待时间到90秒
for i in $(seq 1 $MAX_WAIT); do
    echo -n "."

    # 每10秒检查一次进程是否还在运行
    if [ $((i % 10)) -eq 0 ]; then
        if ! ps -p $PID > /dev/null; then
            echo -e "\n错误: 服务进程已经终止"
            echo "查看日志文件获取更多信息:"
            echo "-----------------------------------"
            tail -n 20 model/swift/logs/qwen_service_swift.log
            echo "-----------------------------------"
            echo "完整日志: model/swift/logs/qwen_service_swift.log"
            exit 1
        fi
        echo -n "进程仍在运行"
    fi

    # 检查服务是否可访问
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "\nQwen2.5-VL-3B-Instruct多模态模型服务(Swift)启动成功"
        echo "API服务地址: http://${LOCAL_IP}:8000"
        echo "API端点: http://${LOCAL_IP}:8000/v1/chat/completions"
        echo "健康检查: http://${LOCAL_IP}:8000/health"
        echo "服务器信息: http://${LOCAL_IP}:8000/server_info"
        echo ""
        echo "可以使用以下命令测试API客户端:"
        echo "python model/swift/test_qwen_vl_api.py --api_url http://${LOCAL_IP}:8000 --image /home/<USER>/wzy/NIPS/dataset_video_temp/60/3.png"
        echo ""
        echo "要从其他服务器访问，请使用:"
        echo "python model/swift/test_qwen_vl_api.py --api_url http://${LOCAL_IP}:8000 --image <图像路径>"
        exit 0
    fi
    sleep 2
done

echo -e "\n服务启动超时，但进程仍在运行。可能需要更长时间加载模型..."
echo "您可以继续等待，或检查日志文件了解详情:"
echo "-----------------------------------"
tail -n 30 model/swift/logs/qwen_service_swift.log
echo "-----------------------------------"
echo "完整日志: model/swift/logs/qwen_service_swift.log"

# 提供选项让用户决定是否终止进程
echo ""
echo "选项:"
echo "1. 继续等待服务启动"
echo "2. 终止服务进程并退出"
read -p "请选择 [1/2]: " choice

if [ "$choice" = "2" ]; then
    echo "正在终止服务进程 (PID: $PID)..."
    kill $PID
    echo "服务已终止"
    exit 1
else
    echo "继续等待服务启动..."
    echo "您可以使用以下命令检查服务状态:"
    echo "curl http://localhost:8000/health"
    echo "或查看日志文件:"
    echo "tail -f model/swift/logs/qwen_service_swift.log"
    exit 0
fi
