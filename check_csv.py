import csv
import os
import re

def check_loc_csv(folder_path):
    loc_file_path = os.path.join(folder_path, "loc.csv")
    if not os.path.exists(loc_file_path):
        print(f"文件未找到: {loc_file_path}")
        return

    all_rows_meet_condition_for_file = True
    try:
        with open(loc_file_path, 'r') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if len(row) < 2:
                    all_rows_meet_condition_for_file = False
                    break 

                # 获取第2列，这是包含数组的字符串
                array_str = row[1]
                
                # 处理字符串：去除换行符和多余空格，使之成为一个单行字符串
                array_str = array_str.replace('\n', ' ').strip()
                # 移除多余空格
                array_str = re.sub(r'\s+', ' ', array_str)
                
                # 提取方括号中的内容
                if '[' in array_str and ']' in array_str:
                    array_content = array_str[array_str.find('[')+1:array_str.rfind(']')]
                    # 用空格分割元素
                    elements = array_content.strip().split()
                    
                    # 检查是否有足够的元素
                    if len(elements) >= 5:  # 确保至少有5个元素 (索引0-4)
                        # 第三个元素（索引3）
                        third_element = elements[3]
                        # 第四个元素（索引4）
                        fourth_element = elements[4]
                        
                        # 检查第三个和第四个元素是否都为 "0.00000000e+00"
                        try:
                            third_value = float(third_element)
                            fourth_value = float(fourth_element)
                            
                            is_third_zero = abs(third_value) < 1e-9
                            is_fourth_zero = abs(fourth_value) < 1e-9
                            
                            if not (is_third_zero and is_fourth_zero):
                                all_rows_meet_condition_for_file = False
                                break
                        except ValueError:
                            all_rows_meet_condition_for_file = False
                            break
                    else:
                        all_rows_meet_condition_for_file = False
                        break
                else:
                    all_rows_meet_condition_for_file = False
                    break
            
            if all_rows_meet_condition_for_file:
                print(f"文件 {loc_file_path} 中的所有行都满足条件 (第3个和第4个元素都为 0.00000000e+00)。")
            else:
                print(f"文件 {loc_file_path} 中并非所有行都满足条件。")

    except Exception as e:
        print(f"处理文件 {loc_file_path} 时出错: {e}")
        import traceback
        traceback.print_exc()

base_path = "data/dataset_video/"
print(f"开始检查 {base_path} 下 0-230 号文件夹...")
for i in range(231): # 0 到 230
    folder_name = str(i)
    full_folder_path = os.path.join(base_path, folder_name)
    if os.path.isdir(full_folder_path):
        check_loc_csv(full_folder_path)
    # else:
    #     print(f"文件夹未找到: {full_folder_path}") # 静默处理找不到的文件夹，只处理存在的

# 单独检查用户提供的示例文件路径，以确保逻辑覆盖此情况
example_folder_path = "data/dataset_video/230" # 这是文件夹路径
print(f"\n正在特别检查示例文件夹 {example_folder_path} 中的 loc.csv:")
if os.path.isdir(example_folder_path):
    check_loc_csv(example_folder_path)
else:
    print(f"示例文件夹未找到: {example_folder_path}") 