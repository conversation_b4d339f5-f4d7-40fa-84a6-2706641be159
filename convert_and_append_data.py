#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从navigation_qa_instruction.jsonl随机抽取数据并添加到grpo_dataset_all_swift.jsonl
"""

import json
import random
import os
from typing import List, Dict, Any

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """加载JSONL文件"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                data.append(json.loads(line))
    return data

def save_jsonl(file_path: str, data: List[Dict[str, Any]]) -> None:
    """保存JSONL文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def convert_to_swift_format(item: Dict[str, Any]) -> Dict[str, Any]:
    """将navigation_qa格式转换为swift格式"""
    # 从conversations中提取内容
    system_content = ""
    user_content = ""
    solution = ""
    
    for conv in item.get("conversations", []):
        if conv.get("role") == "system":
            system_content = conv.get("content", "")
        elif conv.get("role") == "user":
            user_content = conv.get("content", "")
        elif conv.get("role") == "assistant":
            solution = conv.get("content", "")
    
    # 构建Swift RLHF格式
    swift_item = {
        "messages": [
            {"role": "system", "content": system_content},
            {"role": "user", "content": user_content}
        ],
        "solution": solution,
        "folder_id": item.get("id", "unknown"),
        "images": [item.get("image", "")] if "image" in item else [],
        # 添加默认的action_reward，确保格式兼容
        "action_reward": {
            "Move forward": random.uniform(-1, 1),
            "Move backward": random.uniform(-1, 1),
            "Move left": random.uniform(-1, 1),
            "Move right": random.uniform(-1, 1),
            "Move up": random.uniform(-1, 1),
            "Move down": random.uniform(-1, 1),
            "Rotate left": random.uniform(-1, 1),
            "Rotate right": random.uniform(-1, 1),
            "Look up": random.uniform(-1, 1),
            "Look down": random.uniform(-1, 1)
        },
        # 添加空的历史动作列表
        "history_actions": []
    }
    
    # 尝试从solution中提取动作序列
    action_list = ["Move forward", "Move backward", "Move left", "Move right", 
                  "Move up", "Move down", "Rotate left", "Rotate right", 
                  "Look up", "Look down"]
    
    # 设置最可能的动作为solution中提到的动作
    for action in action_list:
        if action in solution:
            swift_item["action_sequence"] = [action]
            # 给这个动作更高的奖励值
            swift_item["action_reward"][action] = random.uniform(0.3, 0.8)
            break
    
    # 如果没有找到动作，设置一个默认的动作序列
    if "action_sequence" not in swift_item:
        swift_item["action_sequence"] = [random.choice(action_list)]
    
    return swift_item

def main():
    # 文件路径
    source_file = "data/navigation_qa_instruction.jsonl"
    target_file = "data/grpo_dataset_all_swift.jsonl"
    
    # 检查文件是否存在
    if not os.path.exists(source_file):
        print(f"错误: 源文件 {source_file} 不存在")
        return
    
    if not os.path.exists(target_file):
        print(f"警告: 目标文件 {target_file} 不存在，将创建新文件")
    
    # 加载源数据
    print(f"正在加载源文件: {source_file}")
    source_data = load_jsonl(source_file)
    print(f"源文件中共有 {len(source_data)} 条数据")
    
    # 随机抽取300条数据
    sample_size = min(300, len(source_data))
    sampled_data = random.sample(source_data, sample_size)
    print(f"随机抽取了 {sample_size} 条数据")
    
    # 转换格式
    print("正在转换数据格式...")
    converted_data = [convert_to_swift_format(item) for item in sampled_data]
    
    # 加载目标文件中的现有数据
    target_data = []
    if os.path.exists(target_file):
        target_data = load_jsonl(target_file)
        print(f"目标文件中已有 {len(target_data)} 条数据")
    
    # 合并数据
    merged_data = target_data + converted_data
    print(f"合并后共有 {len(merged_data)} 条数据")
    
    # 保存合并后的数据
    print(f"正在保存到目标文件: {target_file}")
    save_jsonl(target_file, merged_data)
    
    print(f"成功完成! 从 {source_file} 中抽取了 {sample_size} 条数据并添加到 {target_file}")
    print(f"目标文件现在共有 {len(merged_data)} 条数据")

if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    random.seed(42)
    main()