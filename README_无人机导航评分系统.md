# 无人机导航评分系统说明文档 (v2.0)

## 系统概述

本系统提供了一个完整的无人机导航轨迹评分解决方案，包含评分计算和结果分析两个模块，用于评估模型生成的导航轨迹质量，并识别表现不佳的决策点用于后续GRPO训练。

## 版本 2.0 新特性

### ✅ 主要改进

1. **详细错误日志记录**:
   - 详细记录每个失败任务的具体原因
   - 按错误类型统计失败任务
   - 自动生成失败任务详情文件

2. **任务标识符格式升级**:
   - 新格式: `task_id_attempt_id` (如 "331_0")
   - 支持多尝试次数评估
   - 向后兼容旧格式

3. **多尝试次数支持**:
   - 可评估指定任务的所有尝试次数
   - 可评估所有任务的所有尝试次数
   - 灵活的命令行参数控制

4. **增强的数据验证**:
   - 文件存在性检查
   - 数据格式验证
   - 数据一致性验证

## 主要改进

### ✅ 已修复的问题

1. **权重比例修正**: 
   - 修复前: 终点:轨迹:效率 = 1:1:0.5
   - 修复后: 终点:轨迹:效率 = 1:1:1（完全符合需求）

2. **输出格式完善**:
   - 新增 `step_details` 字段，包含每个步骤的详细信息
   - 现在输出包含: 动作序号、动作编号、动作名称、三个维度分数、总分

3. **效率评分范围修正**:
   - 修复前: 范围[0, 0.5]
   - 修复后: 范围[0, 1.0]，当模型步数少于基准步数时给予更高分数

4. **分析功能增强**:
   - 支持新格式的详细信息显示
   - 保持与旧格式的兼容性
   - 增加表现不佳决策点的示例输出

## 文件结构

```
code/
├── navigation_scoring_system.py    # 主评分系统
├── analyze_navigation_scores.py    # 结果分析工具
└── README_无人机导航评分系统.md   # 本说明文档
```

## 使用方法

### 1. 基本评分功能

```bash
# 评估单个任务的默认尝试(第0次)
python navigation_scoring_system.py --task_id 331

# 评估单个任务的指定尝试次数
python navigation_scoring_system.py --task_id 331 --attempt_id 2

# 评估单个任务的所有尝试次数
python navigation_scoring_system.py --task_id 331 --all_attempts

# 评估所有任务的默认尝试(第0次)
python navigation_scoring_system.py

# 评估所有任务的所有尝试次数
python navigation_scoring_system.py --all_attempts

# 指定数据目录和输出文件
python navigation_scoring_system.py --base_dir /path/to/data --output_file results.json
```

### 2. 分析评分结果

```bash
# 基本分析
python analyze_navigation_scores.py --input_file navigation_scores.json

# 自定义参数分析
python analyze_navigation_scores.py \
    --input_file navigation_scores.json \
    --output_file bad_decisions.json \
    --analysis_file detailed_analysis.json \
    --threshold -0.5
```

## 输出文件详解

### 1. navigation_scores.json (主评分结果)

```json
{
  "331_0": {
    "task_id": "331_0",
    "original_task_id": "331",
    "attempt_id": "0",
    "step_details": [
      {
        "step": 0,
        "action_id": "6",
        "action_name": "向前运动",
        "endpoint_score": 0.234,
        "trajectory_score": 0.156,
        "efficiency_score": 0.022,
        "total_score": 0.137
      }
    ],
    "step_scores": [0.137, ...],
    "endpoint_scores": [0.234, ...],
    "trajectory_scores": [0.156, ...],
    "efficiency_score": 0.75,
    "avg_step_score": 0.137,
    "total_score": 0.137,
    "model_steps": 25,
    "reference_steps": 30
  },
  "summary": {
    "task_count": 77,
    "success_count": 75,
    "failed_count": 2,
    "avg_total_score": 0.123,
    "attempt_id": "0",
    "error_statistics": {
      "目录不存在": {
        "count": 1,
        "tasks": ["332_0"]
      },
      "必需文件缺失": {
        "count": 1,
        "tasks": ["345_0"]
      }
    }
  },
  "failed_tasks": {
    "332_0": {
      "error": "目录不存在",
      "error_details": ["模型数据集目录不存在: data/dataset_rl/332/0"],
      "task_id": "332_0",
      "original_task_id": "332",
      "attempt_id": "0"
    }
  }
}
```

### 2. navigation_scores_failed.json (失败任务详情)

自动生成，包含所有失败任务的详细错误信息。

### 3. bad_decisions.json (表现不佳的决策点)

```json
{
  "331_0": [
    {
      "step": 5,
      "action_id": "7",
      "action_name": "向后运动",
      "endpoint_score": -0.45,
      "trajectory_score": -0.38,
      "efficiency_score": 0.02,
      "total_score": -0.27
    }
  ]
}
```

## 错误类型说明

系统会详细记录以下错误类型：

1. **目录不存在**: 基准数据集或模型数据集目录缺失
2. **必需文件缺失**: loc.csv 或 path.csv 文件不存在
3. **基准坐标数据为空**: 基准轨迹坐标文件无有效数据
4. **模型坐标数据为空**: 模型轨迹坐标文件无有效数据
5. **模型动作数据为空**: 模型动作文件无有效数据
6. **数据加载失败**: 文件读取过程中发生异常
7. **数据验证失败**: 数据格式或一致性问题
8. **评分计算失败**: 各评分维度计算过程中的异常

## 多尝试次数支持

### 数据结构要求

```
data/
├── dataset_video_instruction/  # 基准数据集
│   └── 331/
│       ├── loc.csv
│       └── path.csv
└── dataset_rl/                # 模型数据集
    └── 331/
        ├── 0/                 # 第0次尝试
        │   ├── loc.csv
        │   └── path.csv
        ├── 1/                 # 第1次尝试
        │   ├── loc.csv
        │   └── path.csv
        └── 2/                 # 第2次尝试
            ├── loc.csv
            └── path.csv
```

### 命令行参数详解

- `--task_id`: 指定单个任务ID
- `--attempt_id`: 指定尝试次数ID (默认"0")
- `--all_attempts`: 评估所有可用的尝试次数
- `--base_dir`: 数据目录路径
- `--output_file`: 输出文件路径

## 错误排查指南

当看到"成功评估任务数 < 总任务数"时，检查以下内容：

1. **查看控制台输出**: 系统会实时显示每个失败任务的错误原因
2. **检查失败任务文件**: 自动生成的 `*_failed.json` 文件
3. **验证数据完整性**: 确保所需文件存在且格式正确
4. **检查文件权限**: 确保程序有读取数据文件的权限

### 常见问题解决

```bash
# 问题1: 目录不存在
# 解决: 检查数据路径是否正确，确保基准数据集和模型数据集都存在

# 问题2: 文件缺失
# 解决: 确保每个任务目录下都有 loc.csv 和 path.csv 文件

# 问题3: 数据格式错误
# 解决: 检查CSV文件格式，确保坐标格式为 "序号,\"[x y z rx ry yaw pitch]\""
```

## 兼容性说明

- **向后兼容**: 支持处理旧格式的评分结果文件
- **数据格式**: 自动处理新旧任务标识符格式
- **错误处理**: 优雅处理各种数据问题，不会中断整体评估流程

## 性能优化建议

1. **大规模评估**: 使用 `--all_attempts` 时建议分批处理
2. **存储空间**: 失败任务多时会生成较大的错误日志文件
3. **内存使用**: 大量任务评估时注意内存使用情况

## 版本历史

- **v2.0**: 增加详细错误日志、多尝试次数支持、任务标识符格式升级
- **v1.0**: 基础评分功能、三维度评分、权重修正

## 评分机制说明

### 三个评分维度

1. **终点关系评分** (权重1)
   - 使用奖励2和奖励3机制
   - 评估动作对接近终点的贡献
   - 范围: [-1, 1]

2. **轨迹关系评分** (权重1)
   - 找到基准轨迹最近点+后4个点的平均位置作为参考
   - 使用相同的奖励2和奖励3机制
   - 范围: [-1, 1]

3. **导航效率评分** (权重1)
   - 成功导航: 0.5 × (基准步数/模型步数)，最大1.0
   - 失败导航: 0分
   - 成功判定: 距离终点≤15米

### 特殊情况处理

- 动作编号不在对照表中: 直接给-1分
- 无效动作: 各维度均为-1分

## 评分区间说明

- **优秀** (0.5~1.0): 表现优异的决策
- **良好** (0.0~0.5): 表现良好的决策  
- **一般** (-0.3~0.0): 表现一般的决策
- **较差** (-0.7~-0.3): 表现较差的决策
- **很差** (-1.0~-0.7): 表现很差的决策

建议将评分<-0.3的决策点用于GRPO训练。

## 数据要求

系统需要以下数据结构：

```
data/
├── dataset_video_instruction/  # 基准数据集
│   └── 331/
│       ├── loc.csv            # 坐标记录
│       ├── path.csv           # 动作序列
│       └── goal.txt           # 导航目标
└── dataset_rl/                # 模型数据集
    └── 331/
        └── 0/                 # 第0次尝试
            ├── loc.csv
            ├── path.csv
            └── goal.txt
```

## 系统要求

- Python 3.7+
- numpy
- 标准库: json, csv, argparse, glob

## 注意事项

1. 确保数据文件路径正确
2. 坐标文件格式: `序号,"[x y z rx ry yaw pitch]"`
3. 动作文件格式: `序号,动作编号`
4. 系统会自动跳过缺失或错误的数据文件 