#!/usr/bin/env python3
"""
Checkpoint Watcher - 监控检查点文件并自动传输到远程服务器
"""

import os
import sys
import time
import json
import logging
import argparse
import subprocess
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import shutil
import fnmatch
import re
import hashlib # 新增导入

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('checkpoint_transfer.log')
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
TRANSFER_STATUS = {}
STATUS_FILE = "transfer_status.json"

class CheckpointHandler(FileSystemEventHandler):
    """处理检查点文件的事件处理器"""

    def __init__(self, target_host, target_user, target_path, ssh_key=None, file_pattern=None, ssh_port="22", delete_after_transfer=True, enable_integrity_check=False):
        self.target_host = target_host
        self.target_user = target_user
        self.target_path = target_path
        self.ssh_key = ssh_key
        self.ssh_port = ssh_port
        self.delete_after_transfer = delete_after_transfer
        self.enable_integrity_check = enable_integrity_check # 新增参数
        self.processing_files = set()
        self.path = None

        # 解析文件模式，支持逗号分隔的多个模式
        self.file_patterns = []
        if file_pattern:
            self.file_patterns.extend([p.strip() for p in file_pattern.split(',') if p.strip()])

        # 默认添加常见的视频文件扩展名，如果未指定任何模式
        if not self.file_patterns:
            self.file_patterns.extend(['*.mp4', '*.avi', '*.mov', '*.mkv', '*.flv', '*.wmv', '*.log', '*.jsonl', 'checkpoint-*'])

        # 加载之前的状态（如果存在）
        self._load_status()

    def _load_status(self):
        """加载传输状态"""
        global TRANSFER_STATUS
        if os.path.exists(STATUS_FILE):
            try:
                with open(STATUS_FILE, 'r') as f:
                    TRANSFER_STATUS = json.load(f)
            except Exception as e:
                logger.error(f"加载状态文件失败: {str(e)}")
                TRANSFER_STATUS = {}

    def _save_status(self):
        """保存传输状态"""
        global TRANSFER_STATUS
        try:
            with open(STATUS_FILE, 'w') as f:
                json.dump(TRANSFER_STATUS, f, indent=2)
        except Exception as e:
            logger.error(f"保存状态文件失败: {str(e)}")

    def on_created(self, event):
        """处理文件创建事件"""
        file_path = event.src_path

        # 检查文件模式匹配
        if not self._match_pattern(file_path):
            return

        # 防止重复处理
        if file_path in self.processing_files:
            return

        self.processing_files.add(file_path)

        # 如果是目录
        if event.is_directory:
            # 检查是否是v[数字]-[日期时间]格式的目录
            basename = os.path.basename(file_path)
            v_pattern = re.compile(r'^v\d+-\d{8}-\d{6}$')
            checkpoint_pattern = re.compile(r'^checkpoint-\d+$')

            # 如果是v[数字]-[日期时间]格式的目录，启动监控线程
            if v_pattern.match(basename):
                logger.info(f"检测到训练目录: {file_path}")
                # 启动训练目录监控线程
                thread = threading.Thread(target=self._monitor_train_directory, args=(file_path,))
                thread.daemon = True
                thread.start()
            # 如果是checkpoint-[数字]格式的目录，直接处理
            elif checkpoint_pattern.match(basename):
                logger.info(f"检测到checkpoint目录: {file_path}")
                # 启动目录处理线程
                thread = threading.Thread(target=self._process_directory, args=(file_path,))
                thread.daemon = True
                thread.start()
            else:
                # 其他目录，直接处理
                logger.info(f"检测到新目录: {file_path}")
                # 启动目录处理线程
                thread = threading.Thread(target=self._process_directory, args=(file_path,))
                thread.daemon = True
                thread.start()
        else:
            # 启动文件处理线程
            thread = threading.Thread(target=self._process_file, args=(file_path,))
            thread.daemon = True
            thread.start()

    def _process_directory(self, directory_path):
        """处理目录"""
        logger.info(f"处理目录: {directory_path}")

        # 检查目录是否存在
        if not os.path.exists(directory_path):
            logger.error(f"目录不存在: {directory_path}")
            self.processing_files.discard(directory_path)
            return

        # 检查是否是checkpoint-[数字]格式的目录
        basename = os.path.basename(directory_path)
        checkpoint_pattern = re.compile(r'^checkpoint-\d+$')
        is_checkpoint_dir = checkpoint_pattern.match(basename) is not None

        # 如果是checkpoint目录，等待30秒确保模型完成保存
        if is_checkpoint_dir:
            logger.info(f"检测到checkpoint目录，等待30秒，确保模型完成保存: {directory_path}")
            try:
                time.sleep(30)
            except KeyboardInterrupt:
                logger.warning("处理被用户中断")
                self.processing_files.discard(directory_path)
                return

            # 再次检查目录是否存在（可能在等待期间被删除）
            if not os.path.exists(directory_path):
                logger.error(f"等待后目录不存在: {directory_path}")
                self.processing_files.discard(directory_path)
                return

        # 检查目录中是否有正在写入的文件
        max_check_attempts = 5
        check_attempt = 0

        while check_attempt < max_check_attempts:
            if self._has_active_writes(directory_path):
                check_attempt += 1
                logger.info(f"目录 {directory_path} 中有文件正在写入，等待文件稳定 (尝试 {check_attempt}/{max_check_attempts})")

                # 如果达到最大尝试次数，稍后重新检查
                if check_attempt >= max_check_attempts:
                    logger.info(f"文件仍在写入，稍后重新检查目录: {directory_path}")
                    self.processing_files.discard(directory_path)
                    # 稍后重新检查
                    thread = threading.Timer(30.0, self._recheck_directory, args=(directory_path,))
                    thread.daemon = True
                    thread.start()
                    return

                # 等待文件稳定
                time.sleep(10)
            else:
                logger.info(f"目录 {directory_path} 中的文件已稳定，开始传输")
                break

        # 重试机制
        max_retries = 3
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            if retry_count > 0:
                logger.info(f"重试处理目录 ({retry_count}/{max_retries}): {directory_path}")
                # 再次检查目录是否存在
                if not os.path.exists(directory_path):
                    logger.error(f"重试前目录不存在: {directory_path}")
                    self.processing_files.discard(directory_path)
                    return

            try:
                logger.info(f"开始处理目录 (尝试 {retry_count + 1}/{max_retries}): {directory_path}")
                # 传输目录
                if self._transfer_directory(directory_path):
                    logger.info(f"目录处理成功: {directory_path}")
                    success = True
                else:
                    logger.warning(f"目录传输失败: {directory_path}")
                    retry_count += 1
                    # 如果还有重试机会，等待一段时间后重试
                    if retry_count < max_retries:
                        logger.info(f"等待10秒后重试...")
                        try:
                            time.sleep(10)
                        except KeyboardInterrupt:
                            logger.warning("处理被用户中断")
                            self.processing_files.discard(directory_path)
                            return
            except Exception as e:
                logger.exception(f"处理目录时发生错误: {str(e)}")
                retry_count += 1
                # 如果还有重试机会，等待一段时间后重试
                if retry_count < max_retries:
                    logger.info(f"等待10秒后重试...")
                    try:
                        time.sleep(10)
                    except KeyboardInterrupt:
                        logger.warning("处理被用户中断")
                        self.processing_files.discard(directory_path)
                        return

        # 从处理集合中移除
        self.processing_files.discard(directory_path)

        if not success:
            logger.error(f"处理目录失败，已达到最大重试次数: {directory_path}")

    def _transfer_directory(self, dir_path):
        """传输目录到目标服务器"""
        dir_name = os.path.basename(dir_path)

        try:
            logger.info(f"开始传输目录: {dir_path} 到 {self.target_user}@{self.target_host}:{self.target_path}")

            # 检查目录是否存在
            if not os.path.exists(dir_path):
                logger.error(f"源目录不存在: {dir_path}")
                return False

            # 更新状态
            global TRANSFER_STATUS
            TRANSFER_STATUS[dir_name] = {
                "status": "transferring",
                "progress": "0%",
                "message": "开始传输目录",
                "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": ""
            }
            self._save_status()

            # 获取监控目录的基础路径
            base_path = self.path
            # 计算相对路径
            rel_path = os.path.relpath(dir_path, base_path)
            # 创建目标路径
            target_full_path = os.path.join(self.target_path, rel_path)

            # 确保目标目录存在
            mkdir_cmd = [
                "ssh" if not self.ssh_key else "ssh",
                "-i", self.ssh_key if self.ssh_key else "",
                "-p", self.ssh_port,
                "-o", "StrictHostKeyChecking=no",
                "-o", "ConnectTimeout=10",
                f"{self.target_user}@{self.target_host}",
                f"mkdir -p {target_full_path}"
            ]
            # 移除空参数
            mkdir_cmd = [arg for arg in mkdir_cmd if arg]

            logger.debug(f"执行命令: {' '.join(mkdir_cmd)}")
            try:
                subprocess.run(mkdir_cmd, check=True)
                logger.debug(f"成功创建远程目录: {target_full_path}")
            except subprocess.CalledProcessError as e:
                logger.error(f"创建远程目录失败: {str(e)}")
                TRANSFER_STATUS[dir_name]["status"] = "failed"
                TRANSFER_STATUS[dir_name]["message"] = f"创建远程目录失败: {str(e)}"
                TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()
                return False

            # 收集目录中的所有文件
            all_files = []
            for root, _, files in os.walk(dir_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    all_files.append(file_path)

            if not all_files:
                logger.info(f"目录为空，无需传输: {dir_path}")
                TRANSFER_STATUS[dir_name]["status"] = "success"
                TRANSFER_STATUS[dir_name]["progress"] = "100%"
                TRANSFER_STATUS[dir_name]["message"] = "目录为空，无需传输"
                TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()

                # 如果允许删除，则删除空目录
                if self.delete_after_transfer:
                    try:
                        if os.path.exists(dir_path) and os.path.isdir(dir_path):
                            shutil.rmtree(dir_path)
                            logger.info(f"空目录已删除: {dir_path}")
                    except Exception as e:
                        logger.error(f"删除空目录失败: {dir_path}, 错误: {str(e)}")
                else:
                    logger.info(f"根据配置，不删除空目录: {dir_path}")

                return True

            # 传输文件计数
            total_files = len(all_files)
            transferred_files = 0
            failed_files = 0

            # 逐个传输文件
            for file_path in all_files:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    logger.warning(f"文件不存在，跳过传输: {file_path}")
                    continue

                # 计算目标路径
                rel_file_path = os.path.relpath(file_path, base_path)
                target_file_dir = os.path.dirname(os.path.join(self.target_path, rel_file_path))

                # 确保目标文件目录存在
                mkdir_file_cmd = [
                    "ssh" if not self.ssh_key else "ssh",
                    "-i", self.ssh_key if self.ssh_key else "",
                    "-p", self.ssh_port,
                    "-o", "StrictHostKeyChecking=no",
                    "-o", "ConnectTimeout=10",
                    f"{self.target_user}@{self.target_host}",
                    f"mkdir -p {target_file_dir}"
                ]
                # 移除空参数
                mkdir_file_cmd = [arg for arg in mkdir_file_cmd if arg]

                try:
                    subprocess.run(mkdir_file_cmd, check=True)
                except subprocess.CalledProcessError as e:
                    logger.error(f"创建远程文件目录失败: {target_file_dir}, 错误: {str(e)}")
                    failed_files += 1
                    continue

                # 构建rsync命令
                rsync_cmd = ["rsync", "-avz", "--progress"]
                if self.ssh_key:
                    rsync_cmd.extend(["-e", f"ssh -i {self.ssh_key} -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"])
                else:
                    rsync_cmd.extend(["-e", f"ssh -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"])

                # 添加源文件和目标路径
                rsync_cmd.append(file_path)
                rsync_cmd.append(f"{self.target_user}@{self.target_host}:{target_file_dir}/")

                # 执行rsync命令
                logger.debug(f"执行命令: {' '.join(rsync_cmd)}")

                # 重试机制
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    if retry_count > 0:
                        logger.info(f"重试传输文件 ({retry_count}/{max_retries}): {file_path}")

                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        logger.warning(f"文件在传输前被删除，跳过: {file_path}")
                        break

                    try:
                        # 使用Popen实时获取输出
                        process = subprocess.Popen(
                            rsync_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            bufsize=1,
                            universal_newlines=True
                        )

                        # 读取输出并更新进度
                        for line in process.stdout:
                            line = line.strip()
                            logger.debug(line)

                        # 等待进程完成
                        process.wait()
                        stderr = process.stderr.read()

                        if process.returncode == 0:
                            logger.info(f"文件传输成功: {file_path}")

                            # 如果启用完整性校验
                            if self.enable_integrity_check:
                                local_md5 = self._calculate_md5(file_path)
                                remote_file_full_path = os.path.join(target_file_dir, os.path.basename(file_path))
                                remote_md5 = self._get_remote_md5(remote_file_full_path)

                                if local_md5 and remote_md5 and local_md5 == remote_md5:
                                    logger.info(f"文件完整性校验成功: {file_path}")
                                    transferred_files += 1
                                    success = True

                                    # 计算并更新总进度
                                    progress = int((transferred_files / total_files) * 100)
                                    TRANSFER_STATUS[dir_name]["progress"] = f"{progress}%"
                                    TRANSFER_STATUS[dir_name]["message"] = f"传输中: {transferred_files}/{total_files} 文件"
                                    self._save_status()

                                    # 如果允许删除，则删除原始文件
                                    if self.delete_after_transfer:
                                        try:
                                            if os.path.exists(file_path):
                                                os.remove(file_path)
                                                logger.debug(f"原始文件已删除: {file_path}")
                                            else:
                                                logger.warning(f"原始文件已不存在，无需删除: {file_path}")
                                        except Exception as e:
                                            logger.warning(f"删除原始文件失败: {file_path}, 错误: {str(e)}")
                                    else:
                                        logger.info(f"根据配置，不删除原始文件: {file_path}")

                                    break
                                else:
                                    logger.warning(f"文件完整性校验失败: {file_path}. 本地MD5: {local_md5}, 远程MD5: {remote_md5}")
                                    retry_count += 1
                            else:
                                logger.info(f"文件传输成功 (未进行完整性校验): {file_path}")
                                transferred_files += 1
                                success = True

                                # 计算并更新总进度
                                progress = int((transferred_files / total_files) * 100)
                                TRANSFER_STATUS[dir_name]["progress"] = f"{progress}%"
                                TRANSFER_STATUS[dir_name]["message"] = f"传输中: {transferred_files}/{total_files} 文件"
                                self._save_status()

                                # 如果允许删除，则删除原始文件
                                if self.delete_after_transfer:
                                    try:
                                        if os.path.exists(file_path):
                                            os.remove(file_path)
                                            logger.debug(f"原始文件已删除: {file_path}")
                                        else:
                                            logger.warning(f"原始文件已不存在，无需删除: {file_path}")
                                    except Exception as e:
                                        logger.warning(f"删除原始文件失败: {file_path}, 错误: {str(e)}")
                                else:
                                    logger.info(f"根据配置，不删除原始文件: {file_path}")

                            break
                        else:
                            logger.warning(f"文件传输失败: {file_path}, 错误: {stderr}")
                            retry_count += 1
                    except Exception as e:
                        logger.warning(f"传输文件时发生错误: {file_path}, 错误: {str(e)}")
                        retry_count += 1

                if not success:
                    logger.error(f"文件传输失败，已达到最大重试次数: {file_path}")
                    failed_files += 1

            # 检查传输结果
            if failed_files == 0 and transferred_files > 0:
                logger.info(f"目录传输成功: {dir_path}, 共传输 {transferred_files} 个文件")
                TRANSFER_STATUS[dir_name]["status"] = "success"
                TRANSFER_STATUS[dir_name]["progress"] = "100%"
                TRANSFER_STATUS[dir_name]["message"] = f"传输成功，共 {transferred_files} 个文件"
                TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()

                # 检查源目录是否为空
                remaining_files = []
                for root, _, files in os.walk(dir_path):
                    for file in files:
                        remaining_files.append(os.path.join(root, file))

                if not remaining_files:
                    # 源目录为空，如果允许删除，则删除
                    if self.delete_after_transfer:
                        try:
                            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                                shutil.rmtree(dir_path)
                                logger.info(f"原始目录已删除: {dir_path}")
                        except Exception as e:
                            logger.error(f"删除原始目录失败: {dir_path}, 错误: {str(e)}")
                    else:
                        logger.info(f"根据配置，不删除原始目录: {dir_path}")
                else:
                    logger.warning(f"源目录不为空，还有 {len(remaining_files)} 个文件，不删除: {dir_path}")

                return True
            elif transferred_files > 0:
                logger.warning(f"目录部分传输成功: {dir_path}, 成功 {transferred_files} 个文件, 失败 {failed_files} 个文件")
                TRANSFER_STATUS[dir_name]["status"] = "partial"
                TRANSFER_STATUS[dir_name]["progress"] = f"{int((transferred_files / total_files) * 100)}%"
                TRANSFER_STATUS[dir_name]["message"] = f"部分传输成功，成功 {transferred_files}/{total_files} 文件"
                TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()
                return True
            else:
                logger.error(f"目录传输完全失败: {dir_path}")
                TRANSFER_STATUS[dir_name]["status"] = "failed"
                TRANSFER_STATUS[dir_name]["message"] = "传输失败，所有文件都传输失败"
                TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()
                return False

        except Exception as e:
            logger.exception(f"传输目录时发生错误: {str(e)}")
            TRANSFER_STATUS[dir_name]["status"] = "failed"
            TRANSFER_STATUS[dir_name]["message"] = f"传输错误: {str(e)}"
            TRANSFER_STATUS[dir_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self._save_status()
            return False

    def _calculate_md5(self, file_path):
        """计算文件的MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件 {file_path} 的MD5哈希值失败: {str(e)}")
            return None

    def _get_remote_md5(self, remote_file_path):
        """通过SSH获取远程文件的MD5哈希值"""
        cmd = [
            "ssh" if not self.ssh_key else "ssh",
            "-i", self.ssh_key if self.ssh_key else "",
            "-p", self.ssh_port,
            "-o", "StrictHostKeyChecking=no",
            "-o", "ConnectTimeout=10",
            f"{self.target_user}@{self.target_host}",
            f"md5sum {remote_file_path}"
        ]
        cmd = [arg for arg in cmd if arg] # 移除空参数

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout.split(' ')[0].strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"获取远程文件 {remote_file_path} 的MD5哈希值失败: {e.stderr.strip()}")
            return None
        except Exception as e:
            logger.error(f"获取远程文件 {remote_file_path} 的MD5哈希值时发生错误: {str(e)}")
            return None

    def _has_active_writes(self, path):
        """检查目录中是否有正在写入的文件"""
        if os.path.isfile(path):
            return self._is_file_being_written(path)

        for root, dirs, files in os.walk(path):
            for file in files:
                file_path = os.path.join(root, file)
                if self._is_file_being_written(file_path):
                    return True
        return False

    def _is_file_being_written(self, file_path):
        """检查文件是否正在被写入"""
        try:
            # 获取文件大小
            initial_size = os.path.getsize(file_path)
            # 等待一小段时间
            time.sleep(2)
            # 再次获取文件大小
            final_size = os.path.getsize(file_path)
            # 如果文件大小变化，说明文件正在被写入
            return initial_size != final_size
        except (FileNotFoundError, PermissionError) as e:
            logger.error(f"检查文件 {file_path} 是否正在写入时出错: {e}")
            return True  # 如果出错，假设文件正在被写入

    def _recheck_directory(self, directory_path):
        """重新检查目录"""
        logger.info(f"重新检查目录: {directory_path}")

        # 检查目录是否存在
        if not os.path.exists(directory_path):
            logger.error(f"重新检查时目录不存在: {directory_path}")
            return

        # 如果目录已经在处理中，则跳过
        if directory_path in self.processing_files:
            logger.info(f"目录已经在处理中，跳过: {directory_path}")
            return

        try:
            logger.info(f"将目录添加到处理队列: {directory_path}")
            self.processing_files.add(directory_path)
            self._process_directory(directory_path)
        except Exception as e:
            logger.exception(f"重新检查目录时发生错误: {str(e)}")
            self.processing_files.discard(directory_path)

    def _process_file(self, file_path):
        """在单独的线程中处理文件"""
        logger.info(f"开始处理文件: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在，无法处理: {file_path}")
            self.processing_files.discard(file_path)
            return

        # 检查是否是logging.jsonl文件
        if os.path.basename(file_path) == "logging.jsonl":
            # 获取父目录
            parent_dir = os.path.dirname(file_path)
            parent_basename = os.path.basename(parent_dir)

            # 检查父目录是否符合v[数字]-[日期时间]格式
            v_pattern = re.compile(r'^v\d+-\d{8}-\d{6}$')
            if v_pattern.match(parent_basename):
                logger.info(f"检测到训练日志文件: {file_path}，开始监控")
                # 启动监控线程
                self._monitor_logging_file(file_path)
                return

        # 重试机制
        max_retries = 3
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            if retry_count > 0:
                logger.info(f"重试处理文件 ({retry_count}/{max_retries}): {file_path}")

                # 再次检查文件是否存在
                if not os.path.exists(file_path):
                    logger.error(f"重试前文件不存在: {file_path}")
                    self.processing_files.discard(file_path)
                    return

            try:
                # 等待文件写入完成
                if self._wait_for_file_completion(file_path):
                    # 再次检查文件是否存在
                    if not os.path.exists(file_path):
                        logger.error(f"等待写入完成后文件不存在: {file_path}")
                        self.processing_files.discard(file_path)
                        return

                    # 传输文件
                    if self._transfer_file(file_path):
                        logger.info(f"文件处理成功: {file_path}")
                        success = True
                    else:
                        logger.warning(f"文件传输失败: {file_path}")
                        retry_count += 1
                        # 如果还有重试机会，等待一段时间后重试
                        if retry_count < max_retries:
                            logger.info(f"等待5秒后重试...")
                            try:
                                time.sleep(5)
                            except KeyboardInterrupt:
                                logger.warning("处理被用户中断")
                                self.processing_files.discard(file_path)
                                return
                else:
                    logger.warning(f"等待文件写入完成失败: {file_path}")
                    retry_count += 1
                    # 如果还有重试机会，等待一段时间后重试
                    if retry_count < max_retries:
                        logger.info(f"等待5秒后重试...")
                        try:
                            time.sleep(5)
                        except KeyboardInterrupt:
                            logger.warning("处理被用户中断")
                            self.processing_files.discard(file_path)
                            return
            except Exception as e:
                logger.exception(f"处理文件时发生错误: {str(e)}")
                retry_count += 1
                # 如果还有重试机会，等待一段时间后重试
                if retry_count < max_retries:
                    logger.info(f"等待5秒后重试...")
                    try:
                        time.sleep(5)
                    except KeyboardInterrupt:
                        logger.warning("处理被用户中断")
                        self.processing_files.discard(file_path)
                        return

        # 从处理集合中移除
        self.processing_files.discard(file_path)

        if not success:
            logger.error(f"处理文件失败，已达到最大重试次数: {file_path}")

    def _match_pattern(self, file_path):
        """检查文件是否匹配指定的模式"""
        # 检查文件是否匹配指定的模式
        file_basename = os.path.basename(file_path)
        for pattern in self.file_patterns:
            if fnmatch.fnmatch(file_basename, pattern):
                logger.info(f"文件 {file_path} 匹配模式 {pattern}")
                return True

        # 检查是否是v[数字]-[日期时间]格式的目录
        v_pattern = re.compile(r'^v\d+-\d{8}-\d{6}$')

        # 检查是否是checkpoint-[数字]格式的目录
        checkpoint_pattern = re.compile(r'^checkpoint-\d+$')

        # 如果是目录，检查是否是checkpoint-[数字]格式或v[数字]-[日期时间]格式
        if os.path.isdir(file_path):
            basename = os.path.basename(file_path)

            # 如果是checkpoint-[数字]格式的目录
            if checkpoint_pattern.match(basename):
                # 检查父目录是否是v[数字]-[日期时间]格式
                parent_dir = os.path.basename(os.path.dirname(file_path))
                if v_pattern.match(parent_dir):
                    logger.info(f"匹配到checkpoint目录: {file_path}")
                    return True

            # 如果是v[数字]-[日期时间]格式的目录，标记为匹配，但由on_created方法特殊处理
            if v_pattern.match(basename):
                logger.info(f"匹配到训练目录: {file_path}")
                return True

        # 如果是文件，检查是否是logging.jsonl文件，且父目录是v[数字]-[日期时间]格式
        if os.path.isfile(file_path) and os.path.basename(file_path) == "logging.jsonl":
            parent_dir = os.path.basename(os.path.dirname(file_path))
            if v_pattern.match(parent_dir):
                logger.info(f"匹配到日志文件: {file_path}")
                return True

        return False

    def _wait_for_file_completion(self, file_path):
        """等待文件写入完成"""
        logger.info(f"检测到新文件: {file_path}，等待写入完成...")

        # 更新状态
        global TRANSFER_STATUS
        file_name = os.path.basename(file_path)
        TRANSFER_STATUS[file_name] = {
            "status": "waiting",
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "file_path": file_path,
            "progress": "0%",
            "message": "等待文件写入完成"
        }
        self._save_status()

        # 简单的方法：检查文件大小是否稳定
        last_size = -1
        current_size = os.path.getsize(file_path)

        while last_size != current_size:
            time.sleep(2)  # 等待2秒
            last_size = current_size
            if os.path.exists(file_path):  # 确保文件仍然存在
                current_size = os.path.getsize(file_path)
                # 更新状态
                TRANSFER_STATUS[file_name]["progress"] = "0%"
                TRANSFER_STATUS[file_name]["message"] = f"等待文件写入完成 ({current_size} 字节)"
                self._save_status()
            else:
                logger.warning(f"文件在等待过程中被删除: {file_path}")
                TRANSFER_STATUS[file_name]["status"] = "failed"
                TRANSFER_STATUS[file_name]["message"] = "文件在等待过程中被删除"
                self._save_status()
                return False

        logger.info(f"文件写入完成: {file_path} (大小: {current_size} 字节)")
        TRANSFER_STATUS[file_name]["message"] = f"文件写入完成 (大小: {current_size} 字节)"
        self._save_status()
        return True

    def _transfer_file(self, file_path):
        """传输文件到目标服务器"""
        file_name = os.path.basename(file_path)

        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"源文件不存在: {file_path}")
                return False

            logger.info(f"开始传输文件: {file_path} 到 {self.target_user}@{self.target_host}:{self.target_path}")

            # 更新状态
            global TRANSFER_STATUS
            TRANSFER_STATUS[file_name] = {
                "status": "transferring",
                "progress": "0%",
                "message": "开始传输",
                "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": "",
                "file_path": file_path
            }
            self._save_status()

            # 构建rsync命令
            rsync_cmd = ["rsync", "-avz", "--progress"]
            if self.ssh_key:
                rsync_cmd.extend(["-e", f"ssh -i {self.ssh_key} -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"])
                ssh_cmd = f"ssh -i {self.ssh_key} -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"
            else:
                rsync_cmd.extend(["-e", f"ssh -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"])
                ssh_cmd = f"ssh -p {self.ssh_port} -o StrictHostKeyChecking=no -o ConnectTimeout=10"

            # 添加源文件和目标路径，使用相对路径保留目录结构
            # 获取监控目录的基础路径
            base_path = self.path
            # 计算相对路径
            rel_path = os.path.relpath(file_path, base_path)
            # 创建目标路径
            target_full_path = os.path.join(self.target_path, os.path.dirname(rel_path))

            # 确保目标目录存在
            mkdir_cmd = [
                "ssh" if not self.ssh_key else "ssh",
                "-i", self.ssh_key if self.ssh_key else "",
                "-p", self.ssh_port,
                "-o", "StrictHostKeyChecking=no",
                "-o", "ConnectTimeout=10",
                f"{self.target_user}@{self.target_host}",
                f"mkdir -p {target_full_path}"
            ]
            # 移除空参数
            mkdir_cmd = [arg for arg in mkdir_cmd if arg]

            logger.debug(f"执行命令: {' '.join(mkdir_cmd)}")
            try:
                subprocess.run(mkdir_cmd, check=True)
                logger.debug(f"成功创建远程目录: {target_full_path}")
            except subprocess.CalledProcessError as e:
                logger.error(f"创建远程目录失败: {str(e)}")
                TRANSFER_STATUS[file_name]["status"] = "failed"
                TRANSFER_STATUS[file_name]["message"] = f"创建远程目录失败: {str(e)}"
                TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()
                return False

            # 添加源文件和目标路径
            rsync_cmd.append(file_path)
            rsync_cmd.append(f"{self.target_user}@{self.target_host}:{target_full_path}/")

            # 执行rsync命令
            logger.debug(f"执行命令: {' '.join(rsync_cmd)}")

            # 重试机制
            max_retries = 3
            retry_count = 0
            success = False

            while retry_count < max_retries and not success:
                if retry_count > 0:
                    logger.info(f"重试传输文件 ({retry_count}/{max_retries}): {file_path}")

                # 检查文件是否存在
                if not os.path.exists(file_path):
                    logger.warning(f"文件在传输前被删除，跳过: {file_path}")
                    TRANSFER_STATUS[file_name]["status"] = "failed"
                    TRANSFER_STATUS[file_name]["message"] = "文件在传输前被删除"
                    TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self._save_status()
                    return False

                try:
                    # 使用Popen实时获取输出
                    process = subprocess.Popen(
                        rsync_cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        bufsize=1,
                        universal_newlines=True
                    )

                    # 读取输出并更新进度
                    for line in process.stdout:
                        line = line.strip()
                        logger.debug(line)

                        # 尝试从输出中提取进度信息
                        if "%" in line and "to-check" in line:
                            parts = line.split()
                            for part in parts:
                                if "%" in part:
                                    progress = part
                                    TRANSFER_STATUS[file_name]["progress"] = progress
                                    TRANSFER_STATUS[file_name]["message"] = f"传输中: {progress}"
                                    self._save_status()
                                    break

                    # 等待进程完成
                    process.wait()
                    stderr = process.stderr.read()

                    if process.returncode == 0:
                        logger.info(f"文件传输成功: {file_path}")

                        # 如果启用完整性校验
                        if self.enable_integrity_check:
                            local_md5 = self._calculate_md5(file_path)
                            remote_file_full_path = os.path.join(target_full_path, os.path.basename(file_path))
                            remote_md5 = self._get_remote_md5(remote_file_full_path)

                            if local_md5 and remote_md5 and local_md5 == remote_md5:
                                logger.info(f"文件完整性校验成功: {file_path}")
                                TRANSFER_STATUS[file_name]["status"] = "success"
                                TRANSFER_STATUS[file_name]["progress"] = "100%"
                                TRANSFER_STATUS[file_name]["message"] = "传输成功 (已校验)"
                                TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                self._save_status()

                                # 如果允许删除，则删除原始文件
                                if self.delete_after_transfer:
                                    try:
                                        if os.path.exists(file_path):
                                            os.remove(file_path)
                                            logger.info(f"原始文件已删除: {file_path}")
                                        else:
                                            logger.warning(f"原始文件已不存在，无需删除: {file_path}")
                                    except Exception as e:
                                        logger.warning(f"删除原始文件失败: {file_path}, 错误: {str(e)}")
                                else:
                                    logger.info(f"根据配置，不删除原始文件: {file_path}")

                                success = True
                                return True
                            else:
                                logger.warning(f"文件完整性校验失败: {file_path}. 本地MD5: {local_md5}, 远程MD5: {remote_md5}")
                                TRANSFER_STATUS[file_name]["status"] = "failed"
                                TRANSFER_STATUS[file_name]["message"] = "传输成功但完整性校验失败"
                                TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                self._save_status()
                                retry_count += 1
                        else:
                            TRANSFER_STATUS[file_name]["status"] = "success"
                            TRANSFER_STATUS[file_name]["progress"] = "100%"
                            TRANSFER_STATUS[file_name]["message"] = "传输成功"
                            TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            self._save_status()

                            # 如果允许删除，则删除原始文件
                            if self.delete_after_transfer:
                                try:
                                    if os.path.exists(file_path):
                                        os.remove(file_path)
                                        logger.info(f"原始文件已删除: {file_path}")
                                    else:
                                        logger.warning(f"原始文件已不存在，无需删除: {file_path}")
                                except Exception as e:
                                    logger.warning(f"删除原始文件失败: {file_path}, 错误: {str(e)}")
                            else:
                                logger.info(f"根据配置，不删除原始文件: {file_path}")

                            success = True
                            return True
                    else:
                        logger.warning(f"文件传输失败: {file_path}")
                        logger.warning(f"错误信息: {stderr}")
                        retry_count += 1
                except Exception as e:
                    logger.warning(f"传输文件时发生错误: {str(e)}")
                    retry_count += 1

            if not success:
                logger.error(f"文件传输失败，已达到最大重试次数: {file_path}")
                TRANSFER_STATUS[file_name]["status"] = "failed"
                TRANSFER_STATUS[file_name]["message"] = f"传输失败，已重试 {max_retries} 次"
                TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self._save_status()
                return False

        except Exception as e:
            logger.exception(f"传输文件时发生错误: {str(e)}")
            TRANSFER_STATUS[file_name]["status"] = "failed"
            TRANSFER_STATUS[file_name]["message"] = f"传输错误: {str(e)}"
            TRANSFER_STATUS[file_name]["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self._save_status()
            return False

    def _monitor_logging_file(self, file_path):
        """监控logging.jsonl文件，如果30分钟内没有变化，则传输整个训练目录"""
        logger.info(f"开始监控日志文件: {file_path}")

        # 获取父目录（训练目录）
        train_dir = os.path.dirname(file_path)
        train_dir_name = os.path.basename(train_dir)

        # 记录初始文件大小和修改时间
        initial_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        initial_mtime = os.path.getmtime(file_path) if os.path.exists(file_path) else 0

        # 记录监控开始时间
        start_time = time.time()

        # 监控间隔（秒）
        check_interval = 60  # 每分钟检查一次

        # 无变化持续时间（秒）
        no_change_duration = 30 * 60  # 30分钟

        # 上次变化时间
        last_change_time = start_time

        logger.info(f"监控日志文件: {file_path}, 初始大小: {initial_size}, 初始修改时间: {initial_mtime}")

        try:
            while True:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    logger.warning(f"日志文件不存在，停止监控: {file_path}")
                    return

                # 获取当前文件大小和修改时间
                current_size = os.path.getsize(file_path)
                current_mtime = os.path.getmtime(file_path)

                # 检查文件是否有变化
                if current_size != initial_size or current_mtime != initial_mtime:
                    logger.info(f"日志文件有变化: {file_path}, 大小: {initial_size} -> {current_size}, 修改时间: {initial_mtime} -> {current_mtime}")
                    initial_size = current_size
                    initial_mtime = current_mtime
                    last_change_time = time.time()

                # 检查是否超过无变化持续时间
                current_time = time.time()
                if current_time - last_change_time >= no_change_duration:
                    logger.info(f"日志文件 {file_path} 已经 {no_change_duration/60} 分钟没有变化，认为训练已结束")

                    # 传输整个训练目录
                    logger.info(f"开始传输整个训练目录: {train_dir}")
                    self._transfer_directory(train_dir)

                    # 停止监控
                    return

                # 等待下一次检查
                time.sleep(check_interval)

        except Exception as e:
            logger.exception(f"监控日志文件时发生错误: {str(e)}")

    def _monitor_train_directory(self, directory_path):
        """监控训练目录，等待logging.jsonl文件出现，然后监控该文件"""
        logger.info(f"开始监控训练目录: {directory_path}")

        # 检查目录是否存在
        if not os.path.exists(directory_path):
            logger.error(f"训练目录不存在: {directory_path}")
            self.processing_files.discard(directory_path)
            return

        # 检查目录名称是否符合v[数字]-[日期时间]格式
        basename = os.path.basename(directory_path)
        v_pattern = re.compile(r'^v\d+-\d{8}-\d{6}$')
        if not v_pattern.match(basename):
            logger.error(f"目录名称不符合v[数字]-[日期时间]格式: {directory_path}")
            self.processing_files.discard(directory_path)
            return

        # 检查是否有logging.jsonl文件
        logging_file = os.path.join(directory_path, "logging.jsonl")

        # 监控间隔（秒）
        check_interval = 30  # 每30秒检查一次

        # 最大监控时间（小时）
        max_monitor_hours = 24
        start_time = time.time()
        max_monitor_time = start_time + max_monitor_hours * 3600

        logger.info(f"等待日志文件出现: {logging_file}")

        try:
            while time.time() < max_monitor_time:
                # 检查目录是否存在
                if not os.path.exists(directory_path):
                    logger.warning(f"训练目录不存在，停止监控: {directory_path}")
                    self.processing_files.discard(directory_path)
                    return

                # 检查是否有logging.jsonl文件
                if os.path.exists(logging_file) and os.path.isfile(logging_file):
                    logger.info(f"检测到日志文件: {logging_file}，开始监控")
                    # 开始监控日志文件
                    self._monitor_logging_file(logging_file)
                    return

                # 等待下一次检查
                time.sleep(check_interval)

            # 如果超过最大监控时间仍未发现日志文件
            logger.warning(f"超过最大监控时间 ({max_monitor_hours} 小时)，仍未发现日志文件: {logging_file}")
            self.processing_files.discard(directory_path)

        except Exception as e:
            logger.exception(f"监控训练目录时发生错误: {str(e)}")
            self.processing_files.discard(directory_path)

def show_status():
    """显示当前传输状态"""
    if not os.path.exists(STATUS_FILE):
        print("没有传输记录")
        return

    try:
        with open(STATUS_FILE, 'r') as f:
            status = json.load(f)

        if not status:
            print("没有传输记录")
            return

        print("\n当前传输状态:")
        print("-" * 80)
        print(f"{'文件名':<30} {'状态':<12} {'进度':<10} {'信息':<30}")
        print("-" * 80)

        for file_name, info in status.items():
            print(f"{file_name:<30} {info.get('status', 'unknown'):<12} {info.get('progress', 'N/A'):<10} {info.get('message', ''):<30}")

        print("-" * 80)
    except Exception as e:
        print(f"读取状态文件失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="监控检查点文件并自动传输到远程服务器")
    parser.add_argument("--watch-dir", default="/root/autodl-tmp/port", help="要监控的目录")
    parser.add_argument("--target-host", required=True, help="目标主机IP或域名")
    parser.add_argument("--target-user", required=True, help="目标主机用户名")
    parser.add_argument("--target-path", required=True, help="目标主机上的路径")
    parser.add_argument("--ssh-key", help="SSH私钥文件路径")
    parser.add_argument("--ssh-port", default="22", help="SSH端口号")
    parser.add_argument("--file-pattern", help="文件名模式，例如 '*.pt,*.mp4'")
    parser.add_argument("--status", action="store_true", help="显示当前传输状态并退出")
    parser.add_argument("--delete-after-transfer", action="store_true", default=True,
                        help="传输成功后是否删除源文件/目录 (默认: True)")
    parser.add_argument("--enable-integrity-check", action="store_true", default=False,
                        help="传输成功后是否进行文件完整性校验 (默认: False)")

    args = parser.parse_args()

    # 如果只是查看状态
    if args.status:
        show_status()
        return

    watch_dir = args.watch_dir

    if not os.path.exists(watch_dir):
        logger.error(f"监控目录不存在: {watch_dir}")
        sys.exit(1)

    logger.info(f"开始监控目录: {watch_dir}")
    logger.info(f"目标服务器: {args.target_user}@{args.target_host}:{args.target_path}")
    logger.info(f"传输成功后删除源文件/目录: {args.delete_after_transfer}")
    logger.info(f"启用文件完整性校验: {args.enable_integrity_check}")

    # 创建事件处理器
    event_handler = CheckpointHandler(
        args.target_host,
        args.target_user,
        args.target_path,
        args.ssh_key,
        args.file_pattern,
        args.ssh_port,
        args.delete_after_transfer,
        args.enable_integrity_check # 传递新参数
    )
    event_handler.path = watch_dir  # 设置监控目录的基础路径

    # 创建观察者
    observer = Observer()
    observer.schedule(event_handler, watch_dir, recursive=True)
    observer.start()

    try:
        # 处理现有文件
        logger.info("检查现有文件...")
        for root, dirs, files in os.walk(watch_dir):
            # 排除runs目录
            if "/runs/" in root or root.endswith("/runs"):
                logger.debug(f"排除TensorBoard目录: {root}")
                continue

            # 处理现有目录
            for dir_name in dirs:
                # 排除runs目录
                if dir_name == "runs":
                    logger.debug(f"排除TensorBoard目录: {os.path.join(root, dir_name)}")
                    continue

                dir_path = os.path.join(root, dir_name)
                # 再次检查是否为runs目录
                if "/runs/" in dir_path or dir_path.endswith("/runs"):
                    logger.debug(f"排除TensorBoard目录: {dir_path}")
                    continue

                logger.info(f"处理现有目录: {dir_path}")
                # 只有当目录匹配模式时才处理
                if event_handler._match_pattern(dir_path) and dir_path not in event_handler.processing_files:
                    event_handler.processing_files.add(dir_path)
                    # 启动一个新线程处理目录
                    thread = threading.Thread(target=event_handler._process_directory, args=(dir_path,))
                    thread.daemon = True
                    thread.start()

            # 处理现有文件
            for file_name in files:
                file_path = os.path.join(root, file_name)
                # 排除runs目录中的文件
                if "/runs/" in file_path:
                    logger.debug(f"排除TensorBoard文件: {file_path}")
                    continue

                # 检查文件模式匹配
                if event_handler._match_pattern(file_path) and file_path not in event_handler.processing_files:
                    logger.info(f"处理现有文件: {file_path}")
                    event_handler.processing_files.add(file_path)
                    # 启动一个新线程处理文件
                    thread = threading.Thread(target=event_handler._process_file, args=(file_path,))
                    thread.daemon = True
                    thread.start()

        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.exception(f"发生错误: {str(e)}")
    finally:
        observer.stop()
        observer.join()

if __name__ == "__main__":
    main()
