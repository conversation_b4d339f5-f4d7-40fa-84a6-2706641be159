#!/usr/bin/env python3
"""
测试delete_after_transfer参数解析是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import argparse

def test_parameter_parsing():
    """测试参数解析逻辑"""
    
    # 模拟checkpoint_watcher.py中的参数解析逻辑
    parser = argparse.ArgumentParser(description="测试参数解析")
    parser.add_argument("--delete-after-transfer", type=str, default="true",
                        help="传输成功后是否删除源文件/目录 (默认: true, 可选: true/false)")
    
    # 测试不同的参数值
    test_cases = [
        (["--delete-after-transfer", "false"], False),
        (["--delete-after-transfer", "true"], True),
        (["--delete-after-transfer", "False"], False),
        (["--delete-after-transfer", "True"], True),
        (["--delete-after-transfer", "0"], False),
        (["--delete-after-transfer", "1"], True),
        (["--delete-after-transfer", "no"], False),
        (["--delete-after-transfer", "yes"], True),
        (["--delete-after-transfer", "off"], False),
        (["--delete-after-transfer", "on"], True),
        ([], True),  # 默认值
    ]
    
    print("测试delete_after_transfer参数解析:")
    print("-" * 50)
    
    for args, expected in test_cases:
        try:
            parsed_args = parser.parse_args(args)
            delete_after_transfer = parsed_args.delete_after_transfer.lower() in ['true', '1', 'yes', 'on']
            
            status = "✅ PASS" if delete_after_transfer == expected else "❌ FAIL"
            print(f"{status} | 参数: {args if args else '(默认)'} | 期望: {expected} | 实际: {delete_after_transfer}")
            
        except Exception as e:
            print(f"❌ ERROR | 参数: {args} | 错误: {str(e)}")
    
    print("-" * 50)
    print("测试完成!")

if __name__ == "__main__":
    test_parameter_parsing()
