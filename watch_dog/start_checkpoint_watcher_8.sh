#!/bin/bash
# 启动检查点监控服务

# 配置参数
TARGET_HOST="*************"  # 目标主机IP或域名
TARGET_USER="root"     # 目标主机用户名
TARGET_PATH="/data1/wzy/NIPS/model"     # 目标主机上的保存路径
SSH_KEY="/home/<USER>/wzy/NIPS/watch_dog/id_rsa_8"                      # SSH密钥文件路径，留空则使用密码认证
SSH_PORT="3188"                                 # SSH端口号
FILE_PATTERN="*"     # 文件名匹配模式，只匹配checkpoint文件
watch_dir="/home/<USER>/wzy/NIPS/port"

# 构建命令
CMD="python /home/<USER>/wzy/NIPS/watch_dog/checkpoint_watcher.py --watch-dir $watch_dir --target-host $TARGET_HOST --target-user $TARGET_USER --target-path $TARGET_PATH --ssh-port $SSH_PORT"

# 添加可选参数
if [ ! -z "$SSH_KEY" ]; then
    CMD="$CMD --ssh-key $SSH_KEY"
fi

if [ ! -z "$FILE_PATTERN" ]; then
    CMD="$CMD --file-pattern \"$FILE_PATTERN\""
fi

# 输出配置信息
echo "启动检查点监控服务，配置如下："
echo "监控目录: $watch_dir"
echo "目标主机: $TARGET_USER@$TARGET_HOST:$TARGET_PATH"
echo "SSH端口: $SSH_PORT"
echo "SSH密钥: $SSH_KEY"
echo "文件模式: $FILE_PATTERN"
echo ""
echo "执行命令: $CMD"
echo ""

# 执行命令
echo "按Enter键启动服务，或Ctrl+C取消..."
read

# 使用nohup在后台运行
eval "nohup $CMD > checkpoint_watcher.log 2>&1 &"
echo "服务已在后台启动，日志保存在checkpoint_watcher.log"
echo "进程ID: $!"
echo "使用 'ps aux | grep checkpoint_watcher' 查看进程状态"
