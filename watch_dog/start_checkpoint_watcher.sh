#!/bin/bash
# 启动检查点监控服务

# 权限准备
# cd /home/<USER>/wzy/NIPS/watch_dog
# chmod +x start_checkpoint_watcher_8.sh
# chmod 600 id_rsa_8


# 配置参数
TARGET_HOST="*************"  # 目标主机IP或域名
TARGET_USER="root"     # 目标主机用户名
TARGET_PATH="/data1/wzy/test"     # 目标主机上的保存路径
SSH_KEY="/home/<USER>/wzy/NIPS/watch_dog/id_rsa_8"                      # SSH密钥文件路径，留空则使用密码认证
SSH_PORT="3188"                                 # SSH端口号
# FILE_PATTERN="*"     # 文件名匹配模式，只匹配checkpoint文件
WATCH_DIR="/home/<USER>/wzy/NIPS/port" # 监控目录
DELETE_SOURCE_AFTER_TRANSFER="false" # 传输成功后是否删除源文件，默认为false (不删除)

# 构建命令
CMD="python checkpoint_watcher.py --watch-dir $WATCH_DIR --target-host $TARGET_HOST --target-user $TARGET_USER --target-path $TARGET_PATH"

# 添加可选参数
if [ ! -z "$SSH_KEY" ]; then
    CMD="$CMD --ssh-key $SSH_KEY"
fi

if [ ! -z "$FILE_PATTERN" ]; then
    CMD="$CMD --file-pattern \"$FILE_PATTERN\""
fi

if [ "$DELETE_SOURCE_AFTER_TRANSFER" = "false" ]; then
    CMD="$CMD --delete-after-transfer false"
fi

# 输出配置信息
echo "启动检查点监控服务，配置如下："
echo "监控目录: $WATCH_DIR"
echo "目标主机: $TARGET_USER@$TARGET_HOST:$TARGET_PATH"
echo "SSH端口: $SSH_PORT"
echo "文件模式: $FILE_PATTERN"
echo "删除源文件: $DELETE_SOURCE_AFTER_TRANSFER"
echo ""
echo "执行命令: $CMD"
echo ""

# 执行命令
echo "按Enter键启动服务，或Ctrl+C取消..."
read

# 使用nohup在后台运行
eval "nohup $CMD > checkpoint_watcher.log 2>&1 &"
echo "服务已在后台启动，日志保存在checkpoint_watcher.log"
echo "进程ID: $!"
echo "使用 'ps aux | grep checkpoint_watcher' 查看进程状态"
echpo "使用 'tail -f checkpoint_watcher.log' 查看日志输出"
echpo "pkill -f checkpoint_watcher.py 停止服务"
