# 检查点文件自动传输系统

## 项目概述

检查点文件自动传输系统是一个用于自动监控、传输和管理机器学习模型检查点文件的工具。系统能够实时监控指定目录中的新文件，将其安全地传输到远程服务器，并在传输成功后自动删除原始文件，以节省本地存储空间。

### 主要功能

- 实时监控指定目录中的新文件
- 支持文件模式匹配（例如 `*.pt` 或 `checkpoint-*`）
- 使用rsync进行高效文件传输
- 实时显示传输进度
- 传输状态追踪和查询
- 自动删除成功传输的文件
- 详细的日志记录
- 多线程文件处理

## 系统组件

系统由以下几个主要组件组成：

1. `checkpoint_watcher.py` - 主要的监控和传输脚本
2. `start_checkpoint_watcher.sh` - 启动监控服务的脚本
3. `check_status.sh` - 查看传输状态的脚本
4. `checkpoint-watcher.service` - systemd服务配置文件
5. `setup_checkpoint_watcher.sh` - 安装依赖的脚本

## 安装步骤

### 1. 安装依赖

运行以下命令安装必要的依赖：

```bash
chmod +x setup_checkpoint_watcher.sh
./setup_checkpoint_watcher.sh
```

这将安装以下依赖：
- Python 3
- watchdog库
- rsync

### 2. 配置SSH密钥

确保您有正确的SSH密钥用于连接远程服务器：

```bash
# 设置正确的权限
chmod 600 /root/id_rsa_temp
chmod 600 /root/watch_dog/id_rsa_8
chmod 600 /home/<USER>/wzy/NIPS/watch_dog/id_rsa_8
```

### 3. 配置服务

如果您想将监控服务设置为系统服务，可以使用提供的systemd服务文件：

```bash
sudo cp checkpoint-watcher.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable checkpoint-watcher.service
```

## 配置选项

在`start_checkpoint_watcher.sh`文件中，您可以修改以下配置参数：

```bash
TARGET_HOST="***********"  # 目标主机IP或域名
TARGET_USER="gaochen"      # 目标主机用户名
TARGET_PATH="/data4/gaochen/code/grpo/GRPO_7B"  # 目标主机上的保存路径
SSH_KEY="/root/id_rsa_temp"  # SSH密钥文件路径
SSH_PORT="35167"           # SSH端口号
FILE_PATTERN="*"           # 文件名匹配模式
```

## 使用方法

### 启动监控服务

```bash
cd /home/<USER>/wzy/NIPS/watch_dog
chmod +x start_checkpoint_watcher_8.sh
./home/<USER>/wzy/NIPS/watch_dog/start_checkpoint_watcher_8.sh
```

服务将在后台运行，并监控`/root/autodl-tmp/port`目录中的新文件。

### 查看传输状态

```bash
cd /root/watch_dog
chmod +x check_status.sh
./check_status.sh
```

这将显示当前所有文件的传输状态、进度和详细信息。

### 实时监控状态

如果您想实时监控传输状态，可以使用以下命令：

```bash
watch -n 2 "/root/miniconda3/bin/python /root/watch_dog/checkpoint_watcher.py --status"
```

这将每2秒更新一次状态信息。

### 作为系统服务运行

如果您已经配置了systemd服务，可以使用以下命令启动、停止和查看服务状态：

```bash
# 启动服务
sudo systemctl start checkpoint-watcher.service

# 停止服务
sudo systemctl stop checkpoint-watcher.service

# 查看服务状态
sudo systemctl status checkpoint-watcher.service

# 查看服务日志
sudo journalctl -u checkpoint-watcher.service
```

## 故障排除

### 连接问题

如果遇到连接超时或SSH连接问题：

1. 确认目标服务器的SSH服务是否在指定端口上运行：
   ```bash
   nc -zv *********** 35167
   ```

2. 测试SSH连接：
   ```bash
   ssh -i /root/id_rsa_temp -p 35167 -o ConnectTimeout=10 gaochen@*********** echo "连接测试成功"
   ```

3. 检查SSH密钥权限：
   ```bash
   chmod 600 /root/id_rsa_temp
   ```

4. 确认目标路径存在且有写入权限：
   ```bash
   ssh -i /root/id_rsa_temp -p 35167 gaochen@*********** "ls -la /data4/gaochen/code/grpo/GRPO_7B"
   ```

### 日志查看

查看日志文件获取详细错误信息：

```bash
tail -f checkpoint_watcher.log
```

### 进程管理

如果需要手动停止监控进程：

```bash
pkill -f checkpoint_watcher.py
```

## 状态文件

系统会在当前目录下创建一个`transfer_status.json`文件，用于记录所有文件的传输状态。您可以直接查看此文件了解详细信息：

```bash
cat transfer_status.json
```

## 高级用法

### 直接使用Python脚本

您可以直接使用Python脚本，并传递自定义参数：

```bash
/root/miniconda3/bin/python /root/watch_dog/checkpoint_watcher.py \
  --watch-dir /path/to/watch \
  --target-host example.com \
  --target-user username \
  --target-path /path/on/remote \
  --ssh-key /path/to/key \
  --file-pattern "*.pt"
```

### 添加到crontab

如果您想在系统启动时自动启动监控服务，可以将其添加到crontab：

```bash
crontab -e
```

然后添加以下行：

```
@reboot /root/watch_dog/start_checkpoint_watcher.sh
```

## 注意事项

- 确保SSH密钥的权限正确（600）
- 确保目标服务器上的目录存在且有写入权限
- 如果文件较大，传输可能需要一些时间，请耐心等待
- 系统会自动删除成功传输的文件，请确保您已经备份了重要文件
