# `watch_dog` 模块技术概览

本文档详细阐述了 `watch_dog` 模块的核心工作原理、主要组件、交互关系、关键算法、数据流以及配置选项，旨在帮助开发者和维护者深入理解其内部实现。

## 1. 模块简介

`watch_dog` 模块是一个基于 Python 的文件系统监控和自动化传输工具。它主要用于监控本地指定目录中的文件和目录创建事件，特别是针对机器学习训练过程中生成的检查点（checkpoint）文件和日志文件。一旦检测到符合条件的文件或目录，它将自动通过 SSH 和 rsync 工具将其安全、高效地传输到远程服务器。

## 2. 核心工作原理

`watch_dog` 的核心工作原理可以概括为**事件驱动的异步文件同步**。它利用 `watchdog` 库实时监听文件系统事件，并通过多线程并发处理文件和目录的传输任务。为了确保数据完整性，它在传输前会进行文件稳定性检查，并针对传输失败提供重试机制。此外，它还包含智能的训练日志监控功能，能够判断训练是否结束并触发整个训练目录的传输。

## 3. 主要组件和交互关系

`watch_dog` 模块主要由以下组件构成：

*   **`checkpoint_watcher.py`**:
    *   **核心脚本**: 整个系统的入口点和主要逻辑实现。
    *   **命令行解析**: 使用 `argparse` 处理用户传入的监控目录、目标主机、SSH 配置等参数。
    *   **观察者模式**: 初始化 `watchdog.observers.Observer` 和 `CheckpointHandler`。
    *   **状态管理**: 负责加载和保存 `transfer_status.json` 文件。
    *   **多线程管理**: 启动独立的线程来处理文件/目录传输和日志监控任务。

*   **`CheckpointHandler` (继承自 `watchdog.events.FileSystemEventHandler`)**:
    *   **事件处理器**: 响应 `watchdog` 观察者捕获的文件系统事件（特别是 `on_created`）。
    *   **文件/目录类型识别**: 根据文件/目录的命名模式（如 `v[数字]-[日期时间]` 训练目录、`checkpoint-[数字]` 检查点目录、`logging.jsonl` 日志文件）进行分类处理。
    *   **任务分发**: 根据识别结果，在单独的线程中调用 `_process_file`、`_process_directory`、`_monitor_logging_file` 或 `_monitor_train_directory` 方法。
    *   **状态更新**: 在文件传输的不同阶段更新全局 `TRANSFER_STATUS`。

*   **`watchdog.observers.Observer`**:
    *   **文件系统观察者**: `watchdog` 库的核心组件，负责在后台持续监听指定目录的文件系统事件。
    *   **事件调度**: 当检测到事件时，将事件分发给注册的 `FileSystemEventHandler`。

*   **`subprocess` (Python 标准库)**:
    *   **外部命令执行器**: 用于在 Python 脚本中调用外部命令行工具，如 `ssh` 和 `rsync`。
    *   **实时输出**: 使用 `subprocess.Popen` 来实时捕获 `rsync` 命令的输出，以便更新传输进度。

*   **`rsync` (命令行工具)**:
    *   **高效文件同步**: 实际执行文件和目录传输的工具。它能够高效地同步文件，只传输差异部分，并支持断点续传。
    *   **SSH 集成**: 通过 `-e "ssh ..."` 参数与 SSH 协议结合，实现加密传输。

*   **`ssh` (命令行工具)**:
    *   **安全远程连接**: 用于建立与远程服务器的安全连接。
    *   **目录创建**: 在传输文件前，通过 `ssh` 命令在远程服务器上创建必要的目录结构。
    *   **身份验证**: 支持通过私钥 (`-i`) 进行免密码身份验证。

*   **`threading` (Python 标准库)**:
    *   **并发处理**: 用于创建和管理独立的线程。
    *   **非阻塞操作**: 确保文件系统监控不会被长时间的传输操作阻塞，允许系统同时处理多个传输任务。

*   **`transfer_status.json`**:
    *   **状态持久化**: 一个 JSON 文件，用于持久化存储当前正在进行或已完成的文件传输的状态信息（如文件名、状态、进度、开始/结束时间、消息）。
    *   **故障恢复**: 允许程序在重启后恢复之前的传输状态。

### 4. 技术架构图/流程图

```mermaid
graph TD
    A[本地监控目录] -->|文件/目录创建事件| B(watchdog Observer)
    B --> C{CheckpointHandler.on_created}
    C -->|是 vYYYYMMDD-HHMMSS 目录| D1(启动 _monitor_train_directory 线程)
    C -->|是 checkpoint-N 目录| D2(启动 _process_directory 线程)
    C -->|是 logging.jsonl 文件| D3(启动 _monitor_logging_file 线程)
    C -->|其他文件/目录| D4(启动 _process_file / _process_directory 线程)

    D1 --> E1{监控 logging.jsonl 出现}
    E1 --> F1{logging.jsonl 30分钟无变化?}
    F1 -- 是 --> G1(触发 _process_directory 传输整个训练目录)
    F1 -- 否 --> E1

    D2 --> H1{等待目录文件稳定}
    D3 --> H2{等待文件写入完成}
    D4 --> H3{等待文件/目录稳定}

    H1/H2/H3 --> I{执行 rsync 传输}
    I --> J[远程目标服务器]
    I --> K(更新 transfer_status.json)
    J --> L[传输完成]
    L --> M(删除本地源文件/目录)

    SubGraph 辅助功能
        N[check_status.sh] --> O(调用 checkpoint_watcher.py --status)
        O --> P(读取 transfer_status.json)
        P --> Q[显示传输状态]
    End
```

### 5. 关键算法和数据流

#### 5.1 文件/目录稳定性检查

为了避免传输正在写入的文件或目录，系统实现了稳定性检查机制：

*   **文件**: `_wait_for_file_completion(file_path)` 方法通过周期性（每 2 秒）检查文件大小是否发生变化来判断文件是否已完成写入。如果文件大小在连续两次检查中保持不变，则认为文件已稳定。
*   **目录**: `_has_active_writes(directory_path)` 方法递归检查目录中的所有文件，判断是否有文件正在被写入。对于 `checkpoint-[数字]` 目录，系统会额外等待 30 秒，以确保模型保存操作完成。如果文件仍在写入，系统会进行多次尝试，并在达到最大尝试次数后，将目录重新加入处理队列稍后检查。

#### 5.2 日志文件监控与训练结束判断

`_monitor_logging_file(file_path)` 线程专门用于监控训练日志文件（`logging.jsonl`）。

*   **监控逻辑**: 它会定期（每分钟）检查日志文件的大小和修改时间。
*   **训练结束判断**: 如果日志文件在连续 30 分钟内没有发生任何变化，系统会推断训练过程可能已经结束。
*   **触发全目录传输**: 一旦判断训练结束，系统将自动触发对整个父训练目录（`v[数字]-[日期时间]` 格式）的传输，确保所有训练产物（包括最终检查点、日志等）都被完整地同步到远程服务器。

#### 5.3 传输重试机制

为了提高传输的健壮性，`_transfer_file` 和 `_transfer_directory` 方法都内置了重试逻辑：

*   **最大重试次数**: 默认为 3 次。
*   **重试间隔**: 每次重试前会等待一段时间（文件传输等待 5 秒，目录传输等待 10 秒）。
*   **错误处理**: 捕获 `subprocess.CalledProcessError` 和其他异常，并在失败时进行重试。

#### 5.4 状态管理

系统通过 `TRANSFER_STATUS` 全局字典和 `transfer_status.json` 文件来管理传输状态：

*   **状态字段**: 每个传输任务（文件或目录）的状态包括 `status` (waiting, transferring, success, failed, partial)、`progress`、`message`、`start_time` 和 `end_time`。
*   **持久化**: `_save_status()` 方法将 `TRANSFER_STATUS` 字典序列化为 JSON 格式并写入 `transfer_status.json`。
*   **加载**: `_load_status()` 方法在程序启动时从 `transfer_status.json` 加载之前的状态，实现状态的持久化和恢复。

### 6. 配置选项和参数

`checkpoint_watcher.py` 脚本支持以下命令行参数：

*   `--watch-dir <path>`: **必选**。要监控的本地目录路径。默认为 `/root/autodl-tmp/port`。
*   `--target-host <host>`: **必选**。目标远程主机的 IP 地址或域名。
*   `--target-user <user>`: **必选**。连接目标远程主机使用的用户名。
*   `--target-path <path>`: **必选**。目标远程主机上用于存放传输文件的路径。
*   `--ssh-key <path>`: **可选**。SSH 私钥文件的路径，用于免密码认证。
*   `--ssh-port <port>`: **可选**。SSH 连接的端口号。默认为 `22`。
*   `--file-pattern <pattern>`: **可选**。文件名匹配模式，例如 `'*.pt'` 或 `'checkpoint-*'`。如果指定，只有匹配该模式的文件才会被传输。
*   `--status`: **可选**。如果指定此参数，程序将显示当前的传输状态并退出，不会启动监控。

### 7. 使用示例

#### 7.1 启动监控

```bash
python checkpoint_watcher.py \
    --watch-dir /path/to/local/data \
    --target-host your_remote_host_ip \
    --target-user your_username \
    --target-path /path/to/remote/storage \
    --ssh-key ~/.ssh/id_rsa_your_key \
    --ssh-port 22
```

#### 7.2 查看传输状态

```bash
python checkpoint_watcher.py --status
# 或者使用辅助脚本
./check_status.sh
```

#### 7.3 作为 Systemd 服务运行

为了在后台持续运行，可以将 `checkpoint_watcher.py` 配置为 Systemd 服务。参考 `checkpoint-watcher.service` 文件进行配置。

```ini
# 示例：checkpoint-watcher.service
[Unit]
Description=Checkpoint Watcher Service
After=network.target

[Service]
ExecStart=/usr/bin/python3 /path/to/your/watch_dog/checkpoint_watcher.py --watch-dir /root/autodl-tmp/port --target-host your_remote_host_ip --target-user your_username --target-path /path/to/remote/storage --ssh-key /path/to/your/id_rsa_key
WorkingDirectory=/path/to/your/watch_dog
StandardOutput=append:/path/to/your/watch_dog/checkpoint_watcher_output.log
StandardError=append:/path/to/your/watch_dog/checkpoint_watcher_error.log
Restart=always
User=your_user
Group=your_group

[Install]
WantedBy=multi-user.target
```

请注意，上述 `ExecStart` 中的路径和参数需要根据您的实际部署情况进行修改。

---
