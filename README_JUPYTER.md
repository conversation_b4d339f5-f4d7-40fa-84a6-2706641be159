# JupyterLab 使用指南

## 安装

使用阿里源安装JupyterLab：

```bash
pip install jupyterlab --index-url https://mirrors.aliyun.com/pypi/simple/
```

## 启动

### 普通启动

```bash
jupyter-lab --allow-root
```

注意：由于安全原因，JupyterLab默认不允许以root用户身份运行。使用`--allow-root`参数可以绕过这个限制。

### 后台运行

如果希望在断开SSH连接后JupyterLab仍然继续运行，可以使用以下命令：

```bash
nohup jupyter-lab --allow-root > jupyter.log 2>&1 &
```

这将在后台启动JupyterLab，并将输出重定向到`jupyter.log`文件。

### 查看日志

```bash
    cat jupyter.log
```

## 访问

JupyterLab启动后，会生成一个带有token的URL，例如：

```
http://localhost:8888/lab?token=3722657a43b1a2c07f0ba2d04ad16b98e0a1ca29852946e9


http://localhost:8888/lab?token=0edce7e520aece2a5a3159e448c5d6e42b712f7d30663770
[I 2025-03-25 19:24:21.380 ServerApp] http://localhost:8888/lab
[I 2025-03-25 19:24:21.380 ServerApp]     http://127.0.0.1:8888/lab

http://localhost:8888/lab?token=d2a27e091d03949e9a4db6bc373230d16dfd9d83fe935751
http://127.0.0.1:8888/lab?token=d2a27e091d03949e9a4db6bc373230d16dfd9d83fe935751

http://localhost:8888/lab?token=294093f83a2db4af19790d9e3bdc890348e94e0bc87dafbe
http://127.0.0.1:8888/lab?token=294093f83a2db4af19790d9e3bdc890348e94e0bc87dafbe

http://localhost:8888/lab?token=9cab63474fda1543ac7dd6b31d5f6d249c1737f4a05b93be
http://127.0.0.1:8888/lab?token=9cab63474fda1543ac7dd6b31d5f6d249c1737f4a05b93be
```

将此URL复制到浏览器中即可访问JupyterLab。

## 停止

如果JupyterLab在前台运行，按`Ctrl+C`可以停止它。

如果JupyterLab在后台运行，可以使用以下命令找到并终止进程：

```bash
# 查找JupyterLab进程
ps aux | grep jupyter

# 终止进程
kill <进程ID>
```

## 注意事项

1. 如果服务器在本地网络，外部用户无法直接访问。需要配置端口转发或使用VPN。
2. 确保token不会被泄露，因为它可以用于访问您的JupyterLab。
3. 如果需要长期运行JupyterLab，建议将其配置为系统服务。
