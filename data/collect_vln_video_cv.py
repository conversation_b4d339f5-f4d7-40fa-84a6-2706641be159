import sys
import time
import airsim
import pygame
import numpy as np
import os
import cv2
import pandas as pd
from airsim_utils.coord_transformation import quaternion2eularian_angles

def create_new_folder():
    base_dir = "dataset_video"
    existing_folders = [int(name) for name in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, name)) and name.isdigit()]
    new_folder_name = str(max(existing_folders) + 1) if existing_folders else "0"
    new_folder_path = os.path.join(base_dir, new_folder_name)
    os.makedirs(new_folder_path)
    return new_folder_path, new_folder_name

def save_image(client, file_path):
    response = client.simGetImages([airsim.ImageRequest("0", airsim.ImageType.Scene, False, False)])
    img1d = np.frombuffer(response[0].image_data_uint8, dtype=np.uint8)
    if img1d.size == (response[0].height * response[0].width * 3):
        img_rgb = img1d.reshape(response[0].height, response[0].width, 3)
        cv2.imwrite(file_path, img_rgb)

def set_vehicle_pose(client, position, orientation):
    pose = airsim.Pose(airsim.Vector3r(*position), airsim.to_quaternion(*orientation))
    client.simSetVehiclePose(pose, True)

def set_camera_angle(client, angle):
    # 将角度转换为四元数设置相机姿态
    camera_pose = airsim.Pose(airsim.Vector3r(0, 0, 0), airsim.to_quaternion(angle * np.pi / 180, 0, 0))
    client.simSetCameraPose("0", camera_pose)

def move_relative(client, dx, dy, dz):
    """
    根据无人机当前朝向，计算相对移动的目标位置。
    dx, dy, dz 是相对于无人机局部坐标系的移动量。
    """
    pose = client.simGetVehiclePose()
    orientation = airsim.to_eularian_angles(pose.orientation)  # 获取当前朝向的欧拉角
    yaw = orientation[2]  # 获取偏航角（yaw）

    # 计算局部坐标系下的移动向量在世界坐标系中的分量
    forward = np.array([np.cos(yaw), np.sin(yaw), 0])  # 前方向量
    right = np.array([-np.sin(yaw), np.cos(yaw), 0])   # 右方向量
    up = np.array([0, 0, 1])                          # 上方向量

    # 将局部坐标系的移动量转换到世界坐标系
    move_vector = dx * forward + dy * right + dz * up
    new_position = np.array([pose.position.x_val, pose.position.y_val, pose.position.z_val]) + move_vector

    # 设置新的位置
    set_vehicle_pose(client, new_position, orientation)


def get_current_state(client):
    # get world frame pos and orientation
    # orientation is in roll, pitch, yaw format
    state = client.simGetGroundTruthKinematics()
    pos = state.position.to_numpy_array()
    ori = quaternion2eularian_angles(state.orientation)
    return pos, ori

# ---------- Pygame Settings ---------- #
pygame.init()
screen = pygame.display.set_mode((320, 240))  # 设置屏幕大小
pygame.display.set_caption('keyboard ctrl')  # 设置窗口标题
screen.fill((0, 0, 0))  # 屏幕填充RGB颜色
# ---------- AirSim Settings ---------- #
vehicle_name = ""
AirSim_client = airsim.VehicleClient()
AirSim_client.confirmConnection()

# 初始位置和姿态
# AirSim_client.simSetVehiclePose(
#     airsim.Pose(airsim.Vector3r(7481.66602, -3555.18677,   -53.36726), airsim.to_quaternion(0, 0, 0)),
#     True,  # 忽略碰撞
# )
# set_camera_angle(AirSim_client, 0)

# 初始化参数
commands_map = {
    pygame.K_UP: ('向前运动', (10, 0, 0)),
    pygame.K_DOWN: ('向后运动', (-10, 0, 0)),
    pygame.K_LEFT: ('向左运动', (0, -10, 0)),
    pygame.K_RIGHT: ('向右运动', (0, 10, 0)),
    pygame.K_w: ('向上运动', (0, 0, -10)),
    pygame.K_s: ('向下运动', (0, 0, 10)),
    pygame.K_a: ('向左旋转', -22.5),
    pygame.K_d: ('向右旋转', 22.5),
    pygame.K_f: ('向上看', 45),
    pygame.K_g: ('向下看', -45)
}

camera_angle = 0
current_folder = None

while True:
    time.sleep(0.02)
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            sys.exit()
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_j:
                # 按下 j 键，记录首帧
                current_folder, folder_name = create_new_folder()

                # 保存首帧
                initial_image_path = os.path.join(current_folder, "initial.png")
                save_image(AirSim_client, initial_image_path)
                print(f"Saved initial frame at {initial_image_path}")

                image_index = 0

                loc_series = pd.Series([np.append(np.concatenate(get_current_state(AirSim_client)), camera_angle)], index=[0])
                path_series = pd.Series(dtype="str")

            elif event.key == pygame.K_k and current_folder is not None:
                # 按下 k 键，结束记录
                loc_series.to_csv(os.path.join(current_folder, 'loc.csv'), header=False)
                path_series.to_csv(os.path.join(current_folder, 'path.csv'), header=False)

                # 弹出交互，询问导航目标
                goal = input("请输入当前路线的导航目标：")
                goal_path = os.path.join(current_folder, 'goal.txt')
                with open(goal_path, 'w') as f:
                    f.write(goal)
                print(f"导航目标已保存到 {goal_path}")

                print(f"Recording ended. All images saved in folder {current_folder}")
                current_folder = None

            elif event.key in commands_map:
                # 执行动作并记录完成后的帧
                command, value = commands_map[event.key]

                if command in ['向上看', '向下看']:
                    # 云台调整
                    pre_angle = camera_angle
                    camera_angle += value
                    camera_angle = max(-90, min(90, camera_angle))  # 限制云台角度在 -90 到 90 度之间
                    set_camera_angle(AirSim_client, camera_angle)
                else:
                    # 位置或旋转调整
                    if isinstance(value, tuple):  # 运动命令
                        dx, dy, dz = value
                        move_relative(AirSim_client, dx, dy, dz)
                    else:  # 旋转命令
                        yaw_change = value
                        pose = AirSim_client.simGetVehiclePose()
                        current_orientation = airsim.to_eularian_angles(pose.orientation)
                        new_orientation = [current_orientation[0], current_orientation[1], current_orientation[2] + np.radians(yaw_change)]
                        set_vehicle_pose(AirSim_client, [pose.position.x_val, pose.position.y_val, pose.position.z_val], new_orientation)

                if current_folder is not None:
                    # 保存动作完成后的帧
                    image_path = os.path.join(current_folder, f"{image_index}.png")
                    save_image(AirSim_client, image_path)
                    print(f"Saved frame {image_index} at {image_path}")
                    image_index += 1

                    path_series.loc[len(path_series)] = command
                    loc_series.loc[len(loc_series)] = np.append(np.concatenate(get_current_state(AirSim_client)), camera_angle)

    scan_wrapper = pygame.key.get_pressed()

    # press 'Esc' to quit
    if scan_wrapper[pygame.K_ESCAPE]:
        pygame.quit()
        sys.exit()
