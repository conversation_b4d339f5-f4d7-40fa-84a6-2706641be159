#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导航数据集处理与动作评分系统
功能：
1. 读取导航数据集，包括位置坐标、动作序列和图像
2. 为每个可能的动作计算评分
3. 将完整导航序列拆解为单步问答格式
"""

import os
import json
import logging
import argparse
import pandas as pd
import numpy as np
import math
from typing import Dict, List, Tuple, Any, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 动作映射
ACTION_MAPPING = {
    "向前运动": "向前运动",
    "向后运动": "向后运动",
    "向左运动": "向左运动",
    "向右运动": "向右运动",
    "向上运动": "向上运动",
    "向下运动": "向下运动",
    "向左旋转": "向左旋转",
    "向右旋转": "向右旋转",
    "向上看": "向上看",
    "向下看": "向下看"
}

# 可合并的动作（连续相同动作可合并）
MERGEABLE_MOVEMENTS = {
    "向前运动", "向后运动", "向左运动", "向右运动", "向上运动", "向下运动"
}

# 动作空间描述
ACTION_SPACE_DESC = """无人机可以执行以下动作：
- 向前运动：前进10单位
- 向后运动：后退10单位
- 向左运动：左移10单位
- 向右运动：右移10单位
- 向上运动：上升10单位
- 向下运动：下降10单位
- 向左旋转：左转22.5度
- 向右旋转：右转22.5度
- 向上看：向上看45度
- 向下看：向下看45度"""

# 系统提示内容
SYSTEM_CONTENT = """你是一个视觉导航决策系统。无人机可以执行以下动作：
- 向前运动
- 向后运动
- 向左运动
- 向右运动
- 向上运动
- 向下运动
- 向左旋转22.5度
- 向右旋转22.5度
- 向上看45度
- 向下看45度"""

# 动作参数映射
COMMANDS_MAP = {
    "向前运动": (10, 0, 0),
    "向后运动": (-10, 0, 0),
    "向左运动": (0, -10, 0),
    "向右运动": (0, 10, 0),
    "向上运动": (0, 0, -10),
    "向下运动": (0, 0, 10),
    "向左旋转": -22.5,  # 度
    "向右旋转": 22.5,   # 度
    "向上看": 45,       # 度
    "向下看": -45       # 度
}

# 所有可能的动作列表
ALL_ACTIONS = list(COMMANDS_MAP.keys())


def get_numeric_folders(base_path: str) -> List[str]:
    """
    获取所有数字命名的文件夹，并按数字顺序排序

    Args:
        base_path: 基础路径

    Returns:
        数字文件夹路径列表，按数字顺序排序
    """
    try:
        # 列出所有文件夹
        all_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]

        # 筛选出数字命名的文件夹
        numeric_folders = []
        for folder in all_folders:
            if folder.isdigit():
                numeric_folders.append(folder)

        # 按数字顺序排序
        numeric_folders.sort(key=int)

        # 转换为完整路径
        folder_paths = [os.path.join(base_path, folder) for folder in numeric_folders]

        logger.info(f"找到 {len(folder_paths)} 个数字文件夹")
        return folder_paths
    except Exception as e:
        logger.error(f"获取数字文件夹时出错: {e}")
        return []


def read_actions_from_csv(csv_path: str) -> List[str]:
    """
    从CSV文件中读取动作序列

    Args:
        csv_path: CSV文件路径

    Returns:
        动作列表
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path, header=None)

        # 检查列数
        if len(df.columns) >= 2:
            # 第二列是动作
            actions = df.iloc[:, 1].astype(str).tolist()
        else:
            # 只有一列，可能是没有标题的CSV
            actions = df.iloc[:, 0].astype(str).tolist()

        return actions
    except Exception as e:
        logger.error(f"读取动作CSV文件时出错: {e}")
        return []


def read_locations_from_csv(csv_path: str) -> List[np.ndarray]:
    """
    从CSV文件中读取位置坐标

    Args:
        csv_path: CSV文件路径

    Returns:
        位置坐标列表，每个元素是一个7维numpy数组 [x, y, z, rx, ry, yaw, pitch]
    """
    try:
        # 读取整个文件内容
        with open(csv_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式提取坐标
        import re
        pattern = r'(\d+),"\[(.*?)\]"'
        matches = re.findall(pattern, content, re.DOTALL)

        locations = []
        for _, coord_str in matches:
            # 清理字符串，移除换行符和多余空格
            clean_str = re.sub(r'\s+', ' ', coord_str).strip()
            # 解析为numpy数组
            coords = np.fromstring(clean_str, sep=' ')

            # 确保是7维向量
            if len(coords) == 7:
                locations.append(coords)
            else:
                logger.warning(f"坐标维度不正确: {len(coords)}, 值: {clean_str}")

        # 按索引排序
        locations.sort(key=lambda x: len(locations))

        logger.info(f"成功读取 {len(locations)} 个位置坐标")
        return locations
    except Exception as e:
        logger.error(f"读取位置CSV文件时出错: {e}")
        return []


def read_navigation_target(folder_path: str) -> str:
    """
    读取导航目标

    Args:
        folder_path: 文件夹路径

    Returns:
        导航目标
    """
    goal_path = os.path.join(folder_path, "goal.txt")
    if os.path.exists(goal_path):
        try:
            with open(goal_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            logger.error(f"读取导航目标时出错: {e}")

    return "未知目标"


def apply_action_to_location(location: np.ndarray, action: str) -> np.ndarray:
    """
    应用动作到位置坐标，计算执行动作后的新坐标

    Args:
        location: 当前位置坐标 [x, y, z, rx, ry, yaw, pitch]
        action: 动作名称

    Returns:
        执行动作后的新坐标
    """
    # 复制当前坐标，避免修改原始数据
    new_location = location.copy()

    # 获取动作参数
    action_param = COMMANDS_MAP.get(action)
    if action_param is None:
        logger.warning(f"未知动作: {action}")
        return new_location

    # 位移动作
    if isinstance(action_param, tuple) and len(action_param) == 3:
        dx, dy, dz = action_param

        # 考虑偏航角进行位移变换
        yaw = new_location[5]  # 偏航角（弧度）

        # 计算旋转后的位移
        rotated_dx = dx * math.cos(yaw) - dy * math.sin(yaw)
        rotated_dy = dx * math.sin(yaw) + dy * math.cos(yaw)

        # 应用位移
        new_location[0] += rotated_dx
        new_location[1] += rotated_dy
        new_location[2] += dz

    # 旋转动作
    elif isinstance(action_param, (int, float)):
        if action in ["向左旋转", "向右旋转"]:
            # 偏航角变化（度转弧度）
            yaw_change = (action_param / 180.0) * math.pi
            new_location[5] = (new_location[5] + yaw_change) % (2 * math.pi)
        elif action in ["向上看", "向下看"]:
            # 俯仰角变化（度转弧度）
            pitch_change = (action_param / 180.0) * math.pi
            # 限制俯仰角范围在 -π/2 到 π/2 之间
            new_location[6] = max(min(new_location[6] + pitch_change, math.pi/2), -math.pi/2)

    return new_location


def calculate_reward_1(current_loc: np.ndarray, reference_loc: np.ndarray, action_loc: np.ndarray) -> float:
    """
    计算奖励1：评估动作与参考动作的相似度

    Args:
        current_loc: 当前位置坐标
        reference_loc: 参考动作完成后的坐标
        action_loc: 待评估动作完成后的坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 计算向量差
    reference_vector = reference_loc - current_loc
    action_vector = action_loc - current_loc

    # 计算向量的模长
    ref_norm = np.linalg.norm(reference_vector)
    act_norm = np.linalg.norm(action_vector)

    # 避免除以零
    if ref_norm < 1e-6 or act_norm < 1e-6:
        return 0.0

    # 计算余弦相似度
    cos_sim = np.dot(reference_vector, action_vector) / (ref_norm * act_norm)

    # 根据余弦相似度分配奖励
    if cos_sim > 0.9:  # 几乎完全重合
        return 1.0
    elif cos_sim > 0:  # 锐角关系
        return 0.5
    elif abs(cos_sim) < 1e-6:  # 直角关系
        return 0.0
    else:  # 钝角关系
        return -1.0


def calculate_reward_2(current_loc: np.ndarray, reference_loc: np.ndarray,
                      action_loc: np.ndarray, final_loc: np.ndarray) -> float:
    """
    计算奖励2：评估动作对接近目标的贡献

    Args:
        current_loc: 当前位置坐标
        reference_loc: 参考动作完成后的坐标
        action_loc: 待评估动作完成后的坐标
        final_loc: 最终目标点的坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标（前三维）
    pos_current = current_loc[:3]
    pos_reference = reference_loc[:3]
    pos_action = action_loc[:3]
    pos_final = final_loc[:3]

    # 计算当前位置到目标的距离
    dist_current_to_final = np.linalg.norm(pos_final - pos_current)

    # 计算参考动作后位置到目标的距离
    dist_reference_to_final = np.linalg.norm(pos_final - pos_reference)

    # 计算待评估动作后位置到目标的距离
    dist_action_to_final = np.linalg.norm(pos_final - pos_action)

    # 计算距离变化
    dist_change_reference = dist_current_to_final - dist_reference_to_final
    dist_change_action = dist_current_to_final - dist_action_to_final

    # 归一化为[-1,1]区间
    # 使用高斯函数进行平滑处理
    sigma = 10.0  # 控制平滑程度
    reward = (np.exp(-(dist_action_to_final**2)/(2*sigma**2)) -
              np.exp(-(dist_current_to_final**2)/(2*sigma**2))) / 10.0

    return max(min(reward, 1.0), -1.0)


def calculate_reward_3(current_loc: np.ndarray, action_loc: np.ndarray, final_loc: np.ndarray) -> float:
    """
    计算奖励3：评估动作朝向与目标方向的一致性

    Args:
        current_loc: 当前位置坐标
        action_loc: 待评估动作完成后的坐标
        final_loc: 最终目标点的坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标（前三维）
    pos_current = current_loc[:3]
    pos_action = action_loc[:3]
    pos_final = final_loc[:3]

    # 计算当前朝向向量（由偏航角确定）
    yaw = current_loc[5]
    current_direction = np.array([math.cos(yaw), math.sin(yaw), 0])

    # 计算指向目标的方向向量
    to_final_vector = pos_final - pos_current
    to_final_vector_norm = np.linalg.norm(to_final_vector)

    # 避免除以零
    if to_final_vector_norm < 1e-6:
        return 0.0

    to_final_direction = to_final_vector / to_final_vector_norm

    # 计算动作后指向目标的方向向量
    to_final_after_action = pos_final - pos_action
    to_final_after_action_norm = np.linalg.norm(to_final_after_action)

    # 避免除以零
    if to_final_after_action_norm < 1e-6:
        return 1.0  # 已到达目标

    to_final_direction_after_action = to_final_after_action / to_final_after_action_norm

    # 计算当前朝向与目标方向的夹角
    cos_angle_current = np.dot(current_direction, to_final_direction)
    angle_current = math.acos(max(min(cos_angle_current, 1.0), -1.0)) * 180 / math.pi

    # 计算动作后朝向与目标方向的夹角
    yaw_after_action = action_loc[5]
    direction_after_action = np.array([math.cos(yaw_after_action), math.sin(yaw_after_action), 0])
    cos_angle_after = np.dot(direction_after_action, to_final_direction_after_action)
    angle_after = math.acos(max(min(cos_angle_after, 1.0), -1.0)) * 180 / math.pi

    # 计算奖励
    if angle_after < angle_current:
        # 朝向更接近目标
        return min(1.0, (angle_current - angle_after) / 45.0)
    else:
        # 朝向远离目标
        return max(-1.0, -(angle_after - angle_current) / 45.0)


def calculate_final_score(reward1: float, reward2: float, reward3: float) -> float:
    """
    计算综合评分

    Args:
        reward1: 奖励1值
        reward2: 奖励2值
        reward3: 奖励3值

    Returns:
        综合评分，范围[-1, 1]
    """
    # 如果完全击中参考动作
    if abs(reward1 - 1.0) < 1e-6:
        return 1.0

    # 如果奖励1为正值或0
    if reward1 >= 0:
        score = (reward2 + reward3) / 2
    else:
        # 如果奖励1为负值
        score = (reward1 + reward2 + reward3) / 3

    # 确保评分在[-1, 1]范围内
    return max(min(score, 1.0), -1.0)


def evaluate_all_actions(current_loc: np.ndarray, reference_loc: np.ndarray,
                         final_loc: np.ndarray) -> Dict[str, float]:
    """
    评估所有可能的动作

    Args:
        current_loc: 当前位置坐标
        reference_loc: 参考动作完成后的坐标
        final_loc: 最终目标点的坐标

    Returns:
        动作评分字典，键为动作名称，值为评分
    """
    scores = {}

    # 对每个可能的动作进行评分
    for action in ALL_ACTIONS:
        # 计算执行动作后的位置
        action_loc = apply_action_to_location(current_loc, action)

        # 计算三个奖励指标
        reward1 = calculate_reward_1(current_loc, reference_loc, action_loc)
        reward2 = calculate_reward_2(current_loc, reference_loc, action_loc, final_loc)
        reward3 = calculate_reward_3(current_loc, action_loc, final_loc)

        # 计算综合评分
        final_score = calculate_final_score(reward1, reward2, reward3)

        # 存储评分
        scores[action] = final_score

    return scores


def process_folder(folder_path: str, folder_id: str, output_path: str) -> None:
    """
    处理单个文件夹，生成问答数据和动作评分

    Args:
        folder_path: 文件夹路径
        folder_id: 文件夹ID
        output_path: 输出文件路径
    """
    logger.info(f"处理文件夹 {folder_id}")

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        logger.warning(f"文件夹不存在: {folder_path}")
        return

    # 读取导航目标
    navigation_target = read_navigation_target(folder_path)
    logger.info(f"导航目标: {navigation_target}")

    # 读取动作序列
    path_csv = os.path.join(folder_path, "path.csv")
    if not os.path.exists(path_csv):
        logger.warning(f"path.csv不存在: {path_csv}")
        return

    actions = read_actions_from_csv(path_csv)
    if not actions:
        logger.warning(f"未找到动作: {path_csv}")
        return

    # 读取位置坐标
    loc_csv = os.path.join(folder_path, "loc.csv")
    if not os.path.exists(loc_csv):
        logger.warning(f"loc.csv不存在: {loc_csv}")
        return

    locations = read_locations_from_csv(loc_csv)
    if not locations or len(locations) < len(actions) + 1:
        logger.warning(f"位置坐标数量不足: {len(locations) if locations else 0}")
        return

    # 获取最终位置坐标
    final_location = locations[-1]

    # 生成问答数据和动作评分
    qa_data = []

    # 初始图像路径
    initial_image = os.path.join(folder_path, "initial.png")
    if not os.path.exists(initial_image):
        logger.warning(f"初始图像不存在: {initial_image}")
        return

    # 处理每一步动作
    for i in range(len(actions)):
        # 当前位置和下一位置
        current_location = locations[i]
        next_location = locations[i + 1]

        # 当前图像和下一图像
        current_image = os.path.join(folder_path, f"{i}.png")

        # 检查图像是否存在
        if not os.path.exists(current_image):
            logger.warning(f"图像不存在: {current_image}")
            continue

        # 评估所有可能的动作
        action_scores = evaluate_all_actions(current_location, next_location, final_location)

        # 创建问答数据
        qa_item = {
            "folder_id": folder_id,
            "step": i,
            "navigation_target": navigation_target,
            "current_image": current_image,
            "current_location": current_location.tolist(),
            "reference_action": actions[i],
            "reference_location": next_location.tolist(),
            "final_location": final_location.tolist(),
            "action_scores": action_scores
        }

        qa_data.append(qa_item)

    # 保存问答数据
    output_file = os.path.join(output_path, f"{folder_id}_qa_data.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(qa_data, f, ensure_ascii=False, indent=2)

    logger.info(f"已保存问答数据: {output_file}")


def process_all_folders(base_path: str, output_path: str) -> None:
    """
    处理所有文件夹

    Args:
        base_path: 基础路径
        output_path: 输出路径
    """
    # 创建输出目录
    os.makedirs(output_path, exist_ok=True)

    # 检查base_path是否直接指向一个数字文件夹
    if os.path.basename(base_path).isdigit():
        logger.info(f"直接处理单个文件夹: {base_path}")
        folder_id = os.path.basename(base_path)
        process_folder(base_path, folder_id, output_path)
        logger.info(f"已处理 1 个文件夹")
        return

    # 获取所有数字文件夹
    folder_paths = get_numeric_folders(base_path)

    # 处理每个文件夹
    for folder_path in folder_paths:
        folder_id = os.path.basename(folder_path)
        process_folder(folder_path, folder_id, output_path)

    logger.info(f"已处理 {len(folder_paths)} 个文件夹")


def generate_qa_jsonl(qa_data_path: str, output_jsonl: str) -> None:
    """
    生成问答JSONL文件

    Args:
        qa_data_path: 问答数据路径
        output_jsonl: 输出JSONL文件路径
    """
    # 获取所有JSON文件
    json_files = [f for f in os.listdir(qa_data_path) if f.endswith('_qa_data.json')]

    # 排序
    json_files.sort(key=lambda x: int(x.split('_')[0]))

    # 创建JSONL文件
    with open(output_jsonl, 'w', encoding='utf-8') as out_f:
        for json_file in json_files:
            file_path = os.path.join(qa_data_path, json_file)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    qa_data = json.load(f)

                for qa_item in qa_data:
                    # 构建问答格式
                    folder_id = qa_item['folder_id']
                    step = qa_item['step']
                    navigation_target = qa_item['navigation_target']
                    current_image = qa_item['current_image']
                    reference_action = qa_item['reference_action']
                    action_scores = qa_item['action_scores']

                    # 构建问题
                    question = f"目标：{navigation_target}。请根据当前图像，选择最佳的导航动作。"

                    # 构建答案
                    answer = f"最佳动作是：{reference_action}"

                    # 添加评分信息
                    scores_text = "各动作评分：\n"
                    for action, score in sorted(action_scores.items(), key=lambda x: x[1], reverse=True):
                        scores_text += f"- {action}: {score:.4f}\n"

                    answer += f"\n\n{scores_text}"

                    # 创建JSONL条目
                    jsonl_item = {
                        "id": f"{folder_id}_{step}",
                        "image": current_image,
                        "conversations": [
                            {"role": "system", "content": SYSTEM_CONTENT},
                            {"role": "user", "content": question},
                            {"role": "assistant", "content": answer}
                        ]
                    }

                    # 写入JSONL
                    out_f.write(json.dumps(jsonl_item, ensure_ascii=False) + '\n')

            except Exception as e:
                logger.error(f"处理文件时出错: {file_path}, 错误: {e}")

    logger.info(f"已生成JSONL文件: {output_jsonl}")


def main():
    parser = argparse.ArgumentParser(description='导航数据集处理与动作评分系统')
    parser.add_argument('--base_path', type=str, default='/home/<USER>/wzy/NIPS/data/dataset_video',
                        help='基础路径，包含图像文件夹')
    parser.add_argument('--output_path', type=str, default='/home/<USER>/wzy/NIPS/data/qa_data',
                        help='输出路径，用于保存问答数据')
    parser.add_argument('--output_jsonl', type=str, default='/home/<USER>/wzy/NIPS/data/navigation_qa.jsonl',
                        help='输出JSONL文件路径')
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，显示更多日志信息')
    args = parser.parse_args()

    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # 处理所有文件夹
    process_all_folders(args.base_path, args.output_path)

    # 生成问答JSONL文件
    generate_qa_jsonl(args.output_path, args.output_jsonl)


if __name__ == '__main__':
    main()
