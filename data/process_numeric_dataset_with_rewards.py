#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导航数据集处理与动作评分系统
功能：
1. 读取导航数据集，包括位置坐标、动作序列和图像
2. 为每个可能的动作计算评分
3. 生成与process_numeric_dataset.py类似的输出格式，但添加动作评分
"""

import os
import json
import logging
import argparse
import pandas as pd
import numpy as np
import math
import re
from typing import Dict, List, Tuple, Any, Union

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 中英文动作映射表
ZH_TO_EN_ACTION_MAPPING = {
    # 简化动作映射
    "向前运动": "Move forward",
    "向后运动": "Move backward",
    "向左运动": "Move left",
    "向右运动": "Move right",
    "向上运动": "Move up",
    "向下运动": "Move down",
    "向左旋转": "Rotate left",
    "向右旋转": "Rotate right",
    "向上看": "Look up",
    "向下看": "Look down",

    # 其他可能的表述
    "向左移动": "Move left",
    "向右移动": "Move right",
    "未知动作": "Unknown action"
}

# 英文到中文动作映射表
EN_TO_ZH_ACTION_MAPPING = {
    "Move forward": "向前运动",
    "Move backward": "向后运动",
    "Move left": "向左运动",
    "Move right": "向右运动",
    "Move up": "向上运动",
    "Move down": "向下运动",
    "Rotate left": "向左旋转",
    "Rotate right": "向右旋转",
    "Look up": "向上看",
    "Look down": "向下看"
}

# 动作映射表：详细动作 -> 简化动作（中文）
ACTION_MAPPING = {
    # 简化动作映射（path.csv中可能直接使用简化表述）
    "向前运动": "向前运动",
    "向后运动": "向后运动",
    "向左移动": "向左运动",
    "向右移动": "向右运动",
    "向上运动": "向上运动",
    "向下运动": "向下运动",
    "向左旋转": "向左旋转",
    "向右旋转": "向右旋转",
    "向上看": "向上看",
    "向下看": "向下看",

    # 其他可能的表述
    "向右运动": "向右运动",
    "向左运动": "向左运动"
}

# 可合并的运动动作集合（英文）
MERGEABLE_MOVEMENTS_EN = {
    "Move forward", "Move backward", "Move up", "Move down"
}

# 系统提示内容（英文）
SYSTEM_CONTENT_EN = """A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> reasoning process here </think> \\n<answer> answer here </answer>. 

Within the <think> tag, you must use the following structured format:
<HISTORICAL CONTEXT>
Analysis of historical observations and context.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
Analysis of spatial relationships and goal orientation.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
Decomposition of navigation plan and identification of current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
Analysis of possible actions and their impacts.
</ACTION ANALYSIS>

<DECISION>
Final decision reasoning and selection.
</DECISION>

Ensure that your answer is consistent with and directly derived from your thinking process, maintaining logical coherence between the two sections. User: . Assistant:
You are a visual navigation decision system. You are receiving first-person view navigation images from a drone. The drone can perform the following actions:
- Move forward 10m
- Move backward 10m
- Move left 10m
- Move right 10m
- Move up 10m
- Move down 10m
- Rotate left 22.5 degrees
- Rotate right 22.5 degrees
- Tilt camera up 45 degrees
- Tilt camera down 45 degrees
"""

# 动作空间描述（英文，用于用户提示）
ACTION_SPACE_DESC_EN = """- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down"""

# 动作参数映射
COMMANDS_MAP = {
    "向前运动": (10, 0, 0),
    "向后运动": (-10, 0, 0),
    "向左运动": (0, -10, 0),
    "向右运动": (0, 10, 0),
    "向上运动": (0, 0, -10),
    "向下运动": (0, 0, 10),
    "向左旋转": -22.5,  # 度
    "向右旋转": 22.5,   # 度
    "向上看": 45,       # 度
    "向下看": -45       # 度
}

# 所有可能的动作列表
ALL_ACTIONS = list(COMMANDS_MAP.keys())
ALL_ACTIONS_EN = [ZH_TO_EN_ACTION_MAPPING[action] for action in ALL_ACTIONS]


def get_numeric_folders(base_path: str) -> List[str]:
    """
    获取所有数字命名的文件夹，并按数字顺序排序

    Args:
        base_path: 基础路径

    Returns:
        数字文件夹路径列表，按数字顺序排序
    """
    try:
        # 列出所有文件夹
        all_folders = [f for f in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, f))]

        # 筛选出数字命名的文件夹
        numeric_folders = []
        for folder in all_folders:
            if folder.isdigit():
                numeric_folders.append(folder)

        # 按数字顺序排序
        numeric_folders.sort(key=int)

        # 转换为完整路径
        folder_paths = [os.path.join(base_path, folder) for folder in numeric_folders]

        logger.info(f"找到 {len(folder_paths)} 个数字文件夹")
        return folder_paths
    except Exception as e:
        logger.error(f"获取数字文件夹时出错: {e}")
        return []


def read_actions_from_csv(csv_path: str) -> List[str]:
    """
    从CSV文件中读取动作序列

    Args:
        csv_path: CSV文件路径

    Returns:
        动作列表
    """
    try:
        # 首先尝试检查文件格式
        with open(csv_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            # 检查是否是TSV格式（制表符分隔）
            if '\t' in first_line:
                logger.info(f"检测到TSV格式: {csv_path}")
                # 明确指定header=None，确保第一行不被视为列名
                df = pd.read_csv(csv_path, sep='\t', header=None)
            else:
                # 明确指定header=None，确保第一行不被视为列名
                df = pd.read_csv(csv_path, header=None)

        # 检查列数
        if len(df.columns) == 1:
            # 只有一列，可能是没有标题的CSV
            logger.info(f"CSV文件只有一列: {csv_path}")
            actions = df.iloc[:, 0].astype(str).tolist()
            return actions

        # 如果有多列，尝试找到动作列
        if len(df.columns) >= 2:
            # 检查第一列是否是索引（数字递增）
            first_col = df.iloc[:, 0]
            if first_col.dtype.kind in 'iuf' and first_col.is_monotonic_increasing:
                logger.info(f"CSV文件第一列似乎是索引，使用第二列作为动作: {csv_path}")
                actions = df.iloc[:, 1].astype(str).tolist()
                return actions

        # 由于我们指定了header=None，列名现在是数字索引
        # 直接检查是否有第二列，如果有就使用第二列（动作列）
        if len(df.columns) >= 2:
            logger.info(f"CSV文件有多列，使用第二列作为动作列: {csv_path}")
            actions = df[1].astype(str).tolist()
        else:
            logger.info(f"CSV文件只有一列，使用第一列作为动作列: {csv_path}")
            actions = df[0].astype(str).tolist()

        return actions
    except Exception as e:
        logger.error(f"读取CSV文件时出错: {e}")
        return []


def read_locations_from_csv(csv_path: str) -> List[np.ndarray]:
    """
    从CSV文件中读取位置坐标

    Args:
        csv_path: CSV文件路径

    Returns:
        位置坐标列表，每个元素是一个7维numpy数组 [x, y, z, rx, ry, yaw, pitch]
    """
    try:
        # 读取整个文件内容
        with open(csv_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 使用正则表达式提取坐标
        pattern = r'(\d+),"\[(.*?)\]"'
        matches = re.findall(pattern, content, re.DOTALL)

        locations = []
        for idx, coord_str in matches:
            # 清理字符串，移除换行符和多余空格
            clean_str = re.sub(r'\s+', ' ', coord_str).strip()
            # 解析为numpy数组
            coords = np.fromstring(clean_str, sep=' ')

            # 确保是7维向量
            if len(coords) == 7:
                locations.append((int(idx), coords))
            else:
                logger.warning(f"坐标维度不正确: {len(coords)}, 值: {clean_str}")

        # 按索引排序
        locations.sort(key=lambda x: x[0])

        # 提取坐标数组
        location_arrays = [loc[1] for loc in locations]

        logger.info(f"成功读取 {len(location_arrays)} 个位置坐标")
        return location_arrays
    except Exception as e:
        logger.error(f"读取位置CSV文件时出错: {e}")
        return []


def read_navigation_target(folder_path: str) -> str:
    """
    读取导航目标

    Args:
        folder_path: 文件夹路径

    Returns:
        导航目标
    """
    goal_path = os.path.join(folder_path, "goal.txt")
    if os.path.exists(goal_path):
        try:
            with open(goal_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            logger.error(f"读取导航目标时出错: {e}")

    return "未知目标"


def check_image_exists(image_path: str) -> bool:
    """
    检查图像文件是否存在

    Args:
        image_path: 图像文件路径

    Returns:
        文件是否存在
    """
    return os.path.exists(image_path)


def apply_action_to_location(location: np.ndarray, action: str) -> np.ndarray:
    """
    应用动作到位置坐标，计算执行动作后的新坐标

    Args:
        location: 当前位置坐标 [x, y, z, rx, ry, yaw, pitch]
        action: 动作名称

    Returns:
        执行动作后的新坐标
    """
    # 复制当前坐标，避免修改原始数据
    new_location = location.copy()

    # 获取动作参数
    action_param = COMMANDS_MAP.get(action)
    if action_param is None:
        logger.warning(f"未知动作: {action}")
        return new_location

    # 位移动作
    if isinstance(action_param, tuple) and len(action_param) == 3:
        dx, dy, dz = action_param

        # 考虑偏航角进行位移变换
        yaw = new_location[5]  # 偏航角（弧度）

        # 计算旋转后的位移
        rotated_dx = dx * math.cos(yaw) - dy * math.sin(yaw)
        rotated_dy = dx * math.sin(yaw) + dy * math.cos(yaw)

        # 应用位移
        new_location[0] += rotated_dx
        new_location[1] += rotated_dy
        new_location[2] += dz

    # 旋转动作
    elif isinstance(action_param, (int, float)):
        if action in ["向左旋转", "向右旋转"]:
            # 偏航角变化（度转弧度）
            yaw_change = (action_param / 180.0) * math.pi
            new_location[5] = (new_location[5] + yaw_change) % (2 * math.pi)
        elif action in ["向上看", "向下看"]:
            # 俯仰角变化（度数）
            # new_location[6] 是度数制, action_param 也是度数制
            new_location[6] += action_param
            # 限制俯仰角范围在 -90 到 90 度之间
            new_location[6] = max(min(new_location[6], 90.0), -90.0)

    return new_location


def calculate_reward_1(current_loc: np.ndarray, reference_loc: np.ndarray, action_loc: np.ndarray,
                  reference_action: str = None, evaluated_action: str = None) -> float:
    """
    计算奖励1：评估动作与参考动作的相似度
    直接根据动作名称判断，不再进行复杂的坐标计算

    Args:
        current_loc: 当前位置坐标（为了保持接口一致，但不再使用）
        reference_loc: 参考动作完成后的坐标（为了保持接口一致，但不再使用）
        action_loc: 待评估动作完成后的坐标（为了保持接口一致，但不再使用）
        reference_action: 参考动作名称
        evaluated_action: 待评估动作名称

    Returns:
        奖励值，范围[-1, 1]
    """
    # 如果没有提供动作名称，则返回中性评分
    if reference_action is None or evaluated_action is None:
        return 0.0

    # 定义对立动作映射
    opposite_actions = {
        "向前运动": "向后运动",
        "向后运动": "向前运动",
        "向左运动": "向右运动",
        "向右运动": "向左运动",
        "向上运动": "向下运动",
        "向下运动": "向上运动",
        "向左旋转": "向右旋转",
        "向右旋转": "向左旋转",
        "向上看": "向下看",
        "向下看": "向上看"
    }

    # 如果待评估动作与参考动作完全相同
    if evaluated_action == reference_action:
        return 1.0

    # 如果待评估动作是参考动作的对立动作
    if opposite_actions.get(reference_action) == evaluated_action:
        return 0.0

    # 其他非对立动作
    return 0.0


def calculate_reward_2(current_loc: np.ndarray, reference_loc: np.ndarray,
                      action_loc: np.ndarray, final_loc: np.ndarray) -> float:
    """
    计算奖励2：评估动作对接近目标的贡献

    Args:
        current_loc: 当前位置坐标
        reference_loc: 参考动作完成后的坐标
        action_loc: 待评估动作完成后的坐标
        final_loc: 最终目标点的坐标

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标（前三维）
    pos_current = current_loc[:3]
    pos_reference = reference_loc[:3]
    pos_action = action_loc[:3]
    pos_final = final_loc[:3]

    # 计算当前位置到目标的距离
    dist_current_to_final = np.linalg.norm(pos_final - pos_current)

    # 计算参考动作后位置到目标的距离
    dist_reference_to_final = np.linalg.norm(pos_final - pos_reference)

    # 计算待评估动作后位置到目标的距离
    dist_action_to_final = np.linalg.norm(pos_final - pos_action)

    # 计算待评估动作导致的距离变化
    dist_change_action = dist_current_to_final - dist_action_to_final

    # 计算奖励值：评估动作对接近目标的贡献
    # 如果动作使得距离减小（接近目标），则给予正向奖励
    # 如果动作使得距离增加（远离目标），则给予负向奖励
    # 归一化为[-1,1]区间
    if dist_change_action > 0:  # 距离减小，接近目标
        reward = min(dist_change_action / 10.0, 1.0)  # 归一化，最大值为1
    else:  # 距离增加，远离目标
        reward = max(dist_change_action / 10.0, -1.0)  # 归一化，最小值为-1

    return reward


def calculate_reward_3(current_loc: np.ndarray, action_loc: np.ndarray, final_loc: np.ndarray) -> float:
    """
    计算奖励3：评估动作朝向与目标方向的一致性
    考虑无人机的偏航角和俯仰角共同确定的朝向向量

    Args:
        current_loc: 当前位置坐标 [x, y, z, rx, ry, yaw, pitch]
        action_loc: 待评估动作完成后的坐标 [x, y, z, rx, ry, yaw, pitch]
        final_loc: 最终目标点的坐标 [x, y, z, rx, ry, yaw, pitch]

    Returns:
        奖励值，范围[-1, 1]
    """
    # 提取位置坐标
    pos_current = current_loc[:3]
    pos_action = action_loc[:3]
    pos_final = final_loc[:3]

    # 计算B：当前朝向向量（由偏航角和俯仰角共同确定）
    yaw = current_loc[5]    # 偏航角（水平面内的旋转，弧度制）
    pitch_deg = current_loc[6]  # 俯仰角（垂直方向的旋转，度数制）

    # 将俯仰角从度数转换为弧度
    pitch_rad = (pitch_deg / 180.0) * math.pi

    # 使用球坐标系计算三维朝向向量
    # 注意：我们反转Z轴分量的符号，使其与位置坐标系统一致（Z轴向下为正）
    # x = cos(pitch) * cos(yaw)
    # y = cos(pitch) * sin(yaw)
    # z = -sin(pitch)  # 注意这里的负号，使Z轴向下为正
    current_direction = np.array([
        math.cos(pitch_rad) * math.cos(yaw),
        math.cos(pitch_rad) * math.sin(yaw),
        -math.sin(pitch_rad)  # 反转Z轴方向
    ])  # B

    # 计算E：动作后朝向向量（由动作后的偏航角和俯仰角共同确定）
    action_yaw = action_loc[5]    # 动作后的偏航角（弧度制）
    action_pitch_deg = action_loc[6]  # 动作后的俯仰角（度数制）

    # 将动作后的俯仰角从度数转换为弧度
    action_pitch_rad = (action_pitch_deg / 180.0) * math.pi

    action_direction = np.array([
        math.cos(action_pitch_rad) * math.cos(action_yaw),
        math.cos(action_pitch_rad) * math.sin(action_yaw),
        -math.sin(action_pitch_rad)  # 反转Z轴方向
    ])  # E

    # 计算C：当前位置指向目标的向量
    to_final_vector = pos_final - pos_current
    to_final_vector_norm = np.linalg.norm(to_final_vector)
    if to_final_vector_norm < 1e-6:
        return 0.0  # 已非常接近目标
    to_final_direction = to_final_vector / to_final_vector_norm  # C

    # 计算D：动作后位置指向目标的向量
    to_final_after_action = pos_final - pos_action
    to_final_after_action_norm = np.linalg.norm(to_final_after_action)
    if to_final_after_action_norm < 1e-6:
        return 1.0  # 已到达目标
    to_final_direction_after_action = to_final_after_action / to_final_after_action_norm  # D

    # 计算B和C的夹角（当前朝向与目标方向的夹角）
    cos_angle_BC = np.dot(current_direction, to_final_direction)
    angle_BC = math.acos(max(min(cos_angle_BC, 1.0), -1.0)) * 180 / math.pi

    # 计算E和D的夹角（动作后朝向与目标方向的夹角）
    cos_angle_ED = np.dot(action_direction, to_final_direction_after_action)
    angle_ED = math.acos(max(min(cos_angle_ED, 1.0), -1.0)) * 180 / math.pi

    # 根据夹角变化计算奖励
    # 如果动作后朝向与目标方向的夹角减小，则给予正向奖励
    # 如果动作后朝向与目标方向的夹角增大，则给予负向奖励
    if angle_ED < angle_BC:
        return min(1.0, (angle_BC - angle_ED) / 45.0)
    else:
        return max(-1.0, -(angle_ED - angle_BC) / 45.0)


def calculate_final_score(reward1: float, reward2: float, reward3: float) -> float:
    """
    计算综合评分

    Args:
        reward1: 奖励1值
        reward2: 奖励2值
        reward3: 奖励3值

    Returns:
        综合评分，范围[-1, 1]
    """
    # 如果完全击中参考动作
    if abs(reward1 - 1.0) < 1e-6:
        return 1.0

    # 如果奖励1为正值或0
    if reward1 >= 0:
        score = (reward2 + reward3) / 2
    else:
        # 如果奖励1为负值
        score = (reward1 + reward2 + reward3) / 3

    # 确保评分在[-1, 1]范围内
    return max(min(score, 1.0), -1.0)


def evaluate_all_actions(current_loc: np.ndarray, reference_loc: np.ndarray,
                         final_loc: np.ndarray, reference_action: str = None) -> Dict[str, float]:
    """
    评估所有可能的动作

    Args:
        current_loc: 当前位置坐标
        reference_loc: 参考动作完成后的坐标
        final_loc: 最终目标点的坐标
        reference_action: 参考动作名称

    Returns:
        动作评分字典，键为动作名称，值为评分
    """
    scores = {}

    # 对每个可能的动作进行评分
    for action in ALL_ACTIONS:
        # 计算执行动作后的位置
        action_loc = apply_action_to_location(current_loc, action)

        # 计算三个奖励指标
        reward1 = calculate_reward_1(current_loc, reference_loc, action_loc,
                                    reference_action=reference_action, evaluated_action=action)
        reward2 = calculate_reward_2(current_loc, reference_loc, action_loc, final_loc)
        reward3 = calculate_reward_3(current_loc, action_loc, final_loc)

        # 计算综合评分
        final_score = calculate_final_score(reward1, reward2, reward3)

        # 存储评分
        scores[action] = final_score

    return scores


def get_action_rewards_en(scores: Dict[str, float]) -> Dict[str, float]:
    """
    将中文动作评分转换为英文动作评分

    Args:
        scores: 中文动作评分字典

    Returns:
        英文动作评分字典
    """
    scores_en = {}
    for action_zh, score in scores.items():
        action_en = ZH_TO_EN_ACTION_MAPPING.get(action_zh, action_zh)
        scores_en[action_en] = score

    return scores_en


def merge_consecutive_movements(actions: List[str], step_images: List[str], mergeable_movements: set) -> Tuple[List[str], List[str], List[int]]:
    """
    合并连续相同的动作并跳过中间帧

    Args:
        actions: 动作列表
        step_images: 步骤图像路径列表
        mergeable_movements: 可合并的动作集合

    Returns:
        合并后的动作列表、图像路径列表和每个合并动作的计数列表
    """
    if not actions or len(actions) <= 1:
        return actions, step_images, [1] * len(actions)

    merged_actions = []
    merged_images = []
    merged_counts = []  # 记录每个动作的合并次数

    i = 0
    while i < len(actions):
        current_action = actions[i]
        count = 1  # 默认合并次数为1（单个动作）

        # 如果当前动作是可合并的运动，检查后续动作是否相同
        if current_action in mergeable_movements and i < len(actions) - 1:
            # 计算连续相同的动作数量
            while i + 1 < len(actions) and actions[i + 1] == current_action:
                i += 1
                count += 1

            # 添加对应最后一个相同动作的图像
            if i < len(step_images):
                merged_images.append(step_images[i])
        else:
            # 对于不可合并的动作，直接添加对应的图像
            if i < len(step_images):
                merged_images.append(step_images[i])

        merged_actions.append(current_action)
        merged_counts.append(count)  # 记录当前动作的合并次数
        i += 1

    logger.info(f"合并前动作数量: {len(actions)}, 合并后: {len(merged_actions)}")
    logger.info(f"合并前图像数量: {len(step_images)}, 合并后: {len(merged_images)}")
    logger.info(f"合并计数: {merged_counts}")

    return merged_actions, merged_images, merged_counts


def create_history_case(folder_id: str, navigation_target: str,
                       initial_img: str, step_images: List[str],
                       actions: List[str], simplified_actions: List[str],
                       history_index: int, merged_counts: List[int],
                       locations: List[np.ndarray], max_frames: int = 8) -> Dict:
    """
    创建带有历史记录的案例

    Args:
        folder_id: 文件夹ID
        navigation_target: 导航目标
        initial_img: 初始图像路径
        step_images: 步骤图像路径列表
        actions: 详细动作列表（不直接使用）
        simplified_actions: 简化动作列表
        history_index: 历史索引
        merged_counts: 每个合并动作的计数列表
        locations: 位置坐标列表
        max_frames: 历史观察的最大帧数，包括初始帧和当前位置帧

    Returns:
        带有历史记录的案例数据
    """
    # 获取历史图像并进行采样
    original_history_images = [initial_img] + step_images[:history_index]

    # 使用采样函数对历史图像进行采样
    sampled_history_images = sample_history_images(initial_img, step_images[:history_index], max_frames)

    # 转换历史动作和正确动作为英文
    history_actions_output = []
    for action in simplified_actions[:history_index]:
        if action in ZH_TO_EN_ACTION_MAPPING:
            history_actions_output.append(ZH_TO_EN_ACTION_MAPPING[action])
        else:
            history_actions_output.append(action)

    if simplified_actions[history_index] in ZH_TO_EN_ACTION_MAPPING:
        solution_output = ZH_TO_EN_ACTION_MAPPING[simplified_actions[history_index]]
    else:
        solution_output = simplified_actions[history_index]

    # 英文提示内容
    history_content = f"Navigation target: {navigation_target}\n\n"

    # 添加历史观察部分
    history_content += "Historical observations:\n"

    # 添加初始位置图像
    history_content += "Initial position image:\n<image>\n\n"

    # 计算需要显示的历史图像数量（不包括初始图像和当前位置图像）
    history_image_count = len(sampled_history_images) - 2  # 减去初始图像和当前位置图像

    # 添加历史步骤，支持动作合并
    if history_image_count > 0:
        for i in range(history_image_count):
            step_num = i + 1
            # 由于我们使用了采样，这里不再显示具体的动作描述
            history_content += f'Historical observation {step_num}:\n<image>\n\n'

    # 添加当前位置部分
    history_content += "Current position:\n<image>\n\n"

    # 添加导航指示
    history_content += f"""Please continue to the navigation target {navigation_target} given at the initial position. Analyze the historical observations and current position carefully.In your <think> section, follow this structured reasoning process:

<HISTORICAL CONTEXT>
   - Describe observations from current and past views, focusing on target-related objects and environmental changes.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
   - Analyze the drone's current position, orientation, visible environmental features or landmarks, and determine the spatial relationship, direction, distance, and potential obstacles to the current step's objective.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
   - Decompose the navigation plan and identify the current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
   - For each action, predict its outcome, evaluate its contribution to the current step's objective, consider risks, and assess information gain.
</ACTION ANALYSIS>

<DECISION>
   - Select the optimal action based on progress, alignment, safety, and information gain for the current step and overall goal.
</DECISION>

Your final <answer> must contain ONLY ONE of the following actions, with no additional text:
- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down
"""

    # 创建完整的导航路径动作序列
    full_action_sequence = []
    for action in simplified_actions:
        if action in ZH_TO_EN_ACTION_MAPPING:
            full_action_sequence.append(ZH_TO_EN_ACTION_MAPPING[action])
        else:
            full_action_sequence.append(action)

    # 获取当前步骤的位置坐标
    # 注意：history_index是合并后的索引，需要映射到原始位置坐标
    # 我们需要找到合并后的history_index对应的原始位置索引
    original_index = 0
    for i in range(history_index):
        original_index += merged_counts[i]

    # 确保索引不超出范围
    if original_index >= len(locations):
        original_index = len(locations) - 1

    # 当前位置坐标
    current_loc = locations[original_index]

    # 下一步位置坐标（参考动作完成后的坐标）
    next_original_index = original_index + merged_counts[history_index] if history_index < len(merged_counts) else original_index + 1
    if next_original_index >= len(locations):
        next_original_index = len(locations) - 1
    reference_loc = locations[next_original_index]

    # 最终目标位置坐标
    final_loc = locations[-1]

    logger.debug(f"历史索引: {history_index}, 原始索引: {original_index}, 下一步索引: {next_original_index}")
    logger.debug(f"当前位置: {current_loc[:3]}, 参考位置: {reference_loc[:3]}, 最终位置: {final_loc[:3]}")

    # 评估所有可能的动作
    # 获取参考动作名称
    reference_action = simplified_actions[history_index] if history_index < len(simplified_actions) else None
    scores = evaluate_all_actions(current_loc, reference_loc, final_loc, reference_action=reference_action)

    # 转换为英文动作评分
    scores_en = get_action_rewards_en(scores)

    return {
        "messages": [
            {"role": "system", "content": SYSTEM_CONTENT_EN},
            {"role": "user", "content": history_content}
        ],
        "images": sampled_history_images,  # 使用采样后的图像
        "solution": solution_output,
        "history_actions": history_actions_output,
        "folder_id": folder_id,  # 添加文件夹ID以便追踪
        "current_index": history_index,  # 添加当前在动作序列中的索引
        "action_sequence": full_action_sequence,  # 添加完整动作序列到顶层
        "action_reward": scores_en  # 添加动作评分
    }


def create_no_history_case(folder_id: str, navigation_target: str,
                          initial_img: str, solution: str,
                          all_actions: List[str], locations: List[np.ndarray],
                          merged_counts: List[int] = None) -> Dict:
    """
    创建没有历史记录的案例

    Args:
        folder_id: 文件夹ID
        navigation_target: 导航目标
        initial_img: 初始图像路径
        solution: 正确动作
        all_actions: 完整的导航路径动作列表
        locations: 位置坐标列表
        merged_counts: 每个合并动作的计数列表

    Returns:
        没有历史记录的案例数据
    """
    # 转换正确动作为英文
    if solution in ZH_TO_EN_ACTION_MAPPING:
        solution_output = ZH_TO_EN_ACTION_MAPPING[solution]
    else:
        solution_output = solution

    # 转换所有动作为英文
    full_action_sequence = []
    if all_actions:
        for action in all_actions:
            if action in ZH_TO_EN_ACTION_MAPPING:
                full_action_sequence.append(ZH_TO_EN_ACTION_MAPPING[action])
            else:
                full_action_sequence.append(action)

    # 计算初始位置的动作评分
    current_loc = locations[0]  # 初始位置

    # 下一步位置坐标（参考动作完成后的坐标）
    # 如果有合并动作，需要考虑合并后的索引
    if merged_counts and len(merged_counts) > 0:
        next_index = merged_counts[0]  # 第一个动作的合并次数
    else:
        next_index = 1

    # 确保索引不超出范围
    if next_index >= len(locations):
        next_index = len(locations) - 1

    reference_loc = locations[next_index]
    final_loc = locations[-1]  # 最终位置

    logger.debug(f"初始位置: {current_loc[:3]}, 参考位置: {reference_loc[:3]}, 最终位置: {final_loc[:3]}")

    # 评估所有可能的动作
    # 获取参考动作名称（第一个动作）
    reference_action = solution
    scores = evaluate_all_actions(current_loc, reference_loc, final_loc, reference_action=reference_action)

    # 转换为英文动作评分
    scores_en = get_action_rewards_en(scores)

    return {
        "messages": [
            {"role": "system", "content": SYSTEM_CONTENT_EN},
            {"role": "user", "content": f"""Current position image:\n<image>\nPlease continue to the navigation target {navigation_target} given at the initial position. Analyze the historical observations and current position carefully. In your <think> section, follow this structured reasoning process:

<HISTORICAL CONTEXT>
   - Describe observations from current and past views, focusing on target-related objects and environmental changes.
</HISTORICAL CONTEXT>

<SPATIAL AND GOAL ANALYSIS>
   - Analyze the drone's current position, orientation, visible environmental features or landmarks, and determine the spatial relationship, direction, distance, and potential obstacles to the current step's objective.
</SPATIAL AND GOAL ANALYSIS>

<CURRENT STEP IDENTIFICATION>
   - Decompose the navigation plan and identify the current or next step.
</CURRENT STEP IDENTIFICATION>

<ACTION ANALYSIS>
   - For each action, predict its outcome, evaluate its contribution to the current step's objective, consider risks, and assess information gain.
</ACTION ANALYSIS>

<DECISION>
   - Select the optimal action based on progress, alignment, safety, and information gain for the current step and overall goal.
</DECISION>

Your final <answer> must contain ONLY ONE of the following actions, with no additional text:
- Move forward
- Move backward
- Move left
- Move right
- Move up
- Move down
- Rotate left
- Rotate right
- Look up
- Look down"""}
        ],
        "images": [initial_img],
        "solution": solution_output,
        "folder_id": folder_id,  # 添加文件夹ID以便追踪
        "current_index": 0,  # 当前在动作序列中的索引（第一个动作）
        "action_sequence": full_action_sequence,  # 添加完整动作序列到顶层
        "action_reward": scores_en  # 添加动作评分
    }


def process_folder(folder_path: str, folder_id: str, navigation_target: str, max_actions: int = 32, max_frames: int = 8) -> List[Dict]:
    """
    处理单个文件夹

    Args:
        folder_path: 文件夹路径
        folder_id: 文件夹ID
        navigation_target: 导航目标
        max_actions: 最大动作数量，超过此限制的文件夹将被跳过
        max_frames: 历史观察的最大帧数，包括初始帧和当前位置帧

    Returns:
        从此文件夹生成的案例列表
    """
    logger.info(f"处理文件夹 {folder_id}, 导航目标: {navigation_target}")

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        logger.warning(f"文件夹不存在: {folder_path}")
        return []

    # 读取path.csv获取正确的动作
    path_csv = os.path.join(folder_path, "path.csv")
    if not os.path.exists(path_csv):
        logger.warning(f"path.csv不存在: {path_csv}")
        return []

    try:
        actions = read_actions_from_csv(path_csv)
        # 将详细动作映射为简化动作
        simplified_actions = []
        for action in actions:
            if action in ACTION_MAPPING:
                simplified_actions.append(ACTION_MAPPING[action])
            else:
                logger.warning(f"未知动作: {action}, 跳过")
                simplified_actions.append("未知动作")
    except Exception as e:
        logger.error(f"处理path.csv时出错: {e}")
        return []

    # 读取位置坐标
    loc_csv = os.path.join(folder_path, "loc.csv")
    if not os.path.exists(loc_csv):
        logger.warning(f"loc.csv不存在: {loc_csv}")
        return []

    try:
        locations = read_locations_from_csv(loc_csv)
        if not locations or len(locations) < len(actions) + 1:
            logger.warning(f"位置坐标数量不足: {len(locations) if locations else 0} < {len(actions) + 1}")
            return []
    except Exception as e:
        logger.error(f"读取位置坐标时出错: {e}")
        return []

    # 获取图像路径
    initial_img = os.path.join(folder_path, "initial.png")
    if not check_image_exists(initial_img):
        logger.warning(f"初始图像不存在: {initial_img}")
        return []

    step_images = []
    for i in range(len(actions)):
        step_img = os.path.join(folder_path, f"{i}.png")
        if check_image_exists(step_img):
            step_images.append(step_img)
        else:
            logger.warning(f"步骤图像不存在: {step_img}")
            # 如果图像缺失，我们可以选择停止处理或继续
            break

    # 确保有足够的图像
    if len(step_images) < len(actions):
        logger.warning(f"图像数量不足: {len(step_images)} < {len(actions)}")
        # 调整动作和简化动作的长度以匹配图像数量
        actions = actions[:len(step_images)]
        simplified_actions = simplified_actions[:len(step_images)]

    # 合并连续相同方向的动作
    # 首先将中文动作映射为英文动作，然后合并
    simplified_actions_en = []
    for action in simplified_actions:
        if action in ZH_TO_EN_ACTION_MAPPING:
            simplified_actions_en.append(ZH_TO_EN_ACTION_MAPPING[action])
        else:
            simplified_actions_en.append(action)

    # 使用英文动作和英文可合并动作集合进行合并
    merged_actions_en, merged_images, merged_counts = merge_consecutive_movements(simplified_actions_en, step_images, MERGEABLE_MOVEMENTS_EN)

    # 将合并后的英文动作映射回中文动作
    action_en_to_zh_map = {}
    for i, zh_action in enumerate(simplified_actions):
        if i < len(simplified_actions_en):
            en_action = simplified_actions_en[i]
            if en_action not in action_en_to_zh_map:
                action_en_to_zh_map[en_action] = []
            action_en_to_zh_map[en_action].append(zh_action)

    # 将合并后的英文动作转换回中文动作
    merged_actions = []
    for en_action in merged_actions_en:
        if en_action in action_en_to_zh_map and action_en_to_zh_map[en_action]:
            # 使用第一个匹配的中文动作
            merged_actions.append(action_en_to_zh_map[en_action][0])
        else:
            # 如果找不到匹配的中文动作，使用占位符
            merged_actions.append("未知动作")

    # 检查合并后的动作数量是否超过最大限制
    if len(merged_actions) > max_actions:
        logger.warning(f"文件夹 {folder_id} 合并后的动作数量 ({len(merged_actions)}) 超过限制 ({max_actions}), 跳过")
        return []

    # 生成案例
    cases = []

    # 没有历史记录的案例
    if len(merged_actions) > 0:
        no_history_case = create_no_history_case(
            folder_id, navigation_target,
            initial_img, merged_actions[0],
            merged_actions, locations, merged_counts
        )
        cases.append(no_history_case)

    # 带有历史记录的案例
    for i in range(1, len(merged_actions)):
        history_case = create_history_case(
            folder_id, navigation_target,
            initial_img, merged_images, actions,
            merged_actions, i, merged_counts, locations, max_frames
        )
        cases.append(history_case)

    logger.info(f"文件夹 {folder_id} 生成了 {len(cases)} 个案例")
    return cases


def process_all_folders(base_path: str, output_path: str, max_actions: int = 32, max_frames: int = 8) -> None:
    """
    处理所有文件夹

    Args:
        base_path: 基础路径
        output_path: 输出文件路径
        max_actions: 最大动作数量，超过此限制的文件夹将被跳过
        max_frames: 历史观察的最大帧数，包括初始帧和当前位置帧
    """
    all_cases = []
    processed_folders = 0
    skipped_folders = 0

    # 检查base_path是否直接指向一个数字文件夹
    if os.path.isdir(base_path) and os.path.basename(base_path).isdigit():
        logger.info(f"直接处理单个文件夹: {base_path}")
        folder_id = os.path.basename(base_path)
        navigation_target = read_navigation_target(base_path)

        if not navigation_target or navigation_target == "未知目标":
            logger.warning(f"文件夹 {folder_id} 没有导航目标，跳过")
            skipped_folders += 1
        else:
            try:
                cases = process_folder(base_path, folder_id, navigation_target, max_actions, max_frames)
                if cases:
                    all_cases.extend(cases)
                    processed_folders += 1
                else:
                    # 如果返回空列表，可能是因为动作数量超过限制
                    skipped_folders += 1
            except Exception as e:
                logger.error(f"处理文件夹 {folder_id} 时出错: {e}")
                skipped_folders += 1
    else:
        # 获取数字文件夹
        folder_paths = get_numeric_folders(base_path)

        # 处理每个文件夹
        for folder_path in folder_paths:
            folder_id = os.path.basename(folder_path)

            # 读取导航目标
            navigation_target = read_navigation_target(folder_path)
            if not navigation_target or navigation_target == "未知目标":
                logger.warning(f"文件夹 {folder_id} 没有导航目标，跳过")
                skipped_folders += 1
                continue

            try:
                cases = process_folder(folder_path, folder_id, navigation_target, max_actions, max_frames)
                if cases:
                    all_cases.extend(cases)
                    processed_folders += 1
                else:
                    # 如果返回空列表，可能是因为动作数量超过限制
                    skipped_folders += 1
            except Exception as e:
                logger.error(f"处理文件夹 {folder_id} 时出错: {e}")
                skipped_folders += 1
                continue

    # 输出处理结果
    if os.path.isdir(base_path) and os.path.basename(base_path).isdigit():
        logger.info(f"成功处理 {processed_folders} 个文件夹")
    else:
        logger.info(f"成功处理 {processed_folders}/{len(get_numeric_folders(base_path))} 个文件夹")

    logger.info(f"跳过 {skipped_folders} 个文件夹（可能是因为动作数量超过限制或其他错误）")
    logger.info(f"总共生成 {len(all_cases)} 个案例")

    # 保存为JSONL文件
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            for item in all_cases:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        logger.info(f"数据集已保存到: {output_path}")
    except Exception as e:
        logger.error(f"保存数据集时出错: {e}")
        raise


def sample_history_images(initial_img: str, history_images: List[str], max_frames: int = 8) -> List[str]:
    """
    对历史图片进行均匀采样，保留指定数量的帧

    Args:
        initial_img: 初始图像路径
        history_images: 历史图像路径列表（不包括初始图像）
        max_frames: 采样后的最大帧数（包括初始图像和当前位置图像）

    Returns:
        采样后的图像路径列表
    """
    # 如果历史图像数量加上初始图像不超过最大帧数，则直接返回所有图像
    if len(history_images) + 1 <= max_frames:
        return [initial_img] + history_images

    # 需要从历史图像中采样的帧数（减去初始图像和当前位置图像）
    sample_count = max_frames - 2

    # 当前位置图像（最后一帧）
    current_img = history_images[-1]

    # 从历史图像中均匀采样（不包括最后一帧）
    if len(history_images) > 1:
        # 计算采样间隔
        step = (len(history_images) - 1) / sample_count
        # 采样索引
        indices = [int(i * step) for i in range(sample_count)]
        # 确保索引不超出范围
        indices = [min(i, len(history_images) - 2) for i in indices]
        # 采样图像
        sampled_history = [history_images[i] for i in indices]
    else:
        sampled_history = []

    # 返回采样后的图像列表：初始图像 + 采样的历史图像 + 当前位置图像
    return [initial_img] + sampled_history + [current_img]


def main():
    parser = argparse.ArgumentParser(description='处理导航数据集并添加动作评分')
    parser.add_argument('--base_path', type=str, default='/home/<USER>/wzy/NIPS/data/training_dataset_20250525',
                        help='包含图像文件夹的基础路径')
    parser.add_argument('--output_path', type=str, default='/home/<USER>/wzy/NIPS/data/navigation_qa_instruction_5_26.jsonl',
                        help='输出文件路径')
    parser.add_argument('--max_actions', type=int, default=500,
                        help='最大动作数量，超过此限制的文件夹将被跳过（默认：50）')
    parser.add_argument('--max_frames', type=int, default=8,
                        help='历史观察的最大帧数，包括初始帧和当前位置帧（默认：8）')
    parser.add_argument('--debug', action='store_true',
                        help='启用调试模式，显示更多日志信息')
    args = parser.parse_args()

    # 如果启用调试模式，设置日志级别为DEBUG
    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("调试模式已启用")

    logger.info(f"设置最大动作数量为: {args.max_actions}")
    logger.info(f"设置最大历史帧数为: {args.max_frames}")
    logger.info(f"使用基础路径: {args.base_path}")
    logger.info(f"输出文件路径: {args.output_path}")

    # 处理数据集
    process_all_folders(args.base_path, args.output_path, args.max_actions, args.max_frames)


if __name__ == '__main__':
    main()
